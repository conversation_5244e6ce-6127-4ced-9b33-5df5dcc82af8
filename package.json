{"name": "advanced-skeleton", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --disable-host-check", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "tslint": "prettier-tslint fix '**/*.ts'"}, "private": true, "dependencies": {"@amcharts/amcharts4": "^4.9.28", "@amcharts/amcharts4-fonts": "^4.0.1", "@amcharts/amcharts4-geodata": "^4.1.15", "@amcharts/amcharts5": "^5.1.0", "@angular-devkit/build-angular": "^13.3.3", "@angular/animations": "^13.3.4", "@angular/cdk": "^13.3.5", "@angular/common": "~13.3.4", "@angular/compiler": "~13.3.4", "@angular/compiler-cli": "^13.3.4", "@angular/core": "~13.3.4", "@angular/flex-layout": "^13.0.0-beta.38", "@angular/forms": "^13.3.4", "@angular/localize": "^13.3.4", "@angular/material": "^13.3.5", "@angular/platform-browser": "~13.3.4", "@angular/platform-browser-dynamic": "~13.3.4", "@angular/router": "~13.3.4", "@auth0/angular-jwt": "^5.0.1", "@ng-bootstrap/ng-bootstrap": "^12.0.2", "@popperjs/core": "^2.11.5", "bootstrap": "^4.5.0", "core-js": "^2.5.4", "iso-3166-2": "^1.0.0", "jasmine-core": "^4.1.0", "jwt-decode": "^2.2.0", "karma-jasmine-html-reporter": "^1.7.0", "lodash": "^4.17.11", "mat-table-exporter": "^10.2.3", "maxmind": "^4.1.4", "moment-timezone": "^0.5.34", "moment-timezone-picker": "^1.1.1", "ng-click-outside": "^8.0.0", "ngx-material-timepicker": "^5.5.3", "react-bootstrap-timezone-picker": "^2.0.1", "rxjs": "^7.5.5", "tslib": "^2.0.0", "uuid": "^8.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular/cli": "~13.3.3", "@angular/language-service": "~13.3.4", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.19", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "protractor": "~7.0.0", "puppeteer": "^13.6.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "^4.6.3"}}