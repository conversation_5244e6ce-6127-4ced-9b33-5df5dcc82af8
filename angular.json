{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"advanced-skeleton": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["lodash", "core-js/modules/es.array.map", "core-js/modules/es.reflect.apply", "core-js/modules/es.map", "core-js/modules/es.reflect.get-prototype-of", "core-js/modules/es.symbol.iterator", "core-js/modules/es.array.slice", "core-js/modules/es.symbol.description", "core-js/modules/web.dom-collections.iterator", "core-js/modules/es.reflect.delete-property", "core-js/modules/es.array.iterator", "core-js/modules/es.array.fill", "core-js/modules/es.number.constructor", "core-js/modules/es.array.reverse", "core-js/modules/es.string.trim", "core-js/modules/es.array.index-of", "core-js/modules/es.string.iterator", "core-js/modules/es.string.includes", "core-js/modules/es.array.some", "core-js/modules/es.array.includes", "core-js/modules/es.array.from", "core-js/modules/es.regexp.to-string", "core-js/modules/es.reflect.construct", "core-js/modules/es.function.name", "core-js/modules/es.string.split", "core-js/modules/es.promise", "moment-timezone", "core-js/modules/es.array.reduce", "core-js/modules/es.array.every", "core-js/modules/es.array.concat", "core-js/modules/web.dom-collections.for-each", "core-js/modules/es.object.keys", "core-js/modules/es.object.get-own-property-descriptors", "core-js/modules/es.object.get-own-property-descriptor", "core-js/modules/es.array.for-each", "core-js/modules/es.array.filter", "core-js/modules/es.symbol", "core-js/modules/es.array.join", "core-js/modules/es.string.starts-with", "core-js/modules/es.string.replace", "core-js/modules/es.string.match", "core-js/modules/es.regexp.exec", "raf", "@babel/runtime/helpers/get", "@babel/runtime/helpers/toConsumableArray", "@babel/runtime/helpers/inherits", "@babel/runtime/helpers/possibleConstructorReturn", "@babel/runtime/helpers/slicedToArray", "@babel/runtime/regenerator", "polylabel", "jwt-decode", "xlsx", "core-js/modules/es.object.to-string", "pdfmake/build/pdfmake.js"], "outputPath": "dist/advanced-skeleton", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "6.5mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb"}]}, "development": {}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {}, "configurations": {"production": {"browserTarget": "advanced-skeleton:build:production"}, "development": {"browserTarget": "advanced-skeleton:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "advanced-skeleton:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}}}, "advanced-skeleton-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js"}, "configurations": {"production": {"devServerTarget": "advanced-skeleton:serve:production"}, "development": {"devServerTarget": "advanced-skeleton:serve:development"}}, "defaultConfiguration": "development"}}}}, "defaultProject": "advanced-skeleton"}