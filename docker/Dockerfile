FROM node:16 as builder

RUN mkdir /usr/src/app
WORKDIR /usr/src/app
RUN npm install -g @angular/cli 
COPY . .
RUN npm install --legacy-peer-deps
RUN npm run build

# stage 2 - Nginx
FROM nginx
COPY docker/files/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/dist /usr/share/nginx/html
EXPOSE 4200

# Définition d'un volume
VOLUME /usr/src/app

ENTRYPOINT ["nginx", "-g", "daemon off;"]
