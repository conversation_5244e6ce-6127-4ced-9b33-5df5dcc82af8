# stage 1, here goes nothing
FROM node:16 as builder
WORKDIR /logsanalyticsfront
ENV PATH /logsanalyticsfront/node_modules/.bin:$PATH
COPY package*.json ./
# https://blog.npmjs.org/post/171556855892/introducing-npm-ci-for-faster-more-reliable
RUN npm ci --only=production && \
    npm install typescript
COPY . ./
RUN npm run build
# stage 2 super simple nginx + previously "compiled" crap baked in
FROM nginx
COPY docker/files/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /logsanalyticsfront/dist /usr/share/nginx/html
EXPOSE 43755
ENTRYPOINT ["nginx", "-g", "daemon off;"]
