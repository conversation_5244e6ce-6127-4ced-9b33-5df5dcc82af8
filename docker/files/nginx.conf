
# Expires map for serving angular stuff
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch; #means no cache, as it is not a static page
    text/css                   max;
    application/javascript     max;
    application/woff2          max; # open font format
}

server {
	listen 4200;
	expires $expires;
        location /assets {
                root /usr/src/app/src;
		expires off;
		add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
	}
	location / {
		root /usr/share/nginx/html/advanced-skeleton;
		include /etc/nginx/mime.types;
		try_files $uri $uri/ /index.html;
	}
}

