# AdvancedSkeleton

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 7.2.1.

### Launch
Development :
  * yarn install
  * yarn start

Production:
  * docker-compose up --build -d

Preprod :
  Development side :
    * We now use the registry docker
    * login : sudo docker login registry-kvasir.hexaglobe.net
    * build : sudo docker build -f docker/Dockerfile . -t registry-kvasir.hexaglobe.net/hexaglobe/logsanalyticsfront:preprod
    * push : sudo docker push registry-kvasir.hexaglobe.net/hexaglobe/logsanalyticsfront:preprod
  Server side :
    * login : sudo docker login registry-kvasir.hexaglobe.net
    * pull : sudo docker pull registry-kvasir.hexaglobe.net/hexaglobe/logsanalyticsfront:preprod
    * run : sudo docker run -v "$(pwd)":/usr/src/app -d --name logsanalyticsfrontpreprod -p 4200:4200 registry-kvasir.hexaglobe.net/hexaglobe/logsanalyticsfront:preprod




### Prerequisites
* Node > 10
* angular-cli installed globally (npm install -g @angular/cli)

### Adding a new component
* ng generate component myFolder/myComponentName --skip-import
* Add the import of the component in one of the modules files, create a new module folder and/or file if necessary (src/app/modules/)
* If a module has been created, import it in app.module.ts
* Add the route of the component in app-routing.module.ts
* Add a link to the component in the navbar (src/app/layout/sidebar.component.html)
* * E.g.: 
```angular2html
 <a mat-list-item routerLink="/table" routerLinkActive="active">
    <mat-icon class="p-r15">table_chart</mat-icon>
    <span *ngIf="isExpanded" i18n="@@assets">Admin</span>
  </a>
``` 
* * Add a new icon from mat-icon if necessary (https://material.io/tools/icons/?style=baseline)

### Adding a new service
*  ng generate service myServiceName
*  Services are in src/app/services. Add a new subfolder if necessary.

### Adding a new model
* Models are in src/app/models.
* Fill free to remove the existing models, they are only here as references.

### Other tasks
* Update navbar.component.ts to handle the profile logic if available (logout, display username, display thumbnail of profile picture)
* Update profile component with relevant logic and ui.

### Internationalization
* See https://angular.io/guide/i18n
* Set the translations in the templates whenever possible.
* * E.g.: `<span class="accent-color" i18n="@@themes">Themes</span>`
* Generate the translation source file: `ng xi18n`

### Environment variables
* Environment variables should be added in environment.ts or environment.prod.ts.


## Development server

Run `ng serve` / `yarn start` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).
