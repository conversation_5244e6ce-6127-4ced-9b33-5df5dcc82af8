import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { HttpUrlEncodingCodec } from '@angular/common/http';
import { GlobalConstants } from '../../common/global-constants';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import * as am4charts from '@amcharts/amcharts4/charts';
import { DashboardComponent } from '../dashboard/dashboard.component';
import { Router, CanActivate } from '@angular/router';
import { KeyValue } from '@angular/common';
import { time } from 'console';


function hide_graph() {
  document.getElementById('graph')
      .style.display = 'none';
}

@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss']
})

export class TableComponent implements OnInit {

  AdminPrecision;
  Adminmonth;
  Year;

  extensionJSON;
  statusJSON = {};
  cache_statusJSON = {};
  bandwithJSON =  {};
  upstreamBandwithJSON = {};
  serverJSON = {};
  osFamilyJSON = {};
  uaFamilyJSON = {};

  keyDescOrder = (a: KeyValue<number,string>, b: KeyValue<number,string>): number => {
    return a.key > b.key ? -1 : (b.key > a.key ? 1 : 0);
  };

  topic = GlobalConstants.topicNginx;
  Months = ['Janvier', 'Fevrier', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Aout', 'Septembre', 'Octobre', 'Novembre', 'Decembre'];
  MonthsStart = {'0': '-01-01', '1': '-02-01', '2': '-03-01', '3': '-04-01', '4': '-05-01', '5': '-06-01', '6': '-07-01', '7': '-08-01', '8': '-09-01', '9': '-10-01', '10': '-11-01', '11': '-12-01'};
  MonthsEnd = {'0': '-01-31', '1': '-02-29', '2': '-03-31', '3': '-04-30', '4': '-05-31', '5': '-06-30', '6': '-07-31', '7': '-08-31', '8': '-09-30', '9': '-10-31', '10': '-11-30', '11': '-12-31'};


  constructor(private http: HttpClient, public router: Router) {}

  ngOnInit() {

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      let dashboard = new DashboardComponent(this.http);
      dashboard.reload();
    }
    this.getAdmin().then(result => {
      if (result == 401 || typeof result == 'number')
        this.router.navigate(['work']);
    });

    if (!localStorage.getItem('Adminyear')) {
      let currentYear = new Date().getFullYear();
      this.Year = currentYear;
      localStorage.setItem('Adminyear', currentYear.toString())
    } else
      this.Year = Number(localStorage.getItem('Adminyear'));

    if (!this.leapYear(this.Year))
      this.MonthsEnd = {'0': '-01-31', '1': '-02-28', '2': '-03-31', '3': '-04-30', '4': '-05-31', '5': '-06-30', '6': '-07-31', '7': '-08-31', '8': '-09-30', '9': '-10-31', '10': '-11-30', '11': '-12-31'};

    if (!localStorage.getItem('Adminmonth')) {
      let currentMonth = new Date().getMonth();

      if (currentMonth != 0) {
        this.Adminmonth = this.Months[currentMonth - 1];
        localStorage.setItem('Adminmonth', (currentMonth - 1).toString())
      } else {
        this.Adminmonth = this.Months[11];
        this.Year = this.Year - 1;
        localStorage.setItem('Adminyear', (parseInt(localStorage.getItem('Adminyear')) - 1).toString())
        localStorage.setItem('Adminmonth', (11).toString())
      }
    } else
      this.Adminmonth = this.Months[Number(localStorage.getItem('Adminmonth'))];


    if (!localStorage.getItem('Adminprecision'))
      localStorage.setItem('Adminprecision', 'MONTH')
    this.AdminPrecision = localStorage.getItem('Adminprecision');

    this.getConsumptionNumbers().then(res => {
      this.displayConsumptionGraph(res);
    });

    this.getMonitoring();
  }


  async getConsumptionNumbers() {

    let resultat = [];
    let new_arr = [];
    let body;
    let start = this.Year + this.MonthsStart[Number(localStorage.getItem('Adminmonth'))];
    let tmp_start = new Date(start);
    // let tz_offset = parseInt(localStorage.getItem('signDecalage') + localStorage.getItem('timeZoneDecalage')) * -1;
    // tmp_start.setHours(tmp_start.getHours() + tz_offset);
    start = tmp_start.toISOString();
    let end = this.Year + this.MonthsEnd[Number(localStorage.getItem('Adminmonth'))] + 'T23:59:00Z';
    let tmp_end = new Date(end);
    end = tmp_end.toISOString();

    body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
        .set('Request', '(select DISTINCT(service_id), SUM(sum_bytes_sent) FROM ' + this.topic +
            ' where __time >= \'' + start + '\' AND __time <= \'' + end + '\'' +
            'GROUP BY service_id ORDER BY service_id ASC LIMIT 500)')
        .set('TimeZone', localStorage.getItem('TimeZone'));

    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    //Check in init the correct select month
    let prec = localStorage.getItem('Adminmonth');
    let mth = document.getElementById(prec) as HTMLInputElement;
    if (prec != null)
      mth.checked = true;


    let res = result['data'];
    res = res.slice(1);
    let serv_tmp = res[0][0].split('-')[0];
    let conso_tmp = 0;
    for (let elem of res) {
      if (elem[0].split('-')[0] == serv_tmp) {
        serv_tmp = elem[0].split('-')[0];
        conso_tmp = conso_tmp + elem[1]
      } else {
        new_arr.push(serv_tmp + '-' + conso_tmp / 1000000000);
        serv_tmp = elem[0].split('-')[0];
        conso_tmp =  elem[1]
      }
    }

    new_arr.push(serv_tmp + '-' + conso_tmp / 1000000000);

    for (let item of new_arr) {
      let datas = item.split('-');
      resultat.push({
        'service': datas[0],
        'value': datas[1],
      });
    }

    return resultat;
  }

  displayConsumptionGraph( res)
  {
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv", am4charts.XYChart);
    chart.numberFormatter.numberFormat = "#.###'Go'";
    chart.data = res;
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "service";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
      if (target.dataItem && target.dataItem.index && 2 == 2) {
        return dy + 25;
      }
      return dy;
    });
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = "Value in Go";
    valueAxis.min = 0;
    var series = chart.series.push(new am4charts.ColumnSeries());
    categoryAxis.sortBySeries = series;
    series.dataFields.valueY = "value";
    series.dataFields.categoryX = "service";
    series.name = "value";
    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    var columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    hide_graph();
  }

  selectOnlyThis(id) {
    let dashboard = new DashboardComponent(this.http);

    for (let i = 0;i <= 11; i++)
    {
      let elem = document.getElementById(i.toString()) as HTMLInputElement;
      elem.checked = false;
    }

    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    localStorage.setItem('Adminmonth', id);
    let year = document.getElementById('year') as HTMLInputElement;
    localStorage.setItem('Adminyear', year.value);

    dashboard.reload();
  }

  async getMonitoring() {

    const extension = await this.http.get('assets/extension.txt', {responseType: 'text'}).toPromise();
    this.extensionJSON = JSON.parse(extension);

    const bandwith = await this.http.get('assets/asn_bandwith.txt', {responseType: 'text'}).toPromise();
    this.bandwithJSON = JSON.parse(bandwith);

    const upstream_bandwith = await this.http.get('assets/asn_upstream_bandwith.txt', {responseType: 'text'}).toPromise();
    this.upstreamBandwithJSON = JSON.parse(upstream_bandwith);

    const status = await this.http.get('assets/status.txt', {responseType: 'text'}).toPromise();
    this.statusJSON = JSON.parse(status);

    const osFamily = await this.http.get('assets/osfamily.txt', {responseType: 'text'}).toPromise();
    this.osFamilyJSON = JSON.parse(osFamily);

    const UAFamily = await this.http.get('assets/uafamily.txt', {responseType: 'text'}).toPromise();
    this.uaFamilyJSON = JSON.parse(UAFamily);

    const server = await this.http.get('assets/server.txt', {responseType: 'text'}).toPromise();
    this.serverJSON = JSON.parse(server);

    const cacheStatus = await this.http.get('assets/cache_status.txt', {responseType: 'text'}).toPromise();
    this.cache_statusJSON = JSON.parse(cacheStatus);

  }

  async getAdmin() {
    let result;
    try {
      result = await this.http.get(GlobalConstants.apiURL + 'user/',
          {
            headers: new HttpHeaders()
                .set('Authorization', localStorage.getItem('token'))
          },
      ).toPromise();
    } catch (e) {
      result = e.status;
    }
    return result;
  }

  leapYear(year)
  {
    return ((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0);
  }

}


