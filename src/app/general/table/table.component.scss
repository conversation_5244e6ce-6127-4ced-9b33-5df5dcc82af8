.title {
  padding-bottom: 40px;
}


.Ok {
  color: green;
}

.Warning {
  color: orange;
}

.Critical {
  color: red;
}
//.monitoring_values[value-status="OK "]:after {
//  content: "ookk";
//  color : green;
//}
//
//.monitoring_values[value-status="CRITICAL "]:after {
//  content: "bruh";
//  color : red;
//}
//
//.monitoring_values[value-status="WARNING "]:after {
//  content:"wwa";
//  color : orange;
//}



//.monitoring_values {
//  color: red;
//}

.graph_card {
  display: inline-block;
  width: 96%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

.monitoring_card {
  display: inline-block;
  margin-top: 20px;
  width: 96%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

.monitoring_title {
  text-align: center;
}

.item {
  margin-top: 10px;
}

.item_date {
  color: black;
}

#chartdiv {
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
}

.graphTitle {
  text-align: center;
  padding-top: 20px;
}


.months {
  display: inline-block;
  margin-top: 80px;
  z-index: 2;
  position: absolute;
  top: 0px;
  right: 230px;
  margin-left: auto;
  margin-right: auto;
}


.months:hover .dropdown-content {
  display: block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.container {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #2196F3;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.monthtitle {
  text-align: center;
  text-decoration: underline;
}

#year {
  width: 70px;
  text-align: center;
  font-weight: bold;
}
#graph {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
