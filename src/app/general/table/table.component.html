<mat-card>
    <h1 class="title">ADMIN STATISTICS</h1>


    <h2 class="monthtitle">{{Adminmonth}} {{Year}}</h2>
    <div class="months">
        <input id="year"  placeholder="Year" value="{{Year}}"/>
        <nav><button class="months_btn" mat-flat-button color="accent">Months</button>
            <div class="dropdown-content">
                <div class="checkboxesMonths" *ngFor="let serv of Months; let i = index">
                    <ul>
                        <label class="container">
                            <li><input type="checkbox" id="{{  i }}" class="checkboxMonth" (change)="selectOnlyThis(i)">
                                <span class="checkmark"></span>
                                {{ serv }}</li>
                        </label>
                    </ul>
                </div>
            </div>
        </nav>
    </div>



    <mat-card class="graph_card">
        <div id="chartdiv"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graph"></span>
            </div>
        </div>
        <h2 class="graphTitle">Client Consumption</h2>
    </mat-card>


    <h2 class="monitoring_title">Monitoring</h2>

    <mat-tab-group style='min-height:800px'>
        <mat-tab label="Status">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of statusJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>
                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="Extension">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of extensionJSON| keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="Cache Status">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of cache_statusJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="OS Family">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of osFamilyJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="UA Family">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of uaFamilyJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="Server">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of serverJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="Bandwith">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of bandwithJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
        <mat-tab label="Upstream Bandwith">
            <mat-card class="monitoring_card">
                <div *ngFor="let key of upstreamBandwithJSON | keyvalue: keyDescOrder">
                    <mat-card class="item">
                        <div class="item_date">{{key.key}}</div> <p [ngClass]="{
                        'Ok' : key.value.split('-')[0] == 'OK ',
                        'Warning' : key.value.split('-')[0] == 'WARNING ',
                        'Critical' : key.value.split('-')[0] == 'CRITICAL '
                        }">{{key.value}}</p>                    </mat-card>
                </div>
            </mat-card>
        </mat-tab>
    </mat-tab-group>
</mat-card>




