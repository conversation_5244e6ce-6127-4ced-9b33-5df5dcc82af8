#chartdiv {
  width: 100%;
  height: 500px;
}

#qualityEvolutionChart, #chartdivQualitySecondConsumption, #chartdivArrivalsDeparture {
  width: 100%;
  height: 700px;
}

#chartdivStacked {
  width: 100%;
  height: 500px;
}

.title {
  display: inline-block;
  margin-top: 0;
  margin-bottom: 0;
}

.precision {
  display: inline-block;
  margin-left: 20px;
  align-self: center;
}

.precision:hover .dropdown-content1 {
  display: block;
}

.dropdown-content1 {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.precision_btn1 {
  display: inline-block;
  margin-right: 20px;
}

.container1 {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

.container1:hover input ~ .checkmark {
  background-color: #2196F3;
}

.container1 input:checked ~ .checkmark {
  background-color: #2196F3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container1 input:checked ~ .checkmark:after {
  display: block;
}

.container1 .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.page {
  background-color: whitesmoke;
}

.chartdivmatcard {
  background: white;
  margin-bottom: 20px;
}

/* Change the text color of the tooltip */
::ng-deep .mat-tooltip {
  color: white !important;
}

#chartdivTime {
  width: 100%;
  height: 500px;
}

#chartdivLoad {
  margin-top: 50px;
  display: inline-block;
  width: 70%;
  height: 500px;
}

#StatusColumn {
  margin-top: 50px;
  display: inline-block;
  width: 90%;
  height: 600px;
}

.graphTitle {
  padding-top: 20px;
  text-align: center;
  padding-bottom: 50px;
}

.numbers_card {
  margin-bottom: 20px;
  background: white;
}

.numbers {
  //display: flex;
  padding-bottom: 40px;
  background: whitesmoke;
}

.nb_stat {
  width: 100%;
  text-align: center;
  display: inline-block;
}

.current_nb {
  //display: inline-block;
  margin-left: 10px;
  color: #455da5;
}

.previous_nb1 {
  margin-left: 10px;
  color: green;
  //display: inline-block;
}

.previous_nb2 {
  margin-left: 10px;
  color: red;
  //display: inline-block;
}

.graphCard {
  text-align: center;
}

mat-icon {
  color: #737373;
}

.result_device_type {
   text-align: center;
 }

.result_consp {
  text-align: center;
  padding-bottom: 20px;
}

.device:hover .dropdown-content {
  display: block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.container {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #2196F3;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}


#chartdivAudioVideoType {
  margin-right: auto;
  margin-left: auto;
  height: 500px;
  padding-bottom: 20px;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

.sub_content {
  display: inline-block;
}

.charttime {
  background-color: white;
  margin-bottom: 20px;
  margin-top: 20px;
}


.result_precision {
  text-align: center;
}

.result_timezone {
  text-align: center;
}

.result_asn {
  text-align: center;
}

.input_asn {
  display: inline-block;
  margin-left: 50px;
  width: 130px;
}

.validate_asn_btn {
  display: inline-block;
  margin-left: 20px;
}

.chooseAsn {
  z-index: 1;
  display: inline-block;
}

.device {
  display: inline-block;
  z-index: 2;
  margin-left: 30px;
}

.validate_refresh_asn {
  display: inline-block;
}

#nbViewing, #nbViewingStacked, #viewingTime, #segmentLoad, #viewingTimeSegment, #qualityEvolution, #SegmentTypeSpin, #chartdivQualitySecondConsumptionSpin, #chartdivArrivalsDepartureSpin, #chartdivStackedSpin {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
