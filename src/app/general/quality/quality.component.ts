import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import { SessionComponent } from '../session/session.component';
import { HttpUrlEncodingCodec } from '@angular/common/http';
import { DOCUMENT } from '@angular/common';
import { DashboardComponent } from '../dashboard/dashboard.component';
import{ GlobalConstants } from '../../common/global-constants';
import { NavbarComponent } from '../../layout/navbar/navbar.component';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import * as am5plugins_exporting from "@amcharts/amcharts5/plugins/exporting";
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";


function hide_nbviewing() {
  document.getElementById('nbViewing')
      .style.display = 'none';
}

function hide_nbviewingstacked() {
  document.getElementById('nbViewingStacked')
      .style.display = 'none';
}

function hide_viewingTime() {
  document.getElementById('viewingTime')
      .style.display = 'none';
}

function hide_segmentLoad() {
  document.getElementById('segmentLoad')
      .style.display = 'none';
}

function hide_qualityEvolution() {
  document.getElementById('qualityEvolution')
      .style.display = 'none';
}

function hide_segment_type_load() {
  document.getElementById('SegmentTypeSpin')
      .style.display = 'none';
}

function hide_second_consumption_quality() {
  document.getElementById('chartdivQualitySecondConsumptionSpin')
      .style.display = 'none';
}

function hide_arrivals_vs_departures() {
  document.getElementById('chartdivArrivalsDeparturesSpin')
      .style.display = 'none';
}

// function hide_viewingTimeSegment() {
//   document.getElementById('viewingTimeSegment')
//       .style.display = 'none';
// }

function hide_status_graph() {
  document.getElementById('viewingTimeSegment')
      .style.display = 'none';
}

@Component({
  selector: 'app-quality',
  templateUrl: './quality.component.html',
  styleUrls: ['./quality.component.scss']
})
export class QualityComponent implements OnInit {

  checkbox = [];
  var_to_return_tmp = '';
  services = [];
  checkboxDevice = [];
  devices = ["application", "Desktop", "Smartphone", "Smart TV", "Tablet", "Unrecognized"];
  segment_duration = [];
  service = '';
  topic = GlobalConstants.topicNginx;
  topicNginxPersonalized = GlobalConstants.topicNginxPersonalized;
  topic_viewing = GlobalConstants.topicViewing;
  topic_stream = GlobalConstants.topicViewingStream;
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'06\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  interval2 = {'HOUR': '\'2\' HOUR', 'DAY': '\'2\' DAY', 'WEEK': '\'13\' DAY', 'MONTH': '\'2\' MONTH', 'YEAR': '\'2\' YEAR'};
  precision_to_granu = {'HOUR': 'MINUTE', 'DAY': 'HOUR', 'WEEK': 'DAY', 'MONTH': 'DAY', 'YEAR': 'MONTH'};
  granu = {'CheckGranuSecond': 'SECOND', 'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
  granu_second = 1;

  timeZone = '';
  devicesSelected = [];
  device = '';
  display_device = '';
  precision_display = '';
  dash = new DashboardComponent(this.http);
  navbar = new NavbarComponent(null, this.http, null);
  asn = '';
  start;
  end;
  personalized;
  totalDiffQuality = 0;
  totalDiffStatus = 0;


  constructor(private http: HttpClient) { }

  ngOnInit(): void {

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh')
      window.location.reload();
    }

    this.personalized = localStorage.getItem('personalized');
    this.start = localStorage.getItem('start_date');
    this.end = localStorage.getItem('end_date');
    if(localStorage.getItem('ASN')) {
      this.asn = localStorage.getItem('ASN');
    }
    this.precision_display = localStorage.getItem('precision');
    if(localStorage.getItem('TimeZone')) {
      this.timeZone = localStorage.getItem('TimeZone');
    }
    if (localStorage.getItem("checkboxesDevices")) {
      this.display_device = localStorage.getItem('devices');
      let checkboxDeviceTmp = localStorage.getItem('checkboxesDevices').split(',');
      for (let nb of checkboxDeviceTmp) {
        if (nb !== "null") {
          this.checkboxDevice.push(parseInt(nb));
        }
      }
    } else if (localStorage.getItem('checkboxesDevices') === null || localStorage.getItem('checkboxesDevices') === undefined) {
      localStorage.setItem('checkboxesDevices', '0,1,2,3,4,5');

      // localStorage.setItem('devices', this.devices.toString());
      let tmpDevices = '';
      for(let elem of this.devices) {
        tmpDevices = tmpDevices + '\'' + elem + '\',';
      }
      tmpDevices = tmpDevices.slice(0, -1);
      localStorage.setItem('devices', tmpDevices);

      this.checkboxDevice = [0, 1, 2, 3, 4, 5]
    }
    this.getServices().then(res => {
      if (localStorage.getItem('precision').includes('complete')) {
        this.navbar.setLastIntervalDate(localStorage.getItem('precision'));
      }
      if (localStorage.getItem('new_selected_services') != 'initializations')
        this.service = localStorage.getItem('new_selected_services');
      this.getDiffQuality().then(arr => {
        this.displayQualityCylinder(arr);
      });
      this.getTimeByQuality().then(arrRes => {
        this.displayQualityTimeCylinder(arrRes);
      });
      this.getAvgLoad().then(resp => {
        this.segment_duration = resp;
        hide_segmentLoad()
      });
      this.getDiffStatus().then( result => {
        this.displayStatusPieChart(result);
      });
      // this.getTimeByLoad().then( result => {
      //   this.displaySegmentLoadCylinder(result);
      // });
      this.stackedColumn().then( final => {
        this.displayStackedColumnChart(final);
      });
      this.getQualityEvolutionOverTime().then( result => {
        this.displayQualityEvolutionOverTime(result);
      });
      this.getAudioVideodInfo().then( result => {
        this.displaySegmentType(result);
      });
      this.getQualitySecondConsumption().then( result => {
        this.displayQualitySecondConsumption(result);
      });
      this.getArrivalsVSDepartures().then( result => {
        this.displayArrivalsVSDepartures(result);
      });
    });
  }

  onChangedevices(i) {
    if (this.checkboxDevice.includes(i)) {
      let indexCheck = this.checkboxDevice.indexOf(i);
      this.checkboxDevice.splice(indexCheck, 1);
    } else {
      this.checkboxDevice.push(i);
    }
    this.devicesSelected = [];
    this.device = "";
    for (i of this.checkboxDevice) {
      this.devicesSelected.push(this.devices[i]);
      this.device = this.device + ', \'' + this.devices[i] + '\'';
      if (this.device.charAt(0) === ',') {
        this.device = this.device.substr(1);
      }
    }
    localStorage.setItem('devices', this.device);
    if (this.checkboxDevice?.length > 0) {
      localStorage.setItem('checkboxesDevices', this.checkboxDevice.toLocaleString());
    } else {
      localStorage.setItem('checkboxesDevices', null);
    }
  }

  async getTimeByLoad() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", AVG(\\"sum_viewing_duration_class_s\\") FROM ' + this.topic_viewing + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + 'GROUP BY \\"quality\\" ORDER BY AVG(\\"sum_viewing_duration_class_s\\") DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", AVG(\\"sum_viewing_duration_class_s\\") FROM ' + this.topic_viewing + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + 'GROUP BY \\"quality\\" ORDER BY AVG(\\"sum_viewing_duration_class_s\\") DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    let value: Array<any> = result['data'];
    if (value !== null && !value['error']) {
      value = value.slice(1);
      let jsonArr = [];
      for (let item of value) {
        jsonArr.push({
          'Quality': item[0],
          'Time': item[1]
        })
      }
      return jsonArr;
    }
  }

  async getAvgLoad() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select AVG(\\"sum_request_time\\" / \\"count\\") FROM ' + this.topicNginxPersonalized + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
              'AND service_id IN (' + this.service + ') ' + device_type + as_number + ' UNION ALL select AVG(\\"sum_request_time\\" / \\"count\\") FROM ' + this.topicNginxPersonalized +
              'where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval2[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' LIMIT 100')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select AVG(\\"sum_request_time\\" / \\"count\\") FROM ' + this.topicNginxPersonalized + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              'AND service_id IN (' + this.service + ') ' + device_type + as_number + ' UNION ALL select AVG(\\"sum_request_time\\" / \\"count\\") FROM ' + this.topicNginxPersonalized +
              'where __time >= \'' + localStorage.getItem('comparison_date') + '\' AND __time < \'' + localStorage.getItem('start_date') +  '\' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' LIMIT 100')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    let session = new SessionComponent(this.http)
    result['data'][1] = Math.round(result['data'][1] * 1000) / 1000;
    result['data'][2] = Math.round(result['data'][2] * 1000) / 1000;
    result['data'][0] = session.getDifference(result['data']);
    if (result['data'][2] == undefined) {
      result['data'][0] = "No previous data";
    }
    if (result['data'][1] == null) {
      result['data'][1] = "No data available";
    }
    return (result['data']);
  }

  async getDiffStatus() {

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DISTINCT(\\"status\\"), count(DISTINCT(remote_addr)) from ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY status ORDER BY count(DISTINCT(remote_addr))  DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DISTINCT(\\"status\\"), count(DISTINCT(remote_addr))  from ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\'' + ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +' AND service_id IN (' + this.service + ') GROUP BY status ORDER BY count(DISTINCT(remote_addr))  DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for(let item of result['data']) {
      if (!isNaN(item[1]) && item[0] != '') {
        this.totalDiffStatus = this.totalDiffStatus + Number(item[1]);
      }
    }

    let jsonArr = [];
    let arr = result['data'].slice(1);
    for(let item of arr) {
      if(item[1] !== 0) {
        jsonArr.push({
          'Status': item[0].toString(),
          'Number': (item[1] * 100) / this.totalDiffStatus
        })
      }
    }
    return jsonArr;

  }

  async getTimeByQuality() {
    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", AVG(\\"sum_viewing_duration_class_s\\") FROM ' + this.topic_viewing + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP '+
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"quality\\" ORDER BY AVG(\\"sum_viewing_duration_class_s\\") DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", AVG(\\"sum_viewing_duration_class_s\\") FROM ' + this.topic_viewing + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"quality\\" ORDER BY AVG(\\"sum_viewing_duration_class_s\\") DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    let elem = [];
    if (localStorage.getItem('checkboxes')) {
      elem = localStorage.getItem('checkboxes').split(',');
      for (let id of elem) {
        let value = "check" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    }
    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }
    let value: Array<any> = result['data'];
    if (value !== null && !value['error']) {
      value = value.slice(1);
      let jsonArr = [];
      for (let item of value) {
        jsonArr.push({
          'Quality': item[0],
          'Time': item[1]
        })
      }
      return jsonArr;
    }
  }

  async getDiffQuality() {
    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", count(DISTINCT(remote_addr)) FROM ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')]+ ' AND CURRENT_TIMESTAMP ' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"quality\\" ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"quality\\", count(DISTINCT(remote_addr)) FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"quality\\" ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();


    let elem = [];
    if (localStorage.getItem('checkboxes')) {
      elem = localStorage.getItem('checkboxes').split(',');
      for (let id of elem) {
        let value = "check" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    }

    let elemD = [];
    if (localStorage.getItem('checkboxesDevices')) {
      elemD = localStorage.getItem('checkboxesDevices').split(',');
      for (let id of elemD) {
        let value = "checkDevice" + id;
        let elemD2 = document.getElementById(value) as HTMLInputElement;
        if (elemD2 !== null) {
          elemD2.checked = true;
        }
      }
    }


    //CHECKED AT LOADING THE NEW SREVICES CHECKBOXES
    let elem1 = [];
    if (localStorage.getItem('new_selected_services_checkboxes')) {
      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    } else {
      let value = "select-all";
      let elem2 = document.getElementById(value) as HTMLInputElement;
      if (elem2 !== null) {
        elem2.checked = true;
      }

      let check = [];
      let services = '';
      for(let i = 1; i<= this.services.length; i++) {
        check.push(i);
        services = services +  '\'' + this.services[i-1] + '\',';
      }
      localStorage.setItem('new_selected_services_checkboxes', check.toString());
      localStorage.removeItem('new_selected_services');
      localStorage.setItem('new_selected_services', services.slice(0, -1));

      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    }
    //END NEW CHECKED

    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }

    for(let item of result['data']) {
      if (!isNaN(item[1]) && item[0] != '') {
        this.totalDiffQuality = this.totalDiffQuality + Number(item[1]);
      }
    }

    let value: Array<any> = result['data'];
    if (value !== null && !value['error']) {
      value = value.slice(1);
      let jsonArr = [];
      for (let item of value) {
        if (item[0] != '') {
          jsonArr.push({
            'Quality': item[0],
            'Number': (item[1] * 100) / this.totalDiffQuality
          })
        }
      }
      return jsonArr;
    }
  }


  async getQualityEvolutionOverTime() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
    if (granularity == null) {
      granularity = 'HOUR';
      localStorage.setItem('granularity', 'HOUR');
    }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), quality, count(DISTINCT(remote_addr)), sum(sum_bytes_sent) ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + 'group by DATE_TRUNC(\'' + granularity + '\', __time), quality ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), quality, count(DISTINCT(remote_addr)), sum(sum_bytes_sent) ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + 'group by DATE_TRUNC(\'' + granularity + '\', __time), quality ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];

    value = value.slice(1);
    // Create a Map to group by date
    const groupedData = new Map<string, any>();
    value.forEach(([date, id, value, conso]) => {
        date = date.substring(0, 23) // Remove timezone info, already calculated in Druid
        const date2 = new Date(date);
        const millis = (date2.getTime()).toString();
        if (!groupedData.has(millis)) {
            groupedData.set(millis, { date: Number(millis) });
        }
        groupedData.get(millis)[id] = value;
        if (!groupedData.get(millis)['consumption'])
          groupedData.get(millis)['consumption'] = conso / 1000000000;
        else
          groupedData.get(millis)['consumption'] += conso / 1000000000;
      });
    const result = Array.from(groupedData.values());
    return result;
  }


  displayQualityEvolutionOverTime(result) {

    hide_qualityEvolution();
    let root = am5.Root.new("qualityEvolutionChart");
    root.setThemes([am5themes_Animated.new(root)]);
    let chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      paddingLeft: 0,
      layout: root.verticalLayout,
      pinchZoomX:true
    }));

    let cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    let unit;
    let count = 1;
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "day"
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "hour"
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "minute"
    } else if (localStorage.getItem('granularity') == 'SECOND' && (localStorage.getItem('precision') == 'Personalized')) {
      unit = "second"
      count = this.granu_second;
    } else {
      unit = "day"
    }

    let xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      // maxDeviation: 0,
      baseInterval: {
        timeUnit: unit,
        count: count
      },
      renderer: am5xy.AxisRendererX.new(root, {
        // minGridDistance: 200,
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      min: 0,
      max: 100,
      calculateTotals: true,
      numberFormat: "#'%'",
      renderer: am5xy.AxisRendererY.new(root, {
        pan:"zoom"
      })
    }));

    var volumeAxisRenderer = am5xy.AxisRendererY.new(root, {
     pan: "zoom"
    });
    volumeAxisRenderer.labels.template.setAll({
     centerY: am5.percent(100),
     maxPosition: 0.98
    });
    var volumeAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
     renderer: volumeAxisRenderer,
     height: am5.percent(30),
     layer: 5,
     numberFormat: "#a"
    }));
    volumeAxis.axisHeader.set("paddingTop", 10);
    volumeAxis.axisHeader.children.push(am5.Label.new(root, {
     text: "Consumption (Go)",
     fontWeight: "bold",
     paddingTop: 5,
     paddingBottom: 5
    }));


    var firstColor = chart.get("colors").getIndex(0);

    var volumeSeries = chart.series.push(am5xy.ColumnSeries.new(root, {
     clustered: false,
     fill: firstColor,
     stroke: firstColor,
     valueYField: "consumption",
     valueXField: "date",
     valueYGrouped: "sum",
     xAxis: xAxis,
     yAxis: volumeAxis,
     legendValueText: "{valueY.formatNumber('#.00')} Go",
     tooltip: am5.Tooltip.new(root, {
       labelText: "[bold]Consumption :[\] {valueY.formatNumber('#.00')} Go"
     })
    }));

    chart.leftAxesContainer.set("layout", root.verticalLayout);


    var volumeLegend = volumeAxis.axisHeader.children.push(
     am5.Legend.new(root, {
       useDefaultMarker: true
     })
    );
    volumeLegend.data.setAll([volumeSeries]);
    volumeSeries.data.setAll(result);

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      dataSource: result,
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnaltyicsUserPerQualityEvolutionOverTime"
    });

    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Evolution of number users per quality over time - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function createSeries(name, field) {
      let series = chart.series.push(am5xy.SmoothedXLineSeries.new(root, {
        name: name,
        stacked: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: field,
        valueXField: "date",
        valueYShow: "valueYTotalPercent",
        legendValueText: "{valueY}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "[bold]{name}[/] : {valueYTotalPercent.formatNumber('#.0')}% ({valueY})"
        })
      }));

      series.fills.template.setAll({
        fillOpacity: 0.5,
        visible: true
      });

      series.data.setAll(result);
      series.appear(1000);
    }

    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of result) {
      for(let i in elem) {
        if(!datas.includes(i) && i != 'date') {
          datas.push(i)
        }
      }
    }
    for (let elem of datas) {
      if (elem != 'consumption') {
        createSeries(elem, elem);
      }
    }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    let legend = chart.children.push(am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50
    }));
    legend.data.setAll(chart.series.values);
    chart.appear(1000, 100);
  }


  async stackedColumn() {
    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"device_type\\", \\"quality\\", count(DISTINCT(remote_addr)) FROM ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"device_type\\", \\"quality\\" ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"device_type\\", \\"quality\\", count(DISTINCT(remote_addr)) FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              ' AND service_id IN (' + this.service + ') ' + device_type + as_number + ' GROUP BY \\"device_type\\", \\"quality\\" ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 100)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    let data = result['data'];
    data = data.slice(1);

    let arr2 = [];
    for (let elem of data) {
      if(arr2.indexOf(elem[1]) === -1) {
        arr2.push(elem[1]);
      }
    }

    let final = [];
    for (let qual of arr2) {
      for (let elem of data) {
        if (elem[1] === qual) {
          if (qual != '') {
            final.push({
              'quality': qual,
              [elem[0]]: elem[2]
            })
          }
        }
      }
    }
    return final;
  }

  selectASN(value) {
    localStorage.setItem('ASN', value);
    window.location.reload();
  }


  async getAudioVideodInfo() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
    if (granularity == null) {
      granularity = 'HOUR';
      localStorage.setItem('granularity', 'HOUR');
    }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), segment_type, count(*) ' +
          'FROM ' + this.topicNginxPersonalized + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          'AND service_id IN ('+ this.service +') and segment_type != \'\' ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time), segment_type ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), segment_type, count(*) ' +
          'FROM ' + this.topicNginxPersonalized + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
          'AND service_id IN ('+ this.service +') and segment_type != \'\' ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time), segment_type ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    // Create a Map to group by date
    const groupedData = new Map<string, any>();
    value.forEach(([date, id, value]) => {
        date = date.substring(0, 23) // Remove timezone info, already calculated in Druid
        const date2 = new Date(date);
        const millis = (date2.getTime()).toString();
        if (!groupedData.has(millis)) {
            groupedData.set(millis, { date: Number(millis) });
        }
        groupedData.get(millis)[id] = value;
      });
    const result = Array.from(groupedData.values());
    return result;

  }


  displaySegmentType(result) {
    hide_segment_type_load();
    let root = am5.Root.new("chartdivAudioVideoType");
    root.setThemes([am5themes_Animated.new(root)]);
    let chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      paddingLeft: 0,
      layout: root.verticalLayout,
      pinchZoomX:true
    }));

    let cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    let unit;
    let count = 1;
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "day"
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "hour"
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "minute"
    } else if (localStorage.getItem('granularity') == 'SECOND' && (localStorage.getItem('precision') == 'Personalized')) {
      unit = "second"
      count = this.granu_second;
    } else {
      unit = "day"
    }

    let xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      // maxDeviation: 0,
      baseInterval: {
        timeUnit: unit,
        count: count
      },
      renderer: am5xy.AxisRendererX.new(root, {
        // minGridDistance: 200,
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      min: 0,
      max: 100,
      calculateTotals: true,
      numberFormat: "#'%'",
      renderer: am5xy.AxisRendererY.new(root, {
        pan:"zoom"
      })
    }));

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      dataSource: result,
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnaltyicsLogsPerSegmentType"
    });

    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Evolution segments downloaded by segment type - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function createSeries(name, field) {
      let series = chart.series.push(am5xy.SmoothedXLineSeries.new(root, {
        name: name,
        stacked: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: field,
        valueXField: "date",
        valueYShow: "valueYTotalPercent",
        legendValueText: "{valueY}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "[bold]{name}[/] : {valueYTotalPercent.formatNumber('#.0')}% ({valueY})"
        })
      }));

      series.fills.template.setAll({
        fillOpacity: 0.5,
        visible: true
      });

      series.data.setAll(result);
      series.appear(1000);
    }

    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of result) {
      for(let i in elem) {
        if(!datas.includes(i) && i != 'date') {
          datas.push(i)
        }
      }
    }
    for (let elem of datas) {
      createSeries(elem, elem);
    }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    let legend = chart.children.push(am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50
    }));
    legend.data.setAll(chart.series.values);
    chart.appear(1000, 100);

  }


  async getArrivalsVSDepartures() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
    if (granularity == null) {
      granularity = 'HOUR';
      localStorage.setItem('granularity', 'HOUR');
    }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), count(*) ' +
          'FROM ' + this.topic_stream + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), count(*) ' +
          'FROM ' + this.topic_stream + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1);

    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop))), count(*) ' +
          'FROM ' + this.topic_stream + ' where  MILLIS_TO_TIMESTAMP(viewing_stop) BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop)) ORDER BY DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop)) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop))), count(*) ' +
          'FROM ' + this.topic_stream + ' where MILLIS_TO_TIMESTAMP(viewing_stop) >= \'' + localStorage.getItem('start_date') + '\' AND MILLIS_TO_TIMESTAMP(viewing_stop) <= \'' + localStorage.getItem('end_date') +  '\'' +
          'AND service_id IN ('+ this.service +') ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop)) ORDER BY DATE_TRUNC(\'' + granularity + '\', MILLIS_TO_TIMESTAMP(viewing_stop)) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultatBis = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let valueBis: Array<any> = resultatBis['data'];
    valueBis = valueBis.slice(1);

    let arrivals = new Map(value)
    let departures = new Map(valueBis)
    const allTimes = new Set([...arrivals.keys(), ...departures.keys()]);
    let result = [];

    allTimes.forEach(time => {
      const value1 = arrivals.get(time) || 0; // Get value from arrivals or 0 if key is not found
      const value2 = departures.get(time) || 0; // Get value from departures or 0 if key is not found
      result.push([time, value1, value2]);
    });

    // Create a Map to group by date
    const groupedData = new Map<string, any>();
    result.forEach(([date, arrivals, departures]) => {
      date = date.substring(0, 23) // Remove timezone info, already calculated in Druid
      const date2 = new Date(date);
      const millis = (date2.getTime()).toString();
      if (!groupedData.has(millis)) {
          groupedData.set(millis, { date: Number(millis) });
      }
      groupedData.get(millis)['arrivals'] = arrivals;
      groupedData.get(millis)['departures'] = departures;
    });
    const res = Array.from(groupedData.values());

    return res;
  }


  displayArrivalsVSDepartures(result) {

    hide_arrivals_vs_departures();
    let root = am5.Root.new("chartdivArrivalsDeparture");
    root.setThemes([am5themes_Animated.new(root)]);
    let chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      paddingLeft: 0,
      layout: root.verticalLayout,
      pinchZoomX:true
    }));

    let cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    let unit;
    let count = 1;
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "day"
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "hour"
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "minute"
    } else if (localStorage.getItem('granularity') == 'SECOND' && (localStorage.getItem('precision') == 'Personalized')) {
      unit = "second"
      count = this.granu_second;
    } else {
      unit = "day"
    }

    let xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      // maxDeviation: 0,
      baseInterval: {
        timeUnit: unit,
        count: count
      },
      renderer: am5xy.AxisRendererX.new(root, {
        // minGridDistance: 200,
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      // min: 0,
      // max: 100,
      calculateTotals: true,
      numberFormat: "#",
      renderer: am5xy.AxisRendererY.new(root, {
        pan:"zoom"
      })
    }));

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      dataSource: result,
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnaltyicsArrivalsVSDepartures"
    });

    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Nb users arriving and leaving through time - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function createSeries(name, field) {
      let series = chart.series.push(am5xy.SmoothedXLineSeries.new(root, {
        name: name,
        // stacked: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: field,
        valueXField: "date",
        legendValueText: "{valueY.formatNumber('#,###.')}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "[bold]{name}[/] : ({valueY.formatNumber('#,###.')})"
        })
      }));

      series.data.setAll(result);
      series.appear(1000);
    }

    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of result) {
      for(let i in elem) {
        if(!datas.includes(i) && i != 'date') {
          datas.push(i)
        }
      }
    }
    for (let elem of datas) {
      createSeries(elem, elem);
    }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    let legend = chart.children.push(am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50
    }));
    legend.data.setAll(chart.series.values);
    chart.appear(1000, 100);

  }


  async getQualitySecondConsumption() {

    let device_type = '';
    if (localStorage.getItem('devices')) {
      device_type = "AND device_type IN (" + localStorage.getItem('devices') + ") "
    }

    let as_number = '';
    if (localStorage.getItem('ASN')) {
      as_number = "AND asn = \'" + localStorage.getItem('ASN') + '\''
    }

    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
    if (granularity == null) {
      granularity = 'HOUR';
      localStorage.setItem('granularity', 'HOUR');
    }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), quality, sum(sum_duration_s) ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          'AND service_id IN ('+ this.service +') and quality != \'\' ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time), quality ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select (DATE_TRUNC(\'' + granularity + '\', __time)), quality, sum(sum_duration_s) ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
          'AND service_id IN ('+ this.service +') and quality != \'\' ' + device_type + as_number + ' group by DATE_TRUNC(\'' + granularity + '\', __time), quality ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    // Create a Map to group by date
    const groupedData = new Map<string, any>();
    value.forEach(([date, id, value]) => {
        date = date.substring(0, 23) // Remove timezone info, already calculated in Druid
        const date2 = new Date(date);
        const millis = (date2.getTime()).toString();
        if (!groupedData.has(millis)) {
            groupedData.set(millis, { date: Number(millis) });
        }
        groupedData.get(millis)[id] = value;
      });
    const result = Array.from(groupedData.values());
    return result;

  }


  displayQualitySecondConsumption(result) {

    hide_second_consumption_quality();
    let root = am5.Root.new("chartdivQualitySecondConsumption");
    root.setThemes([am5themes_Animated.new(root)]);
    let chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      paddingLeft: 0,
      layout: root.verticalLayout,
      pinchZoomX:true
    }));

    let cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    let unit;
    let count = 1;
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "day"
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "hour"
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      unit = "minute"
    } else if (localStorage.getItem('granularity') == 'SECOND' && (localStorage.getItem('precision') == 'Personalized')) {
      unit = "second"
      count = this.granu_second;
    } else {
      unit = "day"
    }

    let xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      // maxDeviation: 0,
      baseInterval: {
        timeUnit: unit,
        count: count
      },
      renderer: am5xy.AxisRendererX.new(root, {
        // minGridDistance: 200,
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    let yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      min: 0,
      max: 100,
      calculateTotals: true,
      numberFormat: "#'%'",
      renderer: am5xy.AxisRendererY.new(root, {
        pan:"zoom"
      })
    }));

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      dataSource: result,
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnaltyicsSecondsConsumptionByQuality"
    });

    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Evolution quality time (seconds) consumptions - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function createSeries(name, field) {
      let series = chart.series.push(am5xy.SmoothedXLineSeries.new(root, {
        name: name,
        stacked: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: field,
        valueXField: "date",
        valueYShow: "valueYTotalPercent",
        legendValueText: "{valueY.formatNumber('#,###.')}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "[bold]{name}[/] : {valueYTotalPercent.formatNumber('#.0')}% ({valueY.formatNumber('#,###.')} s)"
        })
      }));

      series.fills.template.setAll({
        fillOpacity: 0.5,
        visible: true
      });

      series.data.setAll(result);
      series.appear(1000);
    }

    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of result) {
      for(let i in elem) {
        if(!datas.includes(i) && i != 'date') {
          datas.push(i)
        }
      }
    }
    for (let elem of datas) {
      createSeries(elem, elem);
    }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    let legend = chart.children.push(am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50
    }));
    legend.data.setAll(chart.series.values);
    chart.appear(1000, 100);

  }


  displayStatusPieChart(result) {
    hide_status_graph();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("StatusColumn", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = result;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsStatus";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    chart.numberFormatter.numberFormat = "#.##'%'";

    let title = 'Different Status (by users) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Status";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Number";
    series.dataFields.categoryX = "Status";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayStackedColumnChart(final) {
    hide_nbviewingstacked();

    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivStacked", am4charts.XYChart);
    chart.data = final;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsQualityDevices";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Number of users per quality (Group by devices) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "quality";
    categoryAxis.title.text = "Qualities";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 20;
    categoryAxis.renderer.cellStartLocation = 0.1;
    categoryAxis.renderer.cellEndLocation = 0.9;

    var  valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.min = 0;
    valueAxis.title.text = "Numbers";

    function createSeries(field, name, stacked) {
      var series = chart.series.push(new am4charts.ColumnSeries());
      chart.scrollbarX = new am4core.Scrollbar();
      series.dataFields.valueY = field;
      series.dataFields.categoryX = "quality";
      series.name = name;
      series.columns.template.tooltipText = "{name}: [bold]{valueY}[/]";
      series.stacked = stacked;
      series.columns.template.width = am4core.percent(95);

      var hoverState = series.columns.template.column.states.create("hover");
      hoverState.properties.cornerRadiusTopLeft = 0;
      hoverState.properties.cornerRadiusTopRight = 0;
      hoverState.properties.fillOpacity = 1;

      chart.cursor = new am4charts.XYCursor();
    }

    createSeries("application", "application", false);
    createSeries("Desktop", "Desktop", false);
    createSeries("Smartphone", "Smartphone", false);
    createSeries("Tablet", "Tablet", false);
    createSeries("Smart TV", "Smart TV", false);
    createSeries("Unrecognized", "Unrecognized", true);

    chart.legend = new am4charts.Legend();
  }

  displaySegmentLoadCylinder(arr) {
    // hide_viewingTimeSegment()
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivLoad", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalytics";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Segment Load - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Quality";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Time";
    series.dataFields.categoryX = "Quality";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayQualityTimeCylinder(arr) {
    hide_viewingTime()
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivTime", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsViewingDurationQuality";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    chart.numberFormatter.numberFormat = "#'s'";

    let title = 'Viewing Time (seconds) per Quality - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Quality";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Time";
    series.dataFields.categoryX = "Quality";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayQualityCylinder(arr) {
    hide_nbviewing()
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsersPerQuality";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    chart.numberFormatter.numberFormat = "#.##'%'";

    let title = 'Share of users per quality (All) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Quality";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Number";
    series.dataFields.categoryX = "Quality";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  async getServices() {

    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
      // if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.ngOnInit()
    }

    // CHECKED THE CHECKBOXES GRANULARITY
    let elemprec1;
    let prec1 = localStorage.getItem('granularity');
    for (let key in this.granu) {
      if (this.granu[key] == prec1) {
        elemprec1 = key;
      }
    }

    if (elemprec1 == 'CheckGranuSecond') {
      if (localStorage.getItem('secondGranularityDetails')) {
        elemprec1 = 'CheckGranuSecond-' + localStorage.getItem('secondGranularityDetails');
      } else {
        elemprec1 = 'CheckGranuSecond-1';
        localStorage.setItem('secondGranularityDetails', '1');
      }
      this.granu_second = Number(localStorage.getItem('secondGranularityDetails'));
    }

    let granu1 = document.getElementById(elemprec1) as HTMLInputElement;
    if (granu1 !== null) {
      granu1.checked = true;
    }
    // CHECKED THE CHECKBOXES GRANULARITY

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh')
      window.location.reload();
    }
  }

  selectGranu(id) {

    let elemSecond = ['CheckGranuSecond-1', 'CheckGranuSecond-6', 'CheckGranuSecond-30', 'CheckGranu0', 'CheckGranu1', 'CheckGranu2', 'CheckGranu3', 'CheckGranu4'];
    for (let element of elemSecond) {
      let elem = document.getElementById(element) as HTMLInputElement;
      elem.checked = false;
    }

    if (id.split('-')[0] == 'CheckGranuSecond') {
      this.granu_second = Number(id.split('-')[1]);
      localStorage.setItem('secondGranularityDetails', id.split('-')[1]);
      // id = "CheckGranuSecond";
    }

    let s = new Date(localStorage.getItem('start_date'));
    let e = new Date(localStorage.getItem('end_date'));
    var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date

    if (this.granu[id] === 'HOUR' && (diff > 7*24)) {
      alert("You cannot choose this granularity");
      id = "CheckGranu2";
    }
    if (this.granu[id] === 'MINUTE' && (diff > 24)) {
      alert("You cannot choose this granularity, reduce hours interval");
      id = "CheckGranu2";
    }
    if (this.granu[id.split('-')[0]] === 'SECOND' && (diff > 12)) {
      alert("You cannot choose this granularity, reduce hours interval");
    }

    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    id = this.granu[id.split('-')[0]];
    localStorage.setItem('granularity', id);
    window.location.reload();
  }

  validate() {
    this.ngOnInit();
  }
}
