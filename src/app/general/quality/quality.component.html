<div class="d-flex justify-content-center">
    <div class="spinner-border" role="status" >
        <span class="sr-only" id="loading"></span>
    </div>
</div>

<mat-card class="page">
    <div class="selector">
        <h1 class="title">QUALITY</h1>
        <div class="chooseAsn">
            <mat-form-field class="input_asn">
                <mat-label id="labelASN">A.S.N (optional)</mat-label>
                <input type="number" matInput id="inputASN" #inputASN>
            </mat-form-field>
        </div>
        <div class="device">
            <nav><button class="device_btn" mat-stroked-button color="accent">Devices
                <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
            </button>
                <div class="dropdown-content">
                    <div class="checkboxesDevice" *ngFor="let serv of devices; let i = index">
                        <ul>
                            <label class="container">
                                <li><input type="checkbox" id="{{ 'checkDevice' + i }}" class="checkboxDevice" (change)="onChangedevices(i)">
                                    <span class="checkmark"></span>
                                    {{ serv }}</li>
                            </label>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
        <div class="validate_refresh_asn" *ngIf="asn === ''; else elseblock">
            <button class="validate_asn_btn" mat-flat-button color="accent" (click)="selectASN(inputASN.value)">Validate</button>
        </div>
        <ng-template #elseblock>
            <button class="validate_asn_btn" mat-flat-button color="accent" (click)="selectASN(inputASN.value)">Refresh</button>
        </ng-template>

        <div class="precision">
            <div *ngIf="personalized === '1' || start != null">
                <nav><button class="precision_btn1" mat-stroked-button color="accent">Granularity
                    <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                </button>
                    <div class="dropdown-content1" >
                        <ul>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranuSecond-1" value="Value1" (click)="selectGranu('CheckGranuSecond-1')"/>1 Second
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranuSecond-6" value="Value1" (click)="selectGranu('CheckGranuSecond-6')"/>6 Seconds
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranuSecond-30" value="Value1" (click)="selectGranu('CheckGranuSecond-30')"/>30 Seconds
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu0" value="Value1" (click)="selectGranu('CheckGranu0')"/>Minute
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu1" value="Value1" (click)="selectGranu('CheckGranu1')"/>Hour
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu2" value="Value1" (click)="selectGranu('CheckGranu2')"/>Day
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu3" value="Value1" (click)="selectGranu('CheckGranu3')"/>Month
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu4" value="Value1" (click)="selectGranu('CheckGranu4')"/>Year
                                <span class="checkmark"></span></label></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>

    </div>
    <h4>Number of services selected : {{service.split(',').length}}</h4>
    <section class="result_asn">
        <div *ngIf="(asn !== '')"><h2 class="subtitle">ASN : </h2><h2 class="sub_content"> {{ asn }}</h2></div>
    </section>
    <section class="result_device_type">
        <div *ngIf="(display_device !== '')"><h2 class="subtitle">Devices Type :</h2><h2 class="sub_content"> {{ display_device.replaceAll('\'', '').replaceAll(',', ', ') }}</h2></div>
    </section>
    <section class="result_consp">
        <!--        <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle">Service(s) : </h2><h2 class="sub_content"> {{ service }}</h2></div>-->
        <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle"></h2><h2 class="sub_content"></h2></div>
        <ng-template class="elseBlock" #elseBlock1><mat-error></mat-error></ng-template>
    </section>
    <mat-card class="chartdivmatcard">
        <mat-card-content>
            <div id="chartdiv"></div>
            <h2 class="graphTitle">Share of users per quality (All) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of users filter by quality used." [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="nbViewing"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="chartdivmatcard">
        <mat-card-content>
            <div id="chartdivStacked"></div>
            <h2 class="graphTitle">Number of users per quality (Group by devices) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of users filter by quality and device used." [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="nbViewingStacked"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="charttime">
        <mat-card-content>
            <div id="chartdivTime"></div>
            <h2 class="graphTitle">Viewing Time (seconds) per Quality <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Average users viewing time by quality used." [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="viewingTime"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="charttime">
        <mat-card-content>
            <div id="qualityEvolutionChart"></div>
            <h2 class="graphTitle">Users per quality evolution (display with consumption)<mat-icon class="p-r15" style="cursor: pointer" matTooltip="Nb users per quality evolution through time." [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="qualityEvolution"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>


    <mat-card class="charttime">
        <mat-card-content>
            <div id="chartdivArrivalsDeparture"></div>
            <h2 class="graphTitle">Users arrivals vs departues<mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of segment logs per segment type" [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="chartdivArrivalsDeparturesSpin"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="charttime">
        <mat-card-content>
            <div id="chartdivQualitySecondConsumption"></div>
            <h2 class="graphTitle">Second consumption per quality<mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of segment logs per segment type" [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="chartdivQualitySecondConsumptionSpin"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="charttime">
        <mat-card-content>
            <div id="chartdivAudioVideoType"></div>
            <h2 class="graphTitle">Audio / Video segment type (by nb logs) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of segment logs per segment type" [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="SegmentTypeSpin"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <!-- <mat-card class="numbers"> -->
        <mat-card class="numbers_card">
            <div class="nb_stat" *ngIf="(segment_duration !== [])">
                <mat-icon class="identity">timer</mat-icon>
                <h2 class="stat_title">Segment load duration : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Average segment load duration." [matTooltipPosition]="'right'">info</mat-icon></h2>
                <div class="d-flex justify-content-center">
                    <div class="spinner-border" role="status" >
                        <span class="sr-only" id="segmentLoad"></span>
                    </div>
                </div>
                <h2 class="current_nb"> {{ segment_duration[1] }} s</h2>
                <hr>
                <h5>Comparing to the same previous interval</h5>
                <div class="nb_stat1" *ngIf="segment_duration[1] - segment_duration[2] < 0; else elseblock1"><h3 class="previous_nb1"> {{ segment_duration[2] }} s</h3><h4 class="previous_nb1"> {{ segment_duration[0] }} %</h4></div>
                <ng-template class="elseblock" #elseblock1><h3 class="previous_nb2"> {{ segment_duration[2] }} s</h3><h4 class="previous_nb2"> + {{ segment_duration[0] }} %</h4></ng-template>
            </div>
        </mat-card>

        <mat-card class="graphCard">
            <mat-card-content>
                <div id="StatusColumn"></div>
                <!--                <h2 class="graphTitle">TODO Viewing Time (seconds) per Segment Load Time FAKE DATA</h2>-->
                <h2 class="graphTitle">Different Status (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Users ration on different request status." [matTooltipPosition]="'right'">info</mat-icon></h2>
                <div class="d-flex justify-content-center">
                    <div class="spinner-border" role="status" >
                        <span class="sr-only" id="viewingTimeSegment"></span>
                    </div>
                </div>
            </mat-card-content>
        </mat-card>
    <!-- </mat-card> -->
</mat-card>
