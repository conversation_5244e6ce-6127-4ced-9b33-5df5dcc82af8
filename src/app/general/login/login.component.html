<div gdColumns="repeat(12, 1fr)" gdGap="15px" class="containerX">
    <div class="card-wrapper" gdColumn.gt-sm="5/9" gdColumn.lt-md="1/13" gdRow="1/2">
        <mat-card class="">
            <mat-card-title i18n="@@login">Login</mat-card-title>
            <form class="example-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                <div>
                    <mat-form-field class="example-full-width">
                        <mat-label>Email</mat-label>
                        <input #name formControlName="username" matInput>
                    </mat-form-field>
                </div>
                <div>
                    <mat-form-field class="example-full-width">
                        <mat-label>Password</mat-label>
                        <input matInput formControlName="password" type="password">
                    </mat-form-field>
                </div>

                <div *ngIf="error === true">
                    <mat-error class="error">Wrong credentials</mat-error>
                </div>
                <div *ngIf="error_reset === true">
                    <mat-error class="error">Set your email to reset your password.</mat-error>
                </div>
                <div *ngIf="reset === true">
                    <mat-success class="success">A reset password email has been sent.</mat-success>
                </div>

                <div id="submit">
                    <button mat-flat-button color="accent" type="submit" [disabled]="!loginForm.valid" (click)="setName(name.value)">Submit</button>
                </div>
                <div>
                    <button mat-button i18n="@@forget" type="button" (click)="handleForgetPassword(name.value)">Forget password</button>
                </div>
            </form>
        </mat-card>
    </div>
</div>
