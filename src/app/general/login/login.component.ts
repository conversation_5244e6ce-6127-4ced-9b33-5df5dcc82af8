import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { Router} from '@angular/router';
import { normalizeSourceMaps } from '@angular-devkit/build-angular/src/utils';
import { GlobalConstants} from '../../common/global-constants';
import * as moment from 'moment-timezone'

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  error;
  error_reset;
  reset;
  value;
  loginForm = new FormGroup({
    username: new FormControl(''),
    password: new FormControl(''),
  });

  constructor(private http: HttpClient, private router: Router) { }

  ngOnInit() {

  this.value = localStorage.getItem('token');
  if (this.value != 'null' && this.value != null) {
    localStorage.clear();

      this.http.post(GlobalConstants.apiURL + 'user/logout',
        null,{
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Authorization', this.value)
        }
    ).subscribe(response => {
          localStorage.setItem('token', null);
        });
    localStorage.setItem('token', null);
    }
  }

  setName(name: string) {
      localStorage.setItem('name', name)
  }

  async handleForgetPassword(name: string) {
    // Code to handle forget password event
    this.error_reset = false;
    this.error = false;
    if (name == "") {
      this.error_reset = true;
    }

    const body = new HttpParams()
          .set('username', this.loginForm.get('username').value)
      var request = await this.http.post(GlobalConstants.apiURL + 'user/forgot-password',
          body,
          {
              headers: new HttpHeaders()
                  .set('accept', 'application/json')
                  .set('Content-Type', 'application/x-www-form-urlencoded')
          }
      ).toPromise()
          .catch(
              (err: HttpErrorResponse) => {
                  this.error = true;
                  console.error('An error occurred:', err.error);
              }
          );
      if (request !== undefined) {
          this.reset = true;
      }
  }

  async onSubmit() {

      localStorage.setItem('timeZoneDecalage', (moment().tz('Europe/Paris')['_offset'] / 60).toString());
      localStorage.setItem('signDecalage', '+');
      localStorage.setItem('TimeZone', "Europe/Paris");
      localStorage.setItem('measures', "0");
      localStorage.setItem('displayType', "0");
      localStorage.setItem('filterDimension', "0");
      localStorage.setItem('cache', 'get');
      const body = new HttpParams()
          .set('username', this.loginForm.get('username').value)
          .set('password', this.loginForm.get('password').value);
      var request = await this.http.post(GlobalConstants.apiURL + 'user/login',
          body,
          {
              headers: new HttpHeaders()
                  .set('accept', 'application/json')
                  .set('Content-Type', 'application/x-www-form-urlencoded')
          }
      ).toPromise()
          .catch(
              (err: HttpErrorResponse) => {
                  this.error = true;
                  console.error('An error occurred:', err.error);
              }
          );
      if (request !== undefined) {
          // @ts-ignore
          localStorage.setItem('token', request.Authorization);
          // @ts-ignore
          localStorage.setItem('user_public_id', request.user_public_id)
          this.router.navigate(['/']);
      }
  }
}
