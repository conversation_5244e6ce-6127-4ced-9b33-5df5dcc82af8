.padding-hor-10 {
  padding-left: 10px;
  padding-right: 10px;
}

.resizable {
  resize: both;
  overflow: auto;
}

.info {
  margin-bottom: 15px;
}

.dashboard {
  margin-top: 2rem;

}

.result_consp {
  text-align: center;
  padding-bottom: 20px;
}

.title {
  padding-bottom: 0px;
  display: inline-block;
}

#cache-btn {
  margin-left: 20px;
  display: inline-block;
}

.request-input {
  width: 100%;
  padding: 12px 20px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  margin-bottom: 20px;
  margin-top: 20px;
}

.label {
  font-family: Arial;
  font-size: 20px;
  text-decoration: underline;
}

.send_btn {
  padding-right: 20px;
  margin-right: 20px;
}

.current_nb {
  margin-left: 10px;
  color: #455da5;
}

.stat_title {
  text-align: center;
  display: inline-block;
  padding-left: 60px;
  text-decoration: underline;
}

.previous_nb1 {
  margin-left: 10px;
  color: green;
  //display: inline-block;
}

.previous_nb2 {
  margin-left: 10px;
  color: #c71818;
  //display: inline-block;
}

.identity {
  font-size: 50px;
  //display: inline-block;
  position: absolute;
}

.numbers {
  display: flex;
  padding-bottom: 40px;
}

.nb_stat {
  width: 100%;
  text-align: center;
  display: inline-block;
}

.nb_stat2 {
  width: 40%;
  margin-right: 20px;
  text-align: center;
  display: inline-block;
}

.nb_stat2bis {
  width: 40%;
  text-align: center;
  display: inline-block;
  margin-left: 20px;
}

.webmobiletitle {
  margin-right: 10px;
  text-align: left;
  display: inline-block;
}
.webmobiletitle2 {
  margin-left: 10px;
  text-align: right;
  display: inline-block;
}

.to-center {
  text-align: center;
  width: 100%;
}

.vertical-line {
  margin-top: 40px;
  position: absolute;
  vertical-align: center;
  border-left: 2px solid #000;
  display: inline-block;
  height: 150px;
}

.vertical-line-little {
  margin-top: 40px;
  position: absolute;
  vertical-align: center;
  border-left: 2px solid #000;
  display: inline-block;
  height: 100px;
}

.vertical-line-title {
  border-left: 2px solid #000;
  display: inline-block;
  height: 15px;
}

.checkboxes {
  display: inline-block;
}

.container {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #2196F3;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.validate_services {
  padding-top: 20px;
  padding-bottom: 40px;
}


.numbers_card {
  //border-style: solid;
  //border-width: thin;
  text-align: center;
  display: inline-block;
  width: 100%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

.numbers_cardbis {
  //border-style: solid;
  //border-width: thin;
  text-align: center;
  display: inline-block;
  width: 100%;
  //min-width: 20%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

.numbers_cardbisavg {
  //border-style: solid;
  //border-width: thin;
  text-align: center;
  display: inline-block;
  //min-width: 30%;
  width: 100%;
  //min-width: 20%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

#chartdiv {
  margin-right: auto;
  margin-left: auto;
  width: 50%;
  height: 500px;
  padding-bottom: 20px;
}

#chartdivLiveVod {
  margin-right: auto;
  margin-left: auto;
  width: 50%;
  height: 500px;
  padding-bottom: 20px;
}

.pieChart_card {
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-left: 20px;
  margin-right: 20px;
  background: rgba(33,150,243,0.2);
}

.pieChart_cardLiveVod {
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-left: 20px;
  margin-right: 20px;
  background: rgba(33,150,243,0.2);
}

.piechart {
  display: flex;
}
 .line {
   margin-bottom: 40px;
 }

#chartdivGrid {
  width: 100%;
  height: 500px;
}

#chartdivCurve {
  width: 100%;
  height: 500px;
}

.grid {
  text-align: center;
  padding-bottom: 60px;
  background-color: whitesmoke;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

.sub_content {
  display: inline-block;
}

.page {
  background-color: whitesmoke;
}

.result_precision {
  text-align: center;
}

.result_timezone {
  text-align: center;
}

#users, #sessions, #activeSessions, #averageViewingDuration, #consumptionPeaks {
  position: absolute;
  left: 60%;
  //right: 50%;
  top: 90%;
  z-index: 1;
  width: 55px;
  height: 55px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

/* Change the text color of the tooltip */
::ng-deep .mat-tooltip {
  color: white !important;
}

#users_web, #sessions_web, #averageViewingDuration_web, #consumptionPeaks_web {
  position: absolute;
  left: 35%;
  top: 90%;
  z-index: 1;
  width: 55px;
  height: 55px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

#users_mobile, #sessions_mobile, #averageViewingDuration_mobile, #consumptionPeaks_mobile {
  position: absolute;
  left: 90%;
  top: 90%;
  z-index: 1;
  width: 55px;
  height: 55px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

#user_visit{
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

//.vertical-line{
//  border-left: 2px solid #000;
//  display: inline-block;
//  height: 130px;
//  margin: 0 20px;
//}
