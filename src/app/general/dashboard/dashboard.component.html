

<mat-card class="page">
    <h1 class="title">DASHBOARD</h1>
    <mat-slide-toggle id="cache-btn" [checked]="checkedCache" (click)="changeCacheStatus()">Cache</mat-slide-toggle>

    <h4>{{last_cache_update}}</h4>
    <h4>Number of services selected : {{service.split(',').length}}</h4>

    <mat-card-content>
        <section class="result_consp">
            <!--            <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle">Service(s) : </h2><h2 class="sub_content"> {{ service }}</h2></div>-->
            <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle"></h2><h2 class="sub_content"></h2></div>
            <ng-template class="elseBlock" #elseBlock1><mat-error>Please select one or more service(s) you have access to.</mat-error></ng-template>
        </section>

        <div *ngIf="show_lag_error_message == true" >
            <app-alert-modal [title]="'This instance is having an issue '" (closeModalEvent)="closeCustomModal()">
                <h3>{{ lag_error }}</h3>
                <button mat-flat-button color="accent" style="width: 100%; margin-top: 20px;" (click)="redirectTo(backup_server)">Go to the backup !</button>
            </app-alert-modal>
        </div>


        <h3>Total</h3>

        <section class="numbers">
            <mat-card class="numbers_card">
                <div class="to-center">
                    <mat-icon class="identity">perm_identity</mat-icon>
                    <h2 class="stat_title">Users : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of users according to ip addresses" [matTooltipPosition]="'right'">info</mat-icon></h2>
                </div>
                <div class="d-flex justify-content-center">
                    <div class="spinner-border" role="status" >
                        <span class="sr-only" id="users"></span>
                    </div>
                </div>
                <div class="nb_stat" *ngIf="nb_user[1]">
                    <h2 class="current_nb"> {{ numberWithCommas(nb_user[1]) }}</h2>
                    <hr>
                    <h5>Comparing to the same previous interval</h5>
                    <div class="nb_stat1" *ngIf="nb_user[1] - nb_user[2] < 0; else elseblock"><h3 class="previous_nb2"> {{ numberWithCommas(nb_user[2]) }}</h3><h4 class="previous_nb2"> {{ numberWithCommas(nb_user[0]) }} %</h4></div>
                    <ng-template class="elseblock" #elseblock><h3 class="previous_nb1"> {{ numberWithCommas(nb_user[2]) }} </h3><h4 class="previous_nb1"> + {{ numberWithCommas(nb_user[0]) }} %</h4></ng-template>
                </div>
            </mat-card>

            <mat-card class="numbers_card">
                <div class="to-center">
                    <mat-icon class="identity">trending_up</mat-icon>
                    <h2 class="stat_title">Consumption peak : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Peak consumption time in Go with number of users equivalence" [matTooltipPosition]="'right'">info</mat-icon></h2>
                </div>
                <div class="d-flex justify-content-center">
                    <div class="spinner-border" role="status" >
                        <span class="sr-only" id="consumptionPeaks"></span>
                    </div>
                </div>
                <div class="nb_stat" *ngIf="consoMax !== [] && consoMax[1] && consoMax[2]">
                    <h2 class="current_nb"> {{ consoMax[1][0] }} Go</h2>
                    <h3 class="current_nb"> Peak time : {{ consoMax[1][2] || 0 }}/{{consoMax[1][3] || 0 }}/{{consoMax[1][4] || 0}} - {{ consoMax[1][1] || 0}}H</h3>
                    <h3 class="current_nb"> {{ numberWithCommas(consoMax[1][5]) }} users</h3>
                    <hr>
                    <h5>Comparing to the same previous interval</h5>
                    <div class="nb_stat1" *ngIf="consoMax[1][0] - consoMax[2][0] < 0; else elseblock1">
                        <h4 class="previous_nb2"> {{ consoMax[2][0] }} Go </h4>
                        <h4 class="previous_nb2"> {{ consoMax[2][2] }}/{{consoMax[2][3] }}/{{consoMax[2][4] }} - {{ consoMax[2][1] }}H  |  {{ numberWithCommas(consoMax[2][5]) }} users</h4>
                        <h4 class="previous_nb2"> {{ consoMax[0] }} %</h4>
                    </div>
                    <ng-template class="elseblock" #elseblock1><h3 class="previous_nb1"> {{ consoMax[2][0] }} Go ( {{ consoMax[2][2] }}/{{consoMax[2][3] }}/{{consoMax[2][4] }} - {{ consoMax[2][1] }}H ) / {{ numberWithCommas(consoMax[2][5]) }} users</h3><h4 class="previous_nb1"> + {{ consoMax[0] }} %</h4></ng-template>
                </div>
            </mat-card>

            <mat-card class="numbers_card">
                <div class="nb_stat" *ngIf="nb_session !== []">
                    <mat-icon class="identity">airline_seat_recline_normal</mat-icon>
                    <h2 class="stat_title">Sessions : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of sessions according on the start of players" [matTooltipPosition]="'right'">info</mat-icon></h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="sessions"></span>
                        </div>
                    </div>
                    <h2 class="current_nb"> {{ numberWithCommas(nb_session[1]) }}</h2>
                </div></mat-card>

            <mat-card class="numbers_card">
                <div class="nb_stat" *ngIf="avg_duration !== []">
                    <mat-icon class="identity">query_builder</mat-icon>
                    <h2 class="stat_title">Average session duration : </h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="averageViewingDuration"></span>
                        </div>
                    </div>
                    <h2 class="current_nb">{{formatDuration(avg_duration[1], true)}}</h2>
                </div>
            </mat-card>
        </section>

            <hr class="line">
            <h3 class="webmobiletitle">Web</h3>
            <span class="vertical-line-title"></span>
            <h3 class="webmobiletitle2">Mobile</h3>

            <section class="numbers">
                <mat-card class="numbers_cardbis">
                    <div class="to-center">
                        <mat-icon class="identity">perm_identity</mat-icon>
                        <h2 class="stat_title">Users : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of users according to ip addresses (Web | Mobile)" [matTooltipPosition]="'right'">info</mat-icon></h2>
                    </div>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="users_web"></span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="users_mobile"></span>
                        </div>
                    </div>

                    <div class="nb_stat2" *ngIf="nb_user_web[1]">
                        <h3>Web</h3>
                        <h2 class="current_nb"> {{ numberWithCommas(nb_user_web[1]) }}</h2>
                        <hr>
                        <h5>Comparing to the same previous interval</h5>
                        <div class="nb_stat1" *ngIf="nb_user_web[1] - nb_user_web[2] < 0; else elseblock"><h3 class="previous_nb2"> {{ numberWithCommas(nb_user_web[2]) }}</h3><h4 class="previous_nb2"> {{ numberWithCommas(nb_user_web[0]) }} %</h4></div>
                        <ng-template class="elseblock" #elseblock><h3 class="previous_nb1"> {{ numberWithCommas(nb_user_web[2]) }} </h3><h4 class="previous_nb1"> + {{ numberWithCommas(nb_user_web[0]) }} %</h4></ng-template>
                    </div>

                    <span class="vertical-line" *ngIf="nb_user_web[1]"></span>

                    <div class="nb_stat2bis" *ngIf="nb_user_mobile[1]">
                        <h3>Mobile</h3>
                        <h2 class="current_nb"> {{ numberWithCommas(nb_user_mobile[1]) }}</h2>
                        <hr>
                        <h5>Comparing to the same previous interval</h5>
                        <div class="nb_stat1bis" *ngIf="nb_user_mobile[1] - nb_user_mobile[2] < 0; else elseblock"><h3 class="previous_nb2"> {{ numberWithCommas(nb_user_mobile[2]) }}</h3><h4 class="previous_nb2"> {{ numberWithCommas(nb_user_mobile[0]) }} %</h4></div>
                        <ng-template class="elseblock" #elseblock><h3 class="previous_nb1"> {{ numberWithCommas(nb_user_mobile[2]) }} </h3><h4 class="previous_nb1"> + {{ numberWithCommas(nb_user_mobile[0]) }} %</h4></ng-template>
                    </div>

                </mat-card>



                <mat-card class="numbers_cardbis">
                    <div class="to-center">
                        <mat-icon class="identity">trending_up</mat-icon>
                        <h2 class="stat_title">Consumption peak : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Peak consumption time in Go with number of users equivalence (Web | Mobile)" [matTooltipPosition]="'right'">info</mat-icon></h2>
                    </div>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="consumptionPeaks_web"></span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="consumptionPeaks_mobile"></span>
                        </div>
                    </div>

                    <div class="nb_stat2" *ngIf="consoMax_web !== [] && consoMax_web[1] && consoMax_web[2]">
                        <h3>Web</h3>
                        <h3 class="current_nb"> {{ consoMax_web[1][0] }} Go</h3>
                        <h4 class="current_nb"> {{ consoMax_web[1][2] || 0}}/{{consoMax_web[1][3] || 0}}/{{consoMax_web[1][4] || 0}} - {{ consoMax_web[1][1] || 0}}H</h4>
                        <h4 class="current_nb"> {{ numberWithCommas(consoMax_web[1][5]) }} users</h4>
                        <hr>
                        <h5>Comparing to the same previous interval</h5>
                        <div class="nb_stat1" *ngIf="consoMax_web[1][0] - consoMax_web[2][0] < 0; else elseblock1">
                            <h4 class="previous_nb2"> {{ consoMax_web[2][0] }} Go </h4>
                            <h4 class="previous_nb2">{{ consoMax_web[2][2] }}/{{consoMax_web[2][3] }}/{{consoMax_web[2][4] }} - {{ consoMax_web[2][1] }}H</h4>
                            <h4 class="previous_nb2"> {{ numberWithCommas(consoMax_web[2][5]) }} users</h4>
                            <h4 class="previous_nb2"> {{ consoMax_web[0] }} %</h4></div>
                        <ng-template class="elseblock" #elseblock1>
                            <h4 class="previous_nb1"> {{ consoMax_web[2][0] }} Go</h4>
                            <h4 class="previous_nb1">{{ consoMax_web[2][2] || 0}}/{{consoMax_web[2][3] || 0}}/{{consoMax_web[2][4] || 0}} - {{ consoMax_web[2][1] || 0}}H</h4>
                            <h4 class="previous_nb1">{{ numberWithCommas(consoMax_web[2][5]) }} users</h4>
                            <h4 class="previous_nb1"> + {{ consoMax_web[0] }} %</h4>
                        </ng-template>
                    </div>

                    <span class="vertical-line" *ngIf="consoMax_web !== [] && consoMax_web[1] && consoMax_web[2]"></span>

                    <div class="nb_stat2bis" *ngIf="consoMax_mobile !== [] && consoMax_mobile[1] && consoMax_mobile[2]">
                        <h3>Mobile</h3>
                        <h3 class="current_nb"> {{ consoMax_mobile[1][0] }} Go</h3>
                        <h4 class="current_nb">{{ consoMax_mobile[1][2] || 0}}/{{consoMax_mobile[1][3] || 0}}/{{consoMax_mobile[1][4] || 0}} - {{ consoMax_mobile[1][1] || 0}}H</h4>
                        <h4 class="current_nb"> {{ numberWithCommas(consoMax_mobile[1][5]) }} users</h4>
                        <hr>
                        <h5>Comparing to the same previous interval</h5>
                        <div class="nb_stat1bis" *ngIf="consoMax_mobile[1][0] - consoMax_mobile[2][0] < 0; else elseblock1">
                            <h4 class="previous_nb2"> {{ consoMax_mobile[2][0] }} Go</h4>
                            <h4 class="previous_nb2"> {{ consoMax_mobile[2][2] || 0}}/{{consoMax_mobile[2][3] || 0}}/{{consoMax_mobile[2][4] || 0}} - {{ consoMax_mobile[2][1] || 0}}H</h4>
                            <h4 class="previous_nb2"> {{ numberWithCommas(consoMax_mobile[2][5]) }} users</h4>
                            <h4 class="previous_nb2"> {{ consoMax_mobile[0] }} %</h4></div>
                        <ng-template class="elseblock" #elseblock1>
                            <h4 class="previous_nb1"> {{ consoMax_mobile[2][0] }} Go </h4>
                            <h4 class="previous_nb1"> {{ consoMax_mobile[2][2] || 0}}/{{consoMax_mobile[2][3] || 0}}/{{consoMax_mobile[2][4] || 0}} - {{ consoMax_mobile[2][1] || 0}}H</h4>
                            <h4 class="previous_nb1"> {{ numberWithCommas(consoMax_mobile[2][5]) }} users</h4>
                            <h4 class="previous_nb1"> + {{ consoMax_mobile[0] }} %</h4></ng-template>
                    </div>
                </mat-card>
                <mat-card class="numbers_cardbis">
                    <mat-icon class="identity">airline_seat_recline_normal</mat-icon>
                    <h2 class="stat_title">Sessions : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of sessions according on the start of players (Web | Mobile)" [matTooltipPosition]="'right'">info</mat-icon></h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="sessions_web"></span>
                        </div>
                    </div>
                    <div class="nb_stat2" *ngIf="nb_session_web !== []">
                        <h3>Web</h3>
                        <h2 class="current_nb"> {{ numberWithCommas(nb_session_web[1]) }}</h2>
                    </div>

                    <span class="vertical-line-little" *ngIf="nb_session_web[1] && nb_session_mobile[1]"></span>

                    <div class="nb_stat2bis" *ngIf="nb_session_mobile !== []">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border" role="status" >
                                <span class="sr-only" id="sessions_mobile"></span>
                            </div>
                        </div>
                        <h3>Mobile</h3>
                        <h2 class="current_nb"> {{ numberWithCommas(nb_session_mobile[1]) }}</h2>
                    </div>
                </mat-card>
                <mat-card class="numbers_cardbisavg">
                    <mat-icon class="identity">query_builder</mat-icon>
                    <h2 class="stat_title">Average session duration : </h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="averageViewingDuration_web"></span>
                        </div>
                    </div>
                    <div class="nb_stat2" *ngIf="avg_duration_web !== []">
                        <h3>Web</h3>
                        <h2 class="current_nb"> {{formatDuration(avg_duration_web[1], true) }}</h2>
                    </div>

                    <span class="vertical-line-little" *ngIf="avg_duration_web[1] && avg_duration_mobile[1]"></span>

                    <div class="nb_stat2bis" *ngIf="avg_duration_mobile !== []">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border" role="status" >
                                <span class="sr-only" id="averageViewingDuration_mobile"></span>
                            </div>
                        </div>
                        <h3>Mobile</h3>
                        <h2 class="current_nb"> {{formatDuration(avg_duration_mobile[1], true)}}</h2>
                    </div>
                </mat-card>
            </section>
        <hr class="line">
        <mat-card class="grid">
            <h2>When do your users visit ? (Last complete week) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Hourly distribution of users over the last week" [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="user_visit"></span>
                </div>
            </div>
            <div id="chartdivGrid"></div>
        </mat-card><hr class="line">
        <app-pie-charts-dashboard></app-pie-charts-dashboard>
    </mat-card-content>
</mat-card>

