import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { PieChartsDashboardComponent} from '../pie-charts-dashboard/pie-charts-dashboard.component';
import { AlertModalComponent } from '../alert-modal/alert-modal.component';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import * as am4charts from '@amcharts/amcharts4/charts';
import { HttpUrlEncodingCodec } from '@angular/common/http';
import{ GlobalConstants } from '../../common/global-constants';
import { NavbarComponent } from '../../layout/navbar/navbar.component';

function hide_users() {
    document.getElementById('users')
        .style.display = 'none';
}

function hide_sessions() {
    document.getElementById('sessions')
        .style.display = 'none';
}

function hide_activeSessions() {
    document.getElementById('activeSessions')
        .style.display = 'none';
}

function hide_viewingDuration() {
    document.getElementById('averageViewingDuration')
        .style.display = 'none';
}

function hide_consumptionPeak() {
    document.getElementById('consumptionPeaks')
        .style.display = 'none';
}

function hide_user_visit() {
    document.getElementById('user_visit')
        .style.display = 'none';
}


function hide_users_web() {
    document.getElementById('users_web')
        .style.display = 'none';
}
function hide_sessions_web() {
    document.getElementById('sessions_web')
        .style.display = 'none';
}
function hide_viewingDuration_web() {
    document.getElementById('averageViewingDuration_web')
        .style.display = 'none';
}
function hide_consumptionPeak_web() {
    document.getElementById('consumptionPeaks_web')
        .style.display = 'none';
}

function hide_users_mobile() {
    document.getElementById('users_mobile')
        .style.display = 'none';
}
function hide_sessions_mobile() {
    document.getElementById('sessions_mobile')
        .style.display = 'none';
}
function hide_viewingDuration_mobile() {
    document.getElementById('averageViewingDuration_mobile')
        .style.display = 'none';
}
function hide_consumptionPeak_mobile() {
    document.getElementById('consumptionPeaks_mobile')
        .style.display = 'none';
}

@Component({
    selector: 'app-dashboard-component',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
    precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
    interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
    interval2 = {'HOUR': '\'2\' HOUR', 'DAY': '\'2\' DAY', 'WEEK': '\'14\' DAY', 'MONTH': '\'2\' MONTH', 'YEAR': '\'2\' YEAR'};
    mobile_web = {'total':'', 'mobile': 'AND browser_type IN (\'multimedia_player\', \'mobile_browser\', \'Multimedia player\', \'Mobile browser\')', 'web': 'AND browser_type NOT IN (\'multimedia_player\', \'mobile_browser\', \'Multimedia player\', \'Mobile browser\')'};
    username: string;
    var_to_return_tmp = '';
    services = [];
    checkbox = [];

    avg_duration = [];
    nb_session = [];
    nb_active_session = [];
    consoMax = [];
    nb_user = [];

    avg_duration_web = [];
    nb_session_web = [];
    consoMax_web = [];
    nb_user_web = [];

    avg_duration_mobile = [];
    nb_session_mobile = [];
    consoMax_mobile = [];
    nb_user_mobile = [];

    personalized;
    service = '';
    end;
    start;
    topic = GlobalConstants.topicNginx;
    topic_viewing = GlobalConstants.topicViewing;
    topic_session = GlobalConstants.topicNginxPersonalized;
    navbar = new NavbarComponent(null, this.http, null);
    consumption;
    timeZone = '';
    result;
    precision_display = '';
    start_conso_max;
    end_conso_max;
    comparison_conso_max;
    checkedCache: boolean;
    last_cache_update = "";
    lag_error = "";
    backup_server = "";
    show_lag_error_message = false;

    constructor(private http: HttpClient) { }

    async ngOnInit() {

        this.username = localStorage.getItem('name');
        localStorage.setItem('granularity', 'DAY');
        this.checkedCache = localStorage.getItem('cache') == 'get';
        this.personalized = localStorage.getItem('personalized');
        this.start = localStorage.getItem('start_date');
        this.end = localStorage.getItem('end_date');
        this.service = localStorage.getItem('services');
        this.precision_display = localStorage.getItem('precision');
        if(localStorage.getItem('TimeZone')) {
            this.timeZone = localStorage.getItem('TimeZone');
        }

        let pieCharts = new PieChartsDashboardComponent(this.http);

        // Check if time interval isn't too big
        let s = new Date(localStorage.getItem('start_date'));
        let e = new Date(localStorage.getItem('end_date'));
        let difference = e.getTime() - s.getTime();
        let days_difference = difference / (1000 * 3600 * 24);
        if (days_difference > 7 || localStorage.getItem("precision") == "MONTH" || localStorage.getItem("precision") == "YEAR") {
            alert("Using interval bigger than 7 days in dashboard is not possible, to access the data you want for this type of large intervals, use more suitable pages like personalized.");
            localStorage.setItem("precision", "DAY");
            localStorage.setItem('displayCalendar', 'true');
            localStorage.setItem('personalized', '0');
            this.personalized = 0;
            localStorage.removeItem('precision');
            localStorage.removeItem('start_date');
            localStorage.removeItem('end_date');
            localStorage.removeItem('comparison_date');
            localStorage.removeItem('granularity');
            this.reload();
        }

        // Check this server Logs treatment lags to offer redirection if needed
        if (localStorage.getItem('checkLag') == null) {
            this.checklag().then(res => {
                if (res['status'] == '303') {
                    this.lag_error = res['message'].split(' : ')[0];
                    this.backup_server = res['message'].split(' : ')[1];
                    this.show_lag_error_message = true;
                } else {
                    localStorage.setItem('checkLag', 'Done');
                }
            });
        }

        this.getServices().then(res => {

            if (localStorage.getItem('precision').includes('complete')) {
                this.navbar.setLastIntervalDate(localStorage.getItem('precision'));
            }

            this.getUserNumber('total').then(nb_user => {
                this.nb_user = nb_user;
            });
            this.getConsumptionMax('total').then(max => {
                this.consoMax = max;
            });
            this.getAvgDuration('total').then(avg => {
                this.avg_duration = avg;
            });
            this.getNbSession('total').then(nb_session => {
                this.nb_session = nb_session;
                if (localStorage.getItem('cache') == 'skip')
                    pieCharts.validate();

            });

            if (localStorage.getItem('cache') == 'get')
                pieCharts.validate();

            this.getUserNumber('web').then(nb_user => {
                this.nb_user_web = nb_user;
                hide_users_web();
            });
            this.getUserNumber('mobile').then(nb_user => {
                this.nb_user_mobile = nb_user;
                hide_users_mobile();
            });
            this.getConsumptionMax('web').then(max => {
                this.consoMax_web = max;
                hide_consumptionPeak_web();
            });
            this.getConsumptionMax('mobile').then(max => {
                this.consoMax_mobile = max;
                hide_consumptionPeak_mobile();
            });
            this.getNbSession('web').then(nb_session => {
                this.nb_session_web = nb_session;
                hide_sessions_web();
            });
            this.getNbSession('mobile').then(nb_session => {
                this.nb_session_mobile = nb_session;
                hide_sessions_mobile();
            });
            this.getAvgDuration('web').then(avg => {
                this.avg_duration_web = avg;
                hide_viewingDuration_web();
            });
            this.getAvgDuration('mobile').then(avg => {
                this.avg_duration_mobile = avg;
                hide_viewingDuration_mobile();
            });
            this.getDayOfTheWeek().then(arr => {
                this.displayGridWeek(arr);
            });

            // this.getNbActiveSession().then(nb_active => {
            //     this.nb_active_session = nb_active;
            // });
        });

    }

    reload() {
        window.location.reload();
    }

    clear() {
        this.result = null;
    }

    displayGridWeek(arr) {

        am4core.useTheme(am4themes_animated);
        var chart = am4core.create("chartdivGrid", am4charts.XYChart);
        chart.maskBullets = false;
        chart.exporting.menu = new am4core.ExportMenu();
        chart.exporting.filePrefix = "AdvancedAnalyticsWeekGrid";

        let title = 'When do your users visit ? - Hexaglobe';
        let options = chart.exporting.getFormatOptions("pdf");
        options.addURL = false;
        chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
        pdf.doc.content.unshift({
            text: title,
            margin: [0, 30],
            style: {
            fontSize: 15,
            bold: true,
            }
        });
        // Add logo
        pdf.doc.content.unshift({
            image: GlobalConstants.hexaglobeLogo,
            fit: [119, 54]
        });
        return pdf;
        });
        chart.exporting.menu.items = [{
        "label": "...",
        "menu": [
            { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
            { "type": "png", "label": "PNG", "options": { "quality": 1 } },
            { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
            { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
            { "label": "Print", "type": "print" }
        ]
        }]; // Set the formats we want to allow the user to export in

        var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
        var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());
        xAxis.dataFields.category = "weekday";
        yAxis.dataFields.category = "hour";
        xAxis.renderer.grid.template.disabled = true;
        xAxis.renderer.minGridDistance = 40;
        yAxis.renderer.grid.template.disabled = true;
        yAxis.renderer.inversed = true;
        yAxis.renderer.minGridDistance = 30;
        var series = chart.series.push(new am4charts.ColumnSeries());
        series.dataFields.categoryX = "weekday";
        series.dataFields.categoryY = "hour";
        series.dataFields.value = "value";
        series.sequencedInterpolation = true;
        series.defaultState.transitionDuration = 3000;
        var bgColor = new am4core.InterfaceColorSet().getFor("background");
        var columnTemplate = series.columns.template;
        columnTemplate.strokeWidth = 1;
        columnTemplate.strokeOpacity = 0.2;
        columnTemplate.stroke = bgColor;
        columnTemplate.tooltipText = "{weekday}, {hour}: {value.workingValue.formatNumber('#.')}";
        columnTemplate.width = am4core.percent(100);
        columnTemplate.height = am4core.percent(100);
        series.heatRules.push({
            target: columnTemplate,
            property: "fill",
            min: am4core.color(bgColor),
            max: chart.colors.getIndex(0)
        });
        var heatLegend = chart.bottomAxesContainer.createChild(am4charts.HeatLegend);
        heatLegend.width = am4core.percent(100);
        heatLegend.series = series;
        heatLegend.valueAxis.renderer.labels.template.fontSize = 9;
        heatLegend.valueAxis.renderer.minGridDistance = 30;
        series.columns.template.events.on("over", function(event) {
            handleHover(event.target);
        });
        series.columns.template.events.on("hit", function(event) {
            handleHover(event.target);
        });
        function handleHover(column) {
            if (!isNaN(column.dataItem.value)) {
                heatLegend.valueAxis.showTooltipAt(column.dataItem.value)
            }
            else {
                heatLegend.valueAxis.hideTooltip();
            }
        }
        series.columns.template.events.on("out", function(event) {
            heatLegend.valueAxis.hideTooltip();
        });

        chart.data = arr;
        hide_user_visit()
    }

    async getDayOfTheWeek() {

        let now = new Date();
        let body;
        let last = now.getDate() - now.getDay() - 1;
        let first = last - 5;
        let firstday = new Date(now.setDate(first)).toISOString().substring(0, 10).toString();
        let lastday = new Date(now.setDate(now.getDate()+7)).toISOString().substring(0, 10).toString();
        let day = {0: 'Sun', 1: 'Mon', 2: 'Tue', 3: 'Wed', 4: 'Thu', 5: 'Fri', 6: 'Sat'};
        let start = firstday;
        let end = lastday;
        body = new HttpParams()
            .set('Request', 'select DISTINCT(EXTRACT(HOUR from __time)), EXTRACT(DAY from __time), EXTRACT(YEAR from __time), EXTRACT(MONTH from __time), COUNT(DISTINCT(\\"remote_addr\\")) FROM ' + this.topic +
                'where service_id IN (' + this.service + ') AND __time BETWEEN \'' + start + '\' AND \'' + end + '\' GROUP BY EXTRACT(HOUR from __time), EXTRACT(DAY from __time), EXTRACT(YEAR from __time), EXTRACT(MONTH from __time) ORDER BY EXTRACT(HOUR from __time), EXTRACT(DAY from __time) LIMIT 300')
            .set('Cache', localStorage.getItem('cache'))
            .set('TimeZone', localStorage.getItem('TimeZone'));

        const result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();
        let jsonArr = [];

        result['data'] = result['data'].slice(1);
        let fill = [{'hour': "0H", 'weekday': "Mon", 'value': 0}, {'hour': "0H", 'weekday': "Tue", 'value': 0}, {'hour': "0H", 'weekday': "Wed", 'value': 0}, {'hour': "0H", 'weekday': "Thu", 'value': 0}, {'hour': "0H", 'weekday': "Fri", 'value': 0}, {'hour': "0H", 'weekday': "Sat", 'value': 0}, {'hour': "0H", 'weekday': "Sun", 'value': 0}, ];
        for (let item of result['data']) {

            if (item[3] < 10)
                item[3] = '0' + item[3];
            if (item[1] < 10)
                item[1] = '0' + item[1];
            if (item[0] < 10)
                item[0]  = '0' +item[0];
            let date = item[2] + '-' + item[3] + '-' + item[1] + 'T' + item[0] + ':00:00';
            let new_date = new Date(date);
            let new_date_iso = new_date.toISOString();
            date = day[new Date(date).getDay()];
            let date1 = day[new Date(new_date_iso).getDay()];
            jsonArr.push({
                'hour': new_date.getHours() + 'H',
                'weekday': date1,
                'value': item[4]
            })
        }
        jsonArr = fill.concat(jsonArr);
        return jsonArr;
    }

    async getUserNumber(where_clause) {

        let body;
        if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', 'select count(DISTINCT \\"remote_addr\\") ' +
                    'FROM ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] +
                    ' UNION ALL select count(DISTINCT \\"remote_addr\\") ' +
                    'from ' + this.topic + 'where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval2[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] +
                    ' AND service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' LIMIT 100')
                .set('Cache', localStorage.getItem('cache'))
                .set('TimeZone', localStorage.getItem('TimeZone'));
        } else {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', 'select count(DISTINCT \\"remote_addr\\") ' +
                    'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\'' + ' AND __time <= \'' + localStorage.getItem('end_date') + '\' AND service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] +
                    ' UNION ALL select count(DISTINCT \\"remote_addr\\") ' +
                    'from ' + this.topic + 'where __time >= \'' + localStorage.getItem('comparison_date') + '\'' + ' AND __time < \'' + localStorage.getItem('start_date') +  '\'' +
                    ' AND service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' LIMIT 100')
                .set('TimeZone', localStorage.getItem('TimeZone'));
        }
        const result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        this.last_cache_update = result['last_update'];

        result['data'][0] = this.getDifference(result['data']);
        if (result['data'][2] == undefined) {
            result['data'][0] = "No previous data";
        }
        if (result['data'][1] == null) {
            result['data'][1] = "No data available";
        }

        let elemprec;
        let prec = localStorage.getItem('precision');
        for (let key in this.precision) {
            if (this.precision[key] == prec) {
                elemprec = key;
            }
        }
        let precision = document.getElementById(elemprec) as HTMLInputElement;
        if (precision !== null) {
            precision.checked = true;
        }

        if (Response)
            hide_users();
        return (result['data']);
    }

    async getNbActiveSession() {

        let body;
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', 'select count(DISTINCT \\"remote_addr\\") FROM ' + this.topic + ' where __time >= CURRENT_TIMESTAMP - INTERVAL \'120\' SECOND ' +
                'AND service_id IN (' + this.service + ') LIMIT 100')
            .set('TimeZone', localStorage.getItem('TimeZone'));

        const result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        if (result['data'][1] == null) {
            result['data'][1] = "No data available";
        }
        if (Response)
            hide_activeSessions()
        return (result['data']);
    }

    async getNbSession(where_clause) {

        let body;
        let start = localStorage.getItem('start_date');
        let end = localStorage.getItem('end_date');
        if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select sum(measure) from(select count(DISTINCT(remote_addr)) as measure ' +
                    'FROM ' + this.topic_session + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP ' +
                    ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 ' + this.mobile_web[where_clause] + ' GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)))')
                .set('Cache', localStorage.getItem('cache'))
                .set('TimeZone', localStorage.getItem('TimeZone'));
        } else {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select sum(measure) from(select count(DISTINCT(remote_addr)) as measure ' +
                    'FROM ' + this.topic_session + ' WHERE __time <= \'' +  end + '\'' + 'and __time >= \'' + start + '\' ' +
                    ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 ' + this.mobile_web[where_clause] + ' GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)))')
                .set('TimeZone', localStorage.getItem('TimeZone'));
        }
        const result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        result['data'][0] = this.getDifference(result['data']);
        if (result['data'][2] == undefined) {
            result['data'][0] = "No previous data";
        }
        if (result['data'][1] == null) {
            result['data'][1] = "No data available";
        }

        //CHECKED AT LOADING THE NEW SREVICES CHECKBOXES
        let elem1 = [];
        if (localStorage.getItem('new_selected_services_checkboxes')) {
            elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
            for (let id of elem1) {
                let value = "serv" + id;
                let elem2 = document.getElementById(value) as HTMLInputElement;
                if (elem2 !== null) {
                    elem2.checked = true;
                }
            }
        } else {
            let value = "select-all";
            let elem2 = document.getElementById(value) as HTMLInputElement;
            if (elem2 !== null) {
                elem2.checked = true;
            }

            let check = [];
            let services = '';
            for(let i = 1; i<= this.services.length; i++) {
                check.push(i);
                services = services +  '\'' + this.services[i-1] + '\',';
            }
            localStorage.setItem('new_selected_services_checkboxes', check.toString());
            localStorage.removeItem('new_selected_services');
            localStorage.setItem('new_selected_services', services.slice(0, -1));

            elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
            for (let id of elem1) {
                let value = "serv" + id;
                let elem2 = document.getElementById(value) as HTMLInputElement;
                if (elem2 !== null) {
                    elem2.checked = true;
                }
            }
        }
        //END NEW CHECKED


        if (Response)
            hide_sessions()
        return (result['data']);
    }

    async getAvgDuration(where_clause) {
        let body;
        if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select AVG(numbers) from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
                    'FROM ' + this.topic_viewing + ' where __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  ' +
                    ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 ' + this.mobile_web[where_clause] + ' group by remote_addr) src )')
                .set('Cache', localStorage.getItem('cache'))
                .set('TimeZone', localStorage.getItem('TimeZone'));
        } else {

            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select AVG(numbers) from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
                    'FROM ' + this.topic_viewing + ' where __time < \'' +  localStorage.getItem('end_date') + '\'' + ' AND __time > \'' + localStorage.getItem('start_date') + '\'' + ' ' +
                    ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 ' + this.mobile_web[where_clause] + ' group by remote_addr) src )')
                .set('TimeZone', localStorage.getItem('TimeZone'));
        }
        const result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();
        result['data'][0] = this.getDifference(result['data']);
        if (result['data'][2] == undefined) {
            result['data'][0] = "No previous data";
        }
        if (result['data'][1] == null) {
            result['data'][1] = "No data available";
        }
        if (Response)
            hide_viewingDuration()
        return result['data'];
    }

    async getConsumptionMax(where_clause) {
        let body;

        if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null && localStorage.getItem('precision') !== 'DAY') {
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time), COUNT(DISTINCT \\\"remote_addr\\\") ' +
                    'FROM ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
                    'AND  service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' ' +
                    'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time) ORDER BY SUM(sum_bytes_sent) DESC ' +
                    'LIMIT 1) UNION ALL (select SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time), COUNT(DISTINCT \\\"remote_addr\\\") ' +
                    'FROM ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval2[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND  service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' ' +
                    'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time) ORDER BY SUM(sum_bytes_sent) DESC LIMIT 1)')
                .set('Cache', localStorage.getItem('cache'))
                .set('TimeZone', localStorage.getItem('TimeZone'));
        } else {
            if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {

                let today = new Date();
                let start = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
                let end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 0);
                let comparison = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
                start.setDate(today.getDate() - 1);
                start.setHours(start.getHours() - (start.getTimezoneOffset() / 60));
                end.setDate(today.getDate() - 1);
                end.setHours(end.getHours() - (end.getTimezoneOffset() / 60));
                comparison.setDate(today.getDate() - 2);
                this.start_conso_max = start.toISOString();
                this.end_conso_max = end.toISOString();
                this.comparison_conso_max = comparison.toISOString();
            } else {
                this.start_conso_max = localStorage.getItem('start_date');
                this.end_conso_max = localStorage.getItem('end_date');
                this.comparison_conso_max = localStorage.getItem('comparison_date');
            }

            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', '(select SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time), COUNT(DISTINCT \\\"remote_addr\\\") ' +
                    'FROM ' + this.topic + ' WHERE __time >= \'' + this.start_conso_max + '\'' + 'AND __time <= \'' + this.end_conso_max +  '\'' +
                    'AND  service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' ' +
                    'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time) ORDER BY SUM(sum_bytes_sent) DESC ' +
                    'LIMIT 1) UNION ALL (select SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time), COUNT(DISTINCT \\\"remote_addr\\\") ' +
                    'FROM ' + this.topic + ' WHERE __time >= \'' + this.comparison_conso_max + '\'' + ' AND __time < \'' + this.start_conso_max +  '\''+ ' AND  service_id IN (' + this.service + ') ' + this.mobile_web[where_clause] + ' ' +
                    'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time) ORDER BY SUM(sum_bytes_sent) DESC LIMIT 1)')
                .set('TimeZone', localStorage.getItem('TimeZone'));
        }
        const result = await this.http.post( GlobalConstants.apiURL+ 'log/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        if (where_clause == "total")
            hide_consumptionPeak();
        else if(where_clause == "web")
            hide_consumptionPeak_web();
        else {
            hide_consumptionPeak_mobile();
        }

        result['data'][0] = this.getDifference(result['data']);
        if (result['data'][1]) {
            result['data'][1][0] = Math.round((result['data'][1][0] / 1000000000) * 10) / 10;
        } else {
            result['data'][1] = [0];
        }
        if (result['data'][2]) {
            result['data'][2][0] = Math.round((result['data'][2][0] / 1000000000) * 10) / 10;
        } else {
            result['data'][2] = [0];
        }
        if (result['data'][2] == undefined) {
            result['data'][0] = "No previous data";
        }
        if (result['data'][1] == null) {
            result['data'][1] = "No data available";
        }
        // Gestion time zone difference hours

        if (result['data'][1][3] < 10) {
            result['data'][1][3] = '0' + result['data'][1][3];
        }
        if (result['data'][1][2] < 10) {
            result['data'][1][2] = '0' + result['data'][1][2];
        }
        if (result['data'][1][1] < 10) {
            result['data'][1][1] = '0' + result['data'][1][1];
        }
        let date1 =result['data'][1][4] + '-' + result['data'][1][3] + '-' + result['data'][1][2] + 'T' + result['data'][1][1] + ":00:00";
        // let new_date1 = this.personalizedTimeZoneHour(date1);
        result['data'][1][3] = new Date(date1).getMonth() + 1;
        result['data'][1][2] = new Date(date1).getDate();
        result['data'][1][1] = new Date(date1).getHours();



        if (result['data'][2][0] !== 0) {
            if (result['data'][2][3] < 10) {
                result['data'][2][3] = '0' + result['data'][2][3];
            }
            if (result['data'][2][2] < 10) {
                result['data'][2][2] = '0' + result['data'][2][2];
            }
            if (result['data'][2][1] < 10) {
                result['data'][2][1] = '0' + result['data'][2][1];
            }
            let date2 = result['data'][2][4] + '-' + result['data'][2][3] + '-' + result['data'][2][2] + 'T' + result['data'][2][1] + ":00:00";
            // let new_date2 = this.personalizedTimeZoneHour(date2);
            result['data'][2][3] = new Date(date2).getMonth() + 1;
            result['data'][2][2] = new Date(date2).getDate();
            result['data'][2][1] = new Date(date2).getHours();
        }

        hide_consumptionPeak()
        return result['data'];
    }

    numberWithCommas(x) {
        if (isNaN(x))
            return x;
        else
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
    }

    formatDuration(duration: any, round: boolean = false): string {
        if (round === false) {
            return isNaN(duration) ? this.numberWithCommas(duration) : this.numberWithCommas(duration) + ' s';
        } else {
            return isNaN(duration) ? this.numberWithCommas(duration) : this.numberWithCommas(Math.round(duration)) + ' s';
        }
    }

    getDifference(arr) {
        let val1;
        let val2;
        if (arr[1] && arr[2]) {
            val1 = arr[1][0];
            val2 = arr[2][0];
        } else {
            val1 = arr[1];
            val2 = arr[2];
        }
        let result = (val1 - val2);
        let operation = Number(result / val2)*100;
        if (isNaN(operation)) {
            return 0;
        } else {
            return Math.round((operation) * 10) / 10;
        }
    }

    async checklag() {
        const result = await this.http.get<any>(GlobalConstants.apiURL + 'log/checklag/',
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();
        return result;
    }

    async getServices() {

        if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
            this.service = localStorage.getItem('new_selected_services');
            let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
            for (let value of checkValues) {
                if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
                    this.checkbox.push(Number(value));
                }
            }
        }
        this.services = [];
        const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        for (let item of ret['data']) {
            this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
            this.services.push(item['name'])
        }
        if (this.var_to_return_tmp.charAt(0) === ',') {
            this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
        }
        //initialization below code is use to do not begin with no value when connect
        if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
            localStorage.setItem('initialized', '1');
            localStorage.setItem('new_selected_services', 'initialization');
            this.service = this.var_to_return_tmp;
            this.var_to_return_tmp = '';
            this.ngOnInit()
        }
        if(localStorage.getItem('refresh')) {
            localStorage.removeItem('refresh')
            let dashboard = new DashboardComponent(this.http);
            dashboard.reload();
        }
    }

    redirectTo(uri: string) {
        window.location.href = uri;
    }

    closeCustomModal() {
        this.show_lag_error_message = false;
    }

    validate() {
        this.ngOnInit();
    }

    changeCacheStatus() {
        if (localStorage.getItem('cache') == 'get') {
            localStorage.setItem('cache', 'skip');
            this.last_cache_update = "";
        } else
            localStorage.setItem('cache', 'get');
        this.reload();
    }
}
