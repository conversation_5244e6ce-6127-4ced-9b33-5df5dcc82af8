import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-alert-modal',
  templateUrl: './alert-modal.component.html',
  styleUrls: ['./alert-modal.component.scss']
})
export class AlertModalComponent implements OnInit {

  @Input() title: string = 'Modal Title';
  @Output() closeModalEvent = new EventEmitter();

  constructor() { }
  ngOnInit(): void {
  }

  closeModal() {
    this.closeModalEvent.emit();
  }
}
