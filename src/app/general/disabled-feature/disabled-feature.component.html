<mat-card>
    <mat-card-header>
        <mat-card-title>{{title}}
            <mat-icon *ngIf="unlocked; else locked">lock_open</mat-icon>
        </mat-card-title>
    </mat-card-header>

    <ng-template #locked>
        <mat-icon>lock</mat-icon>
    </ng-template>

    <button mat-raised-button color="accent" class="submit" (click)="showNotif($event)">
        Contact us for more information
    </button>

    <h3>Advanced sound</h3>
    <h4>
        Sound integration in your videos
    </h4>


    <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aliquam consequuntur impedit in inventore, maiores nam
        nobis non porro, recusandae reprehenderit suscipit temporibus voluptas voluptatibus. Doloremque eaque fugit
        omnis provident tenetur?</p>
    <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aliquam consequuntur impedit in inventore, maiores nam
        nobis non porro, recusandae reprehenderit suscipit temporibus voluptas voluptatibus. Doloremque eaque fugit
        omnis provident tenetur?</p>


    <h3>
        Advanced sound
    </h3>

    <div>
        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aliquam consequuntur impedit in inventore, maiores
            nam nobis non porro, recusandae reprehenderit suscipit temporibus voluptas voluptatibus. Doloremque eaque
            fugit omnis provident tenetur?</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aliquam consequuntur impedit in inventore, maiores
            nam nobis non porro, recusandae reprehenderit suscipit temporibus voluptas voluptatibus. Doloremque eaque
            fugit omnis provident tenetur?</p>
    </div>

</mat-card>


