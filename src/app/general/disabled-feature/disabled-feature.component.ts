import { Component, OnInit } from '@angular/core';
import { NotificationService } from '../../services/notification/notification.service';

@Component({
  selector: 'app-disabled-feature',
  templateUrl: './disabled-feature.component.html',
  styleUrls: ['./disabled-feature.component.scss']
})
export class DisabledFeatureComponent implements OnInit {

  constructor(private  notificationService: NotificationService) {
  }
  unlocked = false;
  title = 'Contact us to unlock this feature';
  ngOnInit() {

  }

  showNotif(event) {
    this.notificationService.addNotificationSuccess('Test of message');
    this.unlocked = true;
    this.title = 'Advanced sound feature';
  }

}
