<mat-card class="page">
    <h1 class="title">OVP</h1>
    <h4>Service selected : {{service.split(',')[0].split('\'')[1]}}</h4>
    <button mat-flat-button color="accent" (click)="toggleDisplayAllDetails()" style="margin-bottom: 20px">Hide / Show All details</button>

    <table mat-table [dataSource]="dataSource" matSort matSortActive="nb_user" matSortDirection="desc"
           matTableExporter #exporter="matTableExporter" style="margin-bottom: 20px;"
           class="mat-elevation-z8">
        <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header>Id</th>
            <td mat-cell *matCellDef="let element"> {{element.id}} </td>
        </ng-container>
        <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header>Title</th>
            <td mat-cell *matCellDef="let element"> {{element.title}} </td>
        </ng-container>
        <ng-container matColumnDef="details">
            <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header>Details</th>
            <td mat-cell *matCellDef="let element">
                <div (click)="toggleRow(element.id)" style="cursor: pointer;">
                <pre *ngIf="isSelectedRow(element.id)" [@fadeInOut]="'in'">{{ finalJson[element.id] | json }}</pre>
                <button mat-icon-button>
                    <mat-icon>{{ isSelectedRow(element.id) ? '' : 'expand_more' }}</mat-icon>
                </button>
            </div>
            </td>
        </ng-container>
        <ng-container matColumnDef="nb_user">
            <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header>User Number</th>
            <td mat-cell *matCellDef="let element"> {{element.nb_user}} </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    <mat-paginator [pageSize]="10" [pageSizeOptions]="[5, 10, 20, 30, 50, 100, 500, 1000]" showFirstLastButtons></mat-paginator>

    <button mat-raised-button
            (click)="exporter.exportTable('xlsx', {fileName:'Advanced-Analytics-OVP', sheet: 'sheet_name'})">Excel
    </button>
    <button mat-raised-button
            (click)="exporter.exportTable('csv', {fileName:'Advanced-Analytics-OVP', sheet: 'sheet_name'})">Csv
    </button>
    <button mat-raised-button
            (click)="exporter.exportTable('json', {fileName:'Advanced-Analytics-OVP', sheet: 'sheet_name'})">Json
    </button>
    <button mat-raised-button
            (click)="exporter.exportTable('txt', {fileName:'Advanced-Analytics-OVP', sheet: 'sheet_name'})">Txt
    </button>

</mat-card>
