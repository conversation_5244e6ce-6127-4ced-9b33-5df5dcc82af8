import { Component, OnInit, ViewChild } from '@angular/core';
import { GlobalConstants } from '../../common/global-constants';
import { HttpClient, HttpHeaders, HttpParams, HttpUrlEncodingCodec } from '@angular/common/http';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort, Sort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';
import { LiveAnnouncer } from '@angular/cdk/a11y';
import {animate, state, style, transition, trigger} from '@angular/animations';


export interface Line {
  id: string;
  title: string;
  nb_user: number;
}

let LINE_DATA: Line[] = [];

@Component({
  selector: 'app-ovp-stats',
  templateUrl: './ovp-stats.component.html',
  styleUrls: ['./ovp-stats.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('in', style({
        opacity: 1,
        height: '*',
        width: '100%'
      })),
      transition(':enter', [
        style({ opacity: 0, height: 0, width: 0 }),
        animate('300ms ease-out', style({ opacity: 1, height: '*', width: '100%' }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ opacity: 0, height: 0, width: 0 }))
      ])
    ])
  ]
})
export class OvpStatsComponent implements OnInit {

  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatPaginator) paginator: MatPaginator;

  timeZone = '';
  services = [];
  service = '';
  personalized;
  checkbox = [];
  var_to_return_tmp = '';
  interval = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  granu = {'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
  finalJson = {};
  displayedColumns: string[] = ['id', 'title', 'details', 'nb_user'];
  dataSource = new MatTableDataSource(LINE_DATA);
  selectedRows: string[] = [];
  displayAllDetails = false;
  topic = GlobalConstants.topicNginxPersonalized;


  constructor(private http: HttpClient, private _liveAnnouncer: LiveAnnouncer) { }

  ngOnInit(): void {

    if(localStorage.getItem('TimeZone'))
      this.timeZone = localStorage.getItem('TimeZone');

    this.dataSource.sortingDataAccessor = (data, sortHeaderId) => data[sortHeaderId].toLocaleLowerCase();

    this.getServices().then(res => {
      this.getTop50OVPVideos().then( result => {
        this.getTop50OVPVideosForElasticSearch(result).then(result2 => {
          this.getOvpInformationsFromES(result2);
        })
      })
    });
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
  }

  toggleRow(rowId: string) {
    const index = this.selectedRows.indexOf(rowId);
    if (index === -1) {
        this.selectedRows.push(rowId);
    } else {
        this.selectedRows.splice(index, 1);
    }
}

  isSelectedRow(rowId: string): boolean {
      if (this.displayAllDetails)
        return true;
      else
        return this.selectedRows.includes(rowId);
  }

  toggleDisplayAllDetails() {
    this.displayAllDetails = !this.displayAllDetails;
  }

  async getTop50OVPVideos() {

    for (let service of this.service.split(',')) {
      if (!service.includes('ovp')) {
        alert('Please select only a single OVP service');
        return
      }
    }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select playlist_id, count(DISTINCT remote_addr) from ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and service_id in (' + this.service + ') group by playlist_id ORDER by count(DISTINCT remote_addr) DESC limit 50')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select playlist_id, count(DISTINCT remote_addr) from ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and service_id in (' + this.service + ') group by playlist_id ORDER by count(DISTINCT remote_addr) DESC limit 50')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};
    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    return value;
  }


  async getTop50OVPVideosForElasticSearch(result) {
    let ovp_ids = {};
    for (let elem of result) {
      if (elem[0][0] == 'S')
        ovp_ids[elem[0].split('-')[0].split('S')[1].toString()] = elem[1];
      else
        ovp_ids[elem[0].toString()] = elem[1];
    }
    return ovp_ids
  }


  async getOvpInformationsFromES(arr) {

    let toQueryIds = '';
    Object.keys(arr).forEach((key) => {
      toQueryIds += key + ' ';
    });

    let index = (this.service.split('-')[1] + '_' + this.service.split('-')[2]).replace('\'', '');
    let body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
        .set('Request', '{"from" : 0, "size" : 50, "query": {"bool": {"must": [  {"match":{"Key":{"query":"' + toQueryIds + '"}}}]}}}')
        .set('Ovp_index', index);
    var result = await this.http.post(GlobalConstants.apiURL + 'log/ovp/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    let line;
    LINE_DATA = [];
    for (let elem of result['data']['hits']['hits']) {
      this.finalJson[elem['_id']] = elem['_source'];
      line = {"id": elem['_id'], "title": elem['_source']['Value'], "nb_user": arr[elem['_id']]};
      LINE_DATA.push(line);
    }
    this.dataSource = new MatTableDataSource(LINE_DATA);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    return;
  }

    // Function to get the user services
  async getServices() {
    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
      // if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.ngOnInit()
    }

    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }

    // CHECKED THE CHECKBOXES GRANULARITY
    let elemprec1;
    let prec1 = localStorage.getItem('granularity');
    for (let key in this.granu) {
      if (this.granu[key] == prec1) {
        elemprec1 = key;
      }
    }
    let granu1 = document.getElementById(elemprec1) as HTMLInputElement;
    if (granu1 !== null) {
      granu1.checked = true;
    }
    // CHECKED THE CHECKBOXES GRANULARITY

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      // window.location.reload();
    }
  }
}
