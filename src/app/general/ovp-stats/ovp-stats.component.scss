.result_timezone {
  text-align: center;
  margin-bottom: 20px;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

.sub_content {
  display: inline-block;
}

table {
  width: 100%;
}

th.mat-sort-header-sorted {
  color: black;
}

.mat-header-cell {
  text-align: center;
  color: #1d8ef1;
  max-width: 200px;
  min-width: 200px;
}

::ng-deep .mat-sort-header-container {
  justify-content: center;
}

::ng-deep .mat-select-panel {
  background-color: white;
}

table
{
  border-collapse: collapse;
}

th
{
  border: 1px solid black;
}

td
{
  border: 1px solid black;
  padding-right: 10px;
  padding-left: 10px;
}
