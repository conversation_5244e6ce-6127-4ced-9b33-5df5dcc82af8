import { Component, OnInit } from '@angular/core';
import { GlobalConstants } from '../../common/global-constants';
import { HttpClient, HttpHeaders, HttpParameterCodec, HttpParams } from '@angular/common/http';

export class HttpUrlEncodingCodec implements HttpParameterCodec {
    encodeKey(k: string): string {
        return standardEncoding(k);
    }

    encodeValue(v: string): string {
        return standardEncoding(v);
    }

    decodeKey(k: string): string {
        return decodeURIComponent(k);
    }

    decodeValue(v: string) {
        return decodeURIComponent(v);
    }
}

function standardEncoding(v: string): string {
    return encodeURIComponent(v);
}

@Component({
    selector: 'app-ovp',
    templateUrl: './ovp.component.html',
    styleUrls: ['./ovp.component.scss']
})

export class OvpComponent implements OnInit {

    ovp_key_correspondances = {"ovp_id": "Key", "ovp_external_id": "Value"}
    use_ovp_filter = true;
    show_ovp_datas = false;
    filters = {};
    filters_json = {};
    filtering_choices = ['match', 'range', 'gte', 'lte'];
    elastic_search_fields = [' - '];
    ovp_keywords;
    ovp_services = [];
    ovp_datas = [];
    ovp_scenes_result = [];

    constructor(private http: HttpClient) {
    }

    ngOnInit(): void {
        this.ovp_keywords = localStorage.getItem('ovpKeywords');
        if (!localStorage.getItem('use_ovp_filter')) {
            localStorage.setItem('use_ovp_filter', 'true');
        } else {
            this.use_ovp_filter = localStorage.getItem('use_ovp_filter') === 'true';
        }


        if (localStorage.getItem('filtersOvp')) {
            let values = localStorage.getItem('filtersOvp').split(', ');
            for (let elem of values) {
                this.filters[elem.split(' : ')[0]] = elem.split(' : ')[1];
            }
        }

        if (localStorage.getItem('filtersOvpJson')) {
            let values = localStorage.getItem('filtersOvpJson').split(', ');
            for (let elem of values) {
                this.filters_json[elem.split(' : ')[0]] = elem.split(' : ')[1];
            }
        }


        for (let elem of localStorage.getItem('services').split(',')) {
            if (elem.includes('ovp')) {
                // @ts-ignore
                elem = elem.replaceAll('\'', '');
                this.ovp_services.push(elem);
            }
        }
    }

    async sendQuery() {

        var indexes = (document.getElementById('index_select')) as HTMLSelectElement;
        if (indexes.selectedIndex != -1) {
            var index = this.ovp_services[indexes.selectedIndex].split('-')[1] + '_' + this.ovp_services[indexes.selectedIndex].split('-')[2];
        } else {
            alert('Please select an Index');
            return;
        }

        let query = this.createQuery(this.filters_json);
        let body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', query)
            .set('Ovp_index', index);

        var result = await this.http.post(GlobalConstants.apiURL + 'log/ovp/',
            body,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

        this.ovp_datas = result['data']['hits']['hits'];
        this.createWhereStatement();
    }

    async getFieldsOVP() {
        var indexes = (document.getElementById('index_select')) as HTMLSelectElement;
        if (indexes.selectedIndex === -1) {
            indexes.selectedIndex = 0;
        }
        this.elastic_search_fields = [' - ', 'All', 'ovp_id', 'ovp_external_id'];
        var index = this.ovp_services[indexes.selectedIndex].split('-')[1] + '_' + this.ovp_services[indexes.selectedIndex].split('-')[2];
        var result = await this.http.get(GlobalConstants.apiURL + 'log/ovp/mapping/' + index,
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();
        let test = result['data'][index]['mappings']['properties']['Metadata']['properties'];
        for (let elem in test) {
            this.elastic_search_fields.push('Metadata.' + elem);
        }
    }

    convertjson(data) {
        if (data != null) {
            let keys = Object.keys(data);
            let result = '';
            for (let key of keys) {
                result += key + ' : ' + data[key] + '|| ';
            }
            return result.split('||');
        } else {
            return null;
        }
    }

    getESRange() {
        var val = (document.getElementById('fieldText')) as HTMLSelectElement;
        var queryField = (document.getElementById('field_select')) as HTMLSelectElement;
        let field = this.elastic_search_fields[queryField.selectedIndex];
        let min = val.value.split(' ')[0];
        let max = val.value.split(' ')[1];
        let res = '{"range":{\"' + field + '\":{"gte" : \"' + min + '\", "lte":\"' + max + '\"}}}';
        this.filters[field] = min + '-' + max;
        this.filters_json[field] = res;
        this.saveFilterLocalStorage();
        return res;
    }

    getGTELTE(cmp) {
        var val = (document.getElementById('fieldText')) as HTMLSelectElement;
        var queryField = (document.getElementById('field_select')) as HTMLSelectElement;
        let field = this.elastic_search_fields[queryField.selectedIndex];
        let res = '{"range":{\"' + field + '\":{\"' + cmp + '\":\"' + val.value + '\"}}}';
        this.filters[field] = val.value;
        this.filters_json[field] = res;
        this.saveFilterLocalStorage();
        return res;
    }

    addMultiMatchStatement() {
        var value = (document.getElementById('fieldText')) as HTMLSelectElement;
        let val = value.value;
        if (val.trim().length === 0) {
            val = '';
        }
        let res = '{"multi_match":{"query":"' + val + '"}}';
        this.filters['All'] = val;
        this.filters_json['All'] = res;
        this.saveFilterLocalStorage();
        return res;
    }

    addFieldMatchStatement() {
        var queryText = (document.getElementById('fieldText')) as HTMLSelectElement;
        var queryField = (document.getElementById('field_select')) as HTMLSelectElement;
        let field = this.elastic_search_fields[queryField.selectedIndex];
        let value = queryText.value;
        let res = '  {"match":{\"' + field + '\":{"query":\"' + value + '\"}}}';
        this.filters[field] = value;
        this.filters_json[field] = res;
        this.saveFilterLocalStorage();
        return res;
    }


    createFilter() {
        var filter_choice = (document.getElementById('filter_select')) as HTMLSelectElement;
        var field = (document.getElementById('field_select')) as HTMLSelectElement;

        if (this.elastic_search_fields[field.selectedIndex] === 'All') {
            this.addMultiMatchStatement();
            return;
        }

        switch (this.filtering_choices[filter_choice.selectedIndex]) {
            case 'match': {
                this.addFieldMatchStatement();
                break;
            }
            case 'range': {
                this.getESRange();
                break;
            }
            case 'gte': {
                this.getGTELTE('gte');
                break;
            }
            case 'lte': {
                this.getGTELTE('lte');
                break;
            }
        }
    }

    createQuery(fields) {

        let keys = Object.keys(fields);
        let body = '';
        for (let key of keys) {
            if (Object.keys(this.ovp_key_correspondances).includes(key)) {
                fields[key] = fields[key].replace(key, this.ovp_key_correspondances[key])
            }
            body += fields[key] + ', ';
        }

        let start = '{"from" : 0, "size" : 1000, "query": {"bool": {"must": [';
        let end = ']}}}';
        return start + body.slice(0, -2) + end;

    }

    createWhereStatement() {
        let where_scene_statement = ' AND (';
        this.ovp_scenes_result = [];
        for (let scene of this.ovp_datas) {
            this.ovp_scenes_result.push(scene['_id']);
            where_scene_statement += 'playlist_id LIKE \'%' + scene['_id'] + '%\' OR ';
        }
        localStorage.setItem('ovp_where_statement', where_scene_statement.slice(0, -3) + ') ');
        return where_scene_statement.slice(0, -3);
    }

    selectIndex() {
        this.getFieldsOVP();
    }

    resetFilter() {
        this.filters_json = [];
        this.filters = {};
        this.ovp_datas = [];
        this.ovp_scenes_result = [];
        localStorage.removeItem('ovp_where_statement');
        localStorage.removeItem('filtersOvp');
        localStorage.removeItem('filtersOvpJson');
    }

    displayOvp() {
        this.use_ovp_filter = !this.use_ovp_filter;
        if (this.use_ovp_filter === true) {
            localStorage.setItem('use_ovp_filter', 'true');
        } else {
            localStorage.setItem('use_ovp_filter', 'false');
        }
    }

    saveFilterLocalStorage() {
        let resFilters = '';
        let resFiltersJson = '';
        for (let elem in this.filters) {
            resFilters += elem + ' : ' + this.filters[elem] + ', ';
        }

        if (Object.keys(this.filters).length > 0) {
            localStorage.setItem('filtersOvp', resFilters.slice(0, -2));
        } else {
            localStorage.removeItem('filtersOvp');
        }

        for (let elem in this.filters_json) {
            resFiltersJson += elem + ' : ' + this.filters_json[elem] + ', ';
        }

        if (Object.keys(this.filters_json).length > 0) {
            localStorage.setItem('filtersOvpJson', resFiltersJson.slice(0, -2));
        } else {
            localStorage.removeItem('filtersOvpJson');
        }

    }

    getElemOfFiltersArray() {

        let res = [];
        let keys = Object.keys(this.filters);
        for (let elem of keys)
            res.push(elem + ' : ' + this.filters[elem]);

        return res;
    }

    deleteFilter(elem) {
        delete this.filters[elem.split(' : ')[0]];
        delete this.filters_json[elem.split(' : ')[0]];
        this.saveFilterLocalStorage();
    }

}
