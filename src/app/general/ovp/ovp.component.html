<mat-drawer #drawer class="ovp-sidenav" mode="over" position="end">
    <h3 style="cursor: pointer" (click)="drawer.toggle()">></h3>
    <h1 class="title">OVP STATISTICS</h1>
    <h3>Index</h3>
    <select [ngModel]="selectIndex" id="index_select" name="indexes" (ngModelChange)="selectIndex()">
        <option [value]="index" *ngFor="let index of ovp_services">
            {{index}}
        </option>
    </select>
    <br><br>

    <h3>Fields</h3>
    <select id="field_select" name="fields" style="min-width: 100px">
        <option [value]="index" *ngFor="let index of elastic_search_fields">
            {{index}}
        </option>
    </select>
    <select id="filter_select" name="filter_select" style="min-width: 100px; margin-left: 20px">
        <option [value]="filter" *ngFor="let filter of filtering_choices">
            {{filter}}
        </option>
    </select>
    <input name="query" id="fieldText" style="margin-left: 20px; margin-right: 20px" autocomplete="off">
    <h5 style="text-decoration: underline; cursor: pointer" (click)="createFilter()">Save field</h5>

    <div *ngFor="let elem of getElemOfFiltersArray()">
        <button type="button" mat-flat-button color="accent"
                style="margin-top: 10px; font-size: 15px; cursor: default; padding-right: 0">{{elem}}
            <button type="button" mat-flat-button color="accent" style="cursor: pointer; margin: 0; padding: 0"
                    (click)="deleteFilter(elem)">x
            </button>
        </button>
    </div>
    <br><br>
    <button mat-flat-button color="accent" (click)="sendQuery()">Send query</button>
    <h5 style="text-decoration: underline; cursor: pointer; float: right; margin-right: 20px" (click)="resetFilter()">Reset</h5>
    <br><br>
    <hr>
    <br>
    <div *ngIf="ovp_scenes_result.length > 0">
        <h4>{{ovp_scenes_result.length}} Scene(s) selected</h4>
        <h5 style="text-decoration: underline; cursor: pointer" (click)="show_ovp_datas = !show_ovp_datas">Display
            datas</h5>
    </div>

    <div *ngIf="show_ovp_datas === true">
        <div *ngFor="let result of ovp_datas; let i = index">
            <a>N° {{i + 1}}</a>
            <h3>Key : </h3><a>{{result['_source']['Key']}} </a>
            <h3>Value : </h3><a>{{result['_source']['Value']}} </a>
            <h3>Metadata : </h3>
            <div *ngFor="let meta of convertjson(result['_source']['Metadata']); let i = index">
                <a>{{meta}}</a>
            </div>
            <br>
            <hr>
            <br>
        </div>
    </div>
</mat-drawer>

<button type="button" class="ovpBtn" mat-flat-button color="accent" (click)="drawer.toggle()">OVP Data Filter</button>
<mat-slide-toggle checked="{{use_ovp_filter}}" (change)="displayOvp()"></mat-slide-toggle>
<div *ngIf="use_ovp_filter" style="display: inline-block; margin-left: 20px">
    <h6>{{convertjson(filters)}}</h6>
</div>
