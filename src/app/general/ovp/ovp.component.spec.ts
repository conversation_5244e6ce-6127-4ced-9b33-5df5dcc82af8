import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { OvpComponent } from './ovp.component';

describe('OvpComponent', () => {
  let component: OvpComponent;
  let fixture: ComponentFixture<OvpComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ OvpComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OvpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
