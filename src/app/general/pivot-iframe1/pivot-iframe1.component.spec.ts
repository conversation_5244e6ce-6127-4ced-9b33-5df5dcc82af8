import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PivotIframe1Component } from './pivot-iframe1.component';

describe('PivotIframe1Component', () => {
  let component: PivotIframe1Component;
  let fixture: ComponentFixture<PivotIframe1Component>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PivotIframe1Component ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PivotIframe1Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
