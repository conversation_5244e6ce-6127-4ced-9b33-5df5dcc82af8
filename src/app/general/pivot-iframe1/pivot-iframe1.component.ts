import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-pivot-iframe1',
  templateUrl: './pivot-iframe1.component.html',
  styleUrls: ['./pivot-iframe1.component.scss']
})


export class PivotIframe1Component implements OnInit {

  request = {};
  view = 'dataCube';
  Defaulturl = 'https://hexaglobe.implycloud.com/p/ba585c37-0590-4e38-adb5-543919d6c796/pivot/i/134ba314867bf5ad9d/Websocket_Analytics_Session_Quality';
  path = '/mkurl-datacube';

  constructor() { }

  ngOnInit(): void {
  }


  // // Toggle between dashboard and dataCube
  // show(show){
  //   this.request = {};
  //   if(show) {
  //     this.view = 'dataCube';
  //
  //     // Set DataCube default Url
  //     this.Defaulturl = 'https://hexaglobe.implycloud.com/p/ba585c37-0590-4e38-adb5-543919d6c796/pivot/i/134ba314867bf5ad9d/Websocket_Analytics_Session_Quality';
  //
  //     this.path = '/mkurl-dataCube'
  //     document.getElementById('dropDown').style.display = 'block';
  //     document.getElementById('search').className = 'col-9';
  //   } else{
  //     this.view = 'dashboard'
  //     this.path = '/mkurl-dashboard'
  //
  //     // Set Dashboard default Url
  //     this.Defaulturl = 'https://hexaglobe.implycloud.com/p/ba585c37-0590-4e38-adb5-543919d6c796/pivot/c/cabc/Websocket_Events_Dashboard';
  //
  //     document.getElementById('dropDown').style.display = 'none';
  //     document.getElementById('search').className = 'col-12';
  //   }
  //   this.getUrl();
  // }
  //
  //
  // async getUrl() {
  //   let url = this.Defaulturl;
  //
  //   this.request.filterValue= document.getElementById('input').value;
  //
  //   // Fetch new Url
  //   let resp = await fetch(this.path, {
  //     method: 'POST',
  //     headers: {
  //       'Accept': 'application/json',
  //       'Content-Type': 'application/json'
  //     },
  //     body: JSON.stringify(this.request)
  //   })
  //
  //   let json = await resp.json();
  //
  //   // If the user input was not blank and a url was returned update the url
  //   if (json.url && (this.request.filterValue ||this.request.dimension)){
  //     url = json.url;
  //   }
  //
  //   // Set New Src
  //   document.getElementById('pivot').src = url;
  // }
}
