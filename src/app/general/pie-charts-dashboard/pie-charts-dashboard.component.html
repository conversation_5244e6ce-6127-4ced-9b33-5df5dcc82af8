<mat-card class="piechart">
    <mat-card class="pieChart_card">
        <mat-icon class="identity">devices</mat-icon>
        <h2 class="stat_title">Devices (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of users per devices" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="devices"></span>
            </div>
        </div>
        <div id="chartdiv1"></div>
    </mat-card>
    <mat-card class="pieChart_cardLiveVod">
        <mat-icon class="identity">live_tv</mat-icon>
        <h2 class="stat_title">LIVE / VOD (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of users per video type" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="live"></span>
            </div>
        </div>
        <div id="chartdivLiveVod1"></div>
    </mat-card>
</mat-card>

<mat-card class="graphMap">
    <mat-card class="graphTimeZone">
            <div id="chartdivTimeZone"></div>
            <h2 class="graphTitle">Top 10 Time Zone (by user) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of sessions for top 10 time zones" [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="zoneBySession"></span>
                </div>
            </div>
    </mat-card>
    <mat-card class="graphTimeZone2">
        <div id="chartdivCity"></div>
        <h2 class="graphTitle">Top 10 Cities (by user) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of sessions for top 10 cities" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="citiesBySession"></span>
            </div>
        </div>
    </mat-card>
</mat-card>
<mat-card class="graphMap">
    <mat-card class="CylinderCountries">
        <div id="chartdivCountryCylinder"></div>
        <h2 class="graphTitle">Top 10 Countries (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of users for top 10 countries" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="countriesByLogs"></span>
            </div>
        </div>
    </mat-card>
</mat-card>
<mat-card class="graphMap">
    <mat-card class="map">
        <div id="chartdivMap"></div>
        <h2 class="graphTitle">Top Countries Around World (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of users per countries" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="countriesWorld"></span>
            </div>
        </div>
    </mat-card>
    <mat-card class="mapFrance">
        <div id="chartdivMapFrance"></div>
        <h2 class="graphTitle">Country influence (by users) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Distribution of users per region" [matTooltipPosition]="'right'">info</mat-icon></h2>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="franceInfluence"></span>
            </div>
        </div>
    </mat-card>
</mat-card>
