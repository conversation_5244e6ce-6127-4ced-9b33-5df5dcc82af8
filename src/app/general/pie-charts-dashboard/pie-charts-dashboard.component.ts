import { Component, OnInit, Output } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import * as am4charts from '@amcharts/amcharts4/charts';
import * as am4maps from '@amcharts/amcharts4/maps'
import am4geodataWorldLow from "@amcharts/amcharts4-geodata/worldLow"
import franceLow from "@amcharts/amcharts4-geodata/franceLow"
import denmarkLow from "@amcharts/amcharts4-geodata/denmarkLow"
import germanLow from "@amcharts/amcharts4-geodata/germanyLow"
import russiaLow from "@amcharts/amcharts4-geodata/russiaLow"
import spainLow from "@amcharts/amcharts4-geodata/spainLow"
import usaLow from "@amcharts/amcharts4-geodata/usaLow"
import congoLow from "@amcharts/amcharts4-geodata/congoLow"
import ukLow from "@amcharts/amcharts4-geodata/ukCountiesLow"
import itLow from "@amcharts/amcharts4-geodata/italyLow"
import plLow from "@amcharts/amcharts4-geodata/polandLow"
import { HttpUrlEncodingCodec } from '@angular/common/http';
import{ GlobalConstants } from '../../common/global-constants';

function hide_devices() {
  document.getElementById('devices')
      .style.display = 'none';
}

function hide_live() {
  document.getElementById('live')
      .style.display = 'none';
}

function hide_zone() {
  document.getElementById('zoneBySession')
      .style.display = 'none';
}

function hide_countries() {
  document.getElementById('countriesByLogs')
      .style.display = 'none';
}

function hide_world() {
  document.getElementById('countriesWorld')
      .style.display = 'none';
}

function hide_cities() {
  document.getElementById('citiesBySession')
      .style.display = 'none';
}

function hide_france() {
  document.getElementById('franceInfluence')
      .style.display = 'none';
}


@Component({
  selector: 'app-pie-charts-dashboard',
  templateUrl: './pie-charts-dashboard.component.html',
  styleUrls: ['./pie-charts-dashboard.component.scss']
})
export class PieChartsDashboardComponent implements OnInit {

  service = '';
  topic = GlobalConstants.topicNginx;
  topic_session = GlobalConstants.topicNginxPersonalized;
  interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  var_to_return_tmp = '';
  services = [];
  checkbox = [];
  timeZone = '';
  total = 0;
  totalWorld = 0;
  geoname;


  constructor(private http: HttpClient) { }

  ngOnInit(): void {
  }

  launch(): void {
    this.service = localStorage.getItem('services');

    if(localStorage.getItem('TimeZone')) {
      this.timeZone = localStorage.getItem('TimeZone');
    }

    this.getServices().then(res => {
      this.getDevicePieChartInfo().then(arr => {
        this.displayPieChartDevices(arr);
      });
      this.getPieChartLiveVodInfo().then(arr2 => {
        this.displayPieChartLiveVod(arr2);
      });
      this.getTimeZoneInfo().then(res => {
        this.displayTimeZoneCylinder(res);
      });
      this.getWorldInfo().then(res2 => {
        this.displayMapWorld(res2);
      });
      this.getTopCityInfo().then(response => {
        this.displayCityCylinder(response);
      });
      this.getCountryInfo().then(re => {
        this.displayCountryCylinder(re);
        this.getMapCountryInfo().then(resultat => {
          this.displayMapCountry(resultat);
        });
      });
    });
  }

  async getPieChartLiveVodInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DISTINCT(\\"service_id\\"), count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY service_id ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 200)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));

    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DISTINCT(\\"service_id\\"), count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\'' + ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') GROUP BY service_id ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 200)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    let arr = [];
    for(let item of result['data']) {
      let view = item[0].split(/[-]+/).pop();
      if (arr[view] === undefined && view !== 'service_id' && view !== '') {
        arr[view] = item[1];
      } else if(view !== 'service_id' && view !== '') {
        arr[view] = arr[view] + item[1];
      }
    }
    let jsonArr = [];
    for(let item in arr) {
      jsonArr.push({
        'Device': item,
        'Number': arr[item]
      })
    }
    return jsonArr;
  }

  async getDevicePieChartInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select DISTINCT(\\"device_type\\"), count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY device_type LIMIT 10')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select DISTINCT(\\"device_type\\"), count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\'' + ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +' AND service_id IN (' + this.service + ') GROUP BY device_type LIMIT 10')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    let jsonArr = [];
    let arr = result['data'].slice(1)
    for(let item of arr) {
      if(item[1] !== 0) {
        if (item[0] === '')
          item[0] = 'Undefined';
        jsonArr.push({
          'Device': item[0],
          'Number': item[1]
        });
      }
    }
    return jsonArr;
  }

  async getMapCountryInfo() {
    let body;
    let country_indicator = this.geoname;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", \\"region_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') AND country_geoname_id = \'' + country_indicator + '\' GROUP BY \\"country_geoname_id\\", \\"region_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 20)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", \\"region_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\''+ ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') AND country_geoname_id = \''+ country_indicator + '\' GROUP BY \\"country_geoname_id\\", \\"region_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 20)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    result['data'] = result['data'].slice(1);
    let jsonArr = [];
    for(let item in result['data']) {
      jsonArr.push({
        'id': result['data'][parseInt(item)][0] + '-' + result['data'][parseInt(item)][1],
        'value': result['data'][parseInt(item)][2]
      });
    }
    return jsonArr;
  }


  async getWorldInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY \\"country_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 200)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\''+ ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') GROUP BY \\"country_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 200)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for(let item in result['data']) {
      if (!isNaN(result['data'][parseInt(item)][1]) && result['data'][parseInt(item)][0] != '') {
        this.totalWorld = this.totalWorld + Number(result['data'][parseInt(item)][1]);
      }
    }

    result['data'] = result['data'].slice(1);
    let jsonArr = [];
    for(let item in result['data']) {
      jsonArr.push({
        'id': result['data'][parseInt(item)][0],
        'value': result['data'][parseInt(item)][1]
      });
    }
    return jsonArr;
  }

  async getCountryInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY \\"country_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 11)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select \\"country_geoname_id\\", count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\''+ ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') GROUP BY \\"country_geoname_id\\" ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 11)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    result['data'] = result['data'].slice(1);
    let jsonArr = [];

    for(let item in result['data']) {
      if (!isNaN(result['data'][parseInt(item)][1]) && result['data'][parseInt(item)][0] != '') {
        this.total = this.total + Number(result['data'][parseInt(item)][1]);
      }
    }

    if (result['data'].length == 0) {
      return result['data'];
    }

    if (result['data'][0][0] != '')
      this.geoname = result['data'][0][0];
    else
      this.geoname = result['data'][1][0];

    for(let item in result['data']) {
      if (result['data'][parseInt(item)][0] === '') {
        result['data'][parseInt(item)][0] = "Undefined";
      }
      if (result['data'][parseInt(item)][0] != "Undefined") {
        jsonArr.push({
          'Country': result['data'][parseInt(item)][0],
          'Value': (result['data'][parseInt(item)][1] * 100) / this.total
        });
      }
    }
    return jsonArr;
  }

  async getTimeZoneInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select time_zone AS timezone, count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY time_zone ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 10)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select time_zone AS timezone, count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\''+ ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') GROUP BY time_zone ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 10)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    result['data'] = result['data'].slice(1);
    let jsonArr = [];
    for(let item in result['data']) {
      jsonArr.push({
        'City': result['data'][parseInt(item)][0],
        'Value': result['data'][parseInt(item)][1]
      });
    }
    return jsonArr;
  }

  async getTopCityInfo() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select country_geoname_id AS country, city_geoname_id AS city, count(DISTINCT(remote_addr)) from ' + this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') GROUP BY country_geoname_id, city_geoname_id ORDER BY count(DISTINCT(remote_addr)) DESC LIMIT 10)')
          .set('Cache', localStorage.getItem('cache'))
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select country_geoname_id AS country, city_geoname_id AS city, count(DISTINCT(\\"remote_addr\\")) from ' + this.topic + ' WHERE __time >= \'' + localStorage.getItem('start_date') + '\''+ ' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' + ' AND service_id IN (' + this.service + ') GROUP BY country_geoname_id, city_geoname_id ORDER BY count(DISTINCT(\\"remote_addr\\")) DESC LIMIT 10)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    result['data'] = result['data'].slice(1);
    let jsonArr = [];
    for(let item in result['data']) {
      if (result['data'][parseInt(item)][1] === '') {
        result['data'][parseInt(item)][1] = "Undefined";
      }
      if (result['data'][parseInt(item)][1] != "Undefined") {
        jsonArr.push({
          'City': result['data'][parseInt(item)][0] + ' / ' + result['data'][parseInt(item)][1],
          'Value': result['data'][parseInt(item)][2]
        });
      }
    }
    return jsonArr;
  }

  displayPieChartLiveVod(arr) {
    hide_live();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivLiveVod1", am4charts.PieChart);
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsLiveVSVod";

    let title = 'Live vs VOD (by users) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in

    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "Number";
    pieSeries.dataFields.category = "Device";

    chart.innerRadius = am4core.percent(30);

    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 2;
    pieSeries.slices.template.strokeOpacity = 1;
    pieSeries.slices.template
        .cursorOverStyle = [
      {
        "property": "cursor",
        "value": "pointer"
      }
    ];
    pieSeries.alignLabels = false;
    pieSeries.labels.template.bent = false;
    pieSeries.labels.template.radius = 10;
    pieSeries.labels.template.padding(10,10,10,10);
    pieSeries.ticks.template.disabled = true;
    pieSeries.labels.template.opacity = 0;

    var shadow = pieSeries.slices.template.filters.push(new am4core.DropShadowFilter);
    shadow.opacity = 0;
    var hoverState = pieSeries.slices.template.states.getKey("hover");
    var hoverShadow = hoverState.filters.push(new am4core.DropShadowFilter);
    hoverShadow.opacity = 0.7;
    hoverShadow.blur = 5;
    chart.legend = new am4charts.Legend();
    chart.data = arr;
  }

  displayPieChartDevices(arr) {
    hide_devices();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv1", am4charts.PieChart);
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsDevicesPiedChart";

    let title = 'Devices (by users) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "Number";
    pieSeries.dataFields.category = "Device";

    chart.innerRadius = am4core.percent(30);

    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 2;
    pieSeries.slices.template.strokeOpacity = 1;
    pieSeries.slices.template
        .cursorOverStyle = [
      {
        "property": "cursor",
        "value": "pointer"
      }
    ];
    pieSeries.alignLabels = false;
    pieSeries.labels.template.bent = false;
    pieSeries.labels.template.radius = 10;
    pieSeries.labels.template.padding(10,10,10,10);
    pieSeries.ticks.template.disabled = true;
    pieSeries.labels.template.opacity = 0;

    var shadow = pieSeries.slices.template.filters.push(new am4core.DropShadowFilter);
    shadow.opacity = 0;
    var hoverState = pieSeries.slices.template.states.getKey("hover"); // normally we have to create the hover state, in this case it already exists
    var hoverShadow = hoverState.filters.push(new am4core.DropShadowFilter);
    hoverShadow.opacity = 0.7;
    hoverShadow.blur = 5;
    chart.legend = new am4charts.Legend();
    chart.data = arr;
  }

  validate() {
    this.launch();
  }

  async getServices() {

    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initialization');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      this.launch()
    }
  }

  displayTimeZoneCylinder(arr) {
    hide_zone();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivTimeZone", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsTiemzones";

    let title = 'Top 10 Timezones (by session) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "City";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Value";
    series.dataFields.categoryX = "City";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayCountryCylinder(arr) {
    hide_countries();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivCountryCylinder", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsCountries";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    chart.numberFormatter.numberFormat = "#.##'%'";

    let title = 'Top 10 Countries (by users) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Country";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Value";
    series.dataFields.categoryX = "Country";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayCityCylinder(arr) {
    hide_cities();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivCity", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsCities";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Top 10 Cities (by session) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "City";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Value";
    series.dataFields.categoryX = "City";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    chart.cursor = new am4charts.XYCursor();
  }

  displayMapWorld(res2) {

    for (let elem of res2) {
      if (Number((elem['value'] * 100) / this.totalWorld) < 0.3)
        elem['value'] = null;
    }

    hide_world();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivMap", am4maps.MapChart);
    chart.geodata = am4geodataWorldLow;
    chart.projection = new am4maps.projections.Miller();
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsWorldMap";

    let title = 'Top Countries Around World (by users)  - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in

    var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());
    polygonSeries.exclude = ["AQ"];
    polygonSeries.heatRules.push({
      property: "fill",
      target: polygonSeries.mapPolygons.template,
      min: chart.colors.getIndex(1).brighten(1),
      max: chart.colors.getIndex(1).brighten(-0.3)
    });
    polygonSeries.useGeodata = true;
    polygonSeries.data = res2;

    let heatLegend = chart.createChild(am4maps.HeatLegend);
    heatLegend.series = polygonSeries;
    heatLegend.align = "right";
    heatLegend.valign = "bottom";
    heatLegend.width = am4core.percent(80);


    heatLegend.marginRight = am4core.percent(4);
    heatLegend.minValue = 0;
    heatLegend.maxValue = 40000000;

    var minRange = heatLegend.valueAxis.axisRanges.create();
    minRange.label.text = "0";
    var maxRange = heatLegend.valueAxis.axisRanges.create();
    maxRange.value = heatLegend.maxValue;

    try {
      maxRange.label.text = res2[0]["value"];
    } catch (e) {}

    heatLegend.valueAxis.renderer.labels.template.adapter.add("text", function (labelText) {
      return "";
    });

    var polygonTemplate = polygonSeries.mapPolygons.template;
    polygonTemplate.tooltipText = "{name}: {value}";
    polygonTemplate.nonScalingStroke = true;
    polygonTemplate.strokeWidth = 0.5;

    var hs = polygonTemplate.states.create("hover");
    hs.properties.fill = am4core.color("#3c5bdc");
  }

  displayMapCountry(res) {
    hide_france();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivMapFrance", am4maps.MapChart);

    if (this.geoname == 'FR')
      chart.geodata = franceLow;
    else if (this.geoname == 'DK')
      chart.geodata = denmarkLow;
    else if (this.geoname == 'DE')
      chart.geodata = germanLow;
    else if (this.geoname == 'RU')
      chart.geodata = russiaLow;
    else if (this.geoname == 'GB')
      chart.geodata = ukLow;
    else if (this.geoname == 'US')
      chart.geodata = usaLow;
    else if (this.geoname == 'CD')
      chart.geodata = congoLow;
    else if (this.geoname == 'IT')
      chart.geodata = itLow;
    else if (this.geoname == 'PL')
      chart.geodata = plLow;

    chart.projection = new am4maps.projections.Miller();
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsRegions";

    let title = 'Country influence (by users) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
    pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
        fontSize: 15,
        bold: true,
        }
    });
    // Add logo
    pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
    });
    return pdf;
    });
    chart.exporting.menu.items = [{
    "label": "...",
    "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
    ]
    }]; // Set the formats we want to allow the user to export in

    var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());
    polygonSeries.heatRules.push({
      property: "fill",
      target: polygonSeries.mapPolygons.template,
      min: chart.colors.getIndex(1).brighten(1),
      max: chart.colors.getIndex(1).brighten(-0.3)
    });
    polygonSeries.useGeodata = true;
    polygonSeries.data = res;

    let heatLegend = chart.createChild(am4maps.HeatLegend);
    heatLegend.series = polygonSeries;
    heatLegend.align = "right";
    heatLegend.valign = "bottom";
    heatLegend.width = am4core.percent(80);
    heatLegend.marginRight = am4core.percent(4);
    heatLegend.minValue = 0;
    heatLegend.maxValue = 40000000;

    var minRange = heatLegend.valueAxis.axisRanges.create();
    minRange.value = heatLegend.minValue;
    minRange.label.text = "0";
    var maxRange = heatLegend.valueAxis.axisRanges.create();
    maxRange.value = heatLegend.maxValue;
    try {
      maxRange.label.text = res[0]["value"];
    } catch (e) {}

    heatLegend.valueAxis.renderer.labels.template.adapter.add("text", function (labelText) {
      return "";
    });

    var polygonTemplate = polygonSeries.mapPolygons.template;
    polygonTemplate.tooltipText = "{name}: {value}";
    polygonTemplate.nonScalingStroke = true;
    polygonTemplate.strokeWidth = 0.5;

    var hs = polygonTemplate.states.create("hover");
    hs.properties.fill = am4core.color("#3c5bdc");
  }
}
