import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PieChartsDashboardComponent } from './pie-charts-dashboard.component';

describe('PieChartsDashboardComponent', () => {
  let component: PieChartsDashboardComponent;
  let fixture: ComponentFixture<PieChartsDashboardComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PieChartsDashboardComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PieChartsDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
