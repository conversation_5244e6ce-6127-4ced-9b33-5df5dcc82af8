#chartdiv1 {
  margin-right: auto;
  margin-left: auto;
  width: 50%;
  height: 500px;
  padding-bottom: 20px;

}

#chartdivLiveVod1 {
  margin-right: auto;
  margin-left: auto;
  width: 50%;
  height: 500px;
  padding-bottom: 20px;
}

.pieChart_card {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-right: 20px;
  background: white;
}

.pieChart_cardLiveVod {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-left: 20px;
  background: white;
}

.piechart {
  display: flex;
  background-color: whitesmoke;
}

.graphMap {
  background-color: whitesmoke;
  display: flex;
}

.stat_title {
  text-align: center;
  display: inline-block;
  padding-left: 60px;
  text-decoration: underline;
}

.identity {
  font-size: 50px;
  position: absolute;
}

#chartdivTimeZone {
  width: 100%;
  height: 500px;
}

#chartdivCity {
  width: 100%;
  height: 500px;
}

#chartdivCountryCylinder {
  width: 100%;
  height: 500px;
}

.graphTimeZone {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-right: 20px;
}

.graphTimeZone2 {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  width: 50%;
  margin-left: 20px;
}

.CylinderCountries {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  width: 100%;
}

.map {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  margin-right: 20px;
  width: 50%;
}

.mapFrance {
  border-style: solid;
  border-width: thin;
  text-align: center;
  display: inline-block;
  margin-left: 20px;
  width: 50%;
}

#chartdivMap {
  width: 100%;
  height: 500px;
}

#chartdivMapFrance {
   width: 100%;
   height: 500px;
 }

#devices, #live, #zoneBySession, #countriesByLogs, #countriesWorld, #citiesBySession, #franceInfluence {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}