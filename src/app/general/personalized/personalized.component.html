<mat-card class="page">
    <div class="personalizedTitleGranularity">
        <h1 class="title">PERSONALIZED STATISTICS</h1>
        <div class="precision">
            <div *ngIf="personalized === '1'  || start != null">
                <nav>
                    <button class="precision_btn1" mat-stroked-button color="accent">Granularity
                        <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                    </button>
                    <div class="dropdown-content1">
                        <ul>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu0" value="Value1"
                                    (click)="selectGranularity('CheckGranu0')"/>Minute
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu1" value="Value1"
                                    (click)="selectGranularity('CheckGranu1')"/>Hour
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu2" value="Value1"
                                    (click)="selectGranularity('CheckGranu2')"/>Day
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu3" value="Value1"
                                    (click)="selectGranularity('CheckGranu3')"/>Month
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu4" value="Value1"
                                    (click)="selectGranularity('CheckGranu4')"/>Year
                                <span class="checkmark"></span></label></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <h4>Number of services selected : {{service.split(',').length}}</h4>

    <mat-card class="fields_card">
        <h3>Dimensions :</h3>
        <div #fieldsList="cdkDropList" cdkDropList class="fields-list"
             [cdkDropListConnectedTo]="[doneList, filterList]"
             [cdkDropListData]="fields"
             (cdkDropListDropped)="drop($event)">
            <button mat-flat-button color="accent" class="field-box" *ngFor="let field of fields" cdkDrag>{{field}}</button>
        </div>
    </mat-card>

    <mat-card class="measures_card">
        <div style="display: flex;">
            <mat-card style="display: flex;">
                <div class="measure">
                    <nav>
                        <button class="measure_btn" mat-stroked-button color="accent">Measures
                            <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                        </button>
                        <div class="dropdown-content">
                            <div class="checkboxesMeasures" *ngFor="let measure of measures; let i = index">
                                <ul>
                                    <input type="radio" style="cursor:pointer" id="{{ 'checkMeasure' + i }}"
                                        (change)="onChangeMeasure(i)">
                                    <label class="container" for="{{ 'checkMeasure' + i }}">{{ measure }}</label>
                                </ul>
                            </div>
                        </div>
                    </nav>
                </div>
                <div class="display">
                    <nav>
                        <button class="display_btn" mat-stroked-button color="accent">Display
                            <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                        </button>
                        <div class="dropdown-content">
                            <div class="checkboxesDisplays" *ngFor="let display of display_type; let i = index">
                                <ul>
                                    <input type="radio" style="cursor:pointer" id="{{ 'displayType' + i }}"
                                        (change)="onChangeDisplay(i)">
                                    <label class="container" for="{{ 'displayType' + i }}">{{ display }}</label>
                                </ul>
                            </div>
                        </div>
                    </nav>
                </div>
            </mat-card>
            <div style="margin-left: auto; margin-right: 10px; align-items: center; display: flex; width: 100%; justify-content: center;">
                <button style="margin-left: 20px; background-color: #09802d; width: 120px" mat-raised-button color="accent" (click)="reload()">Validate</button>
            </div>
        </div>

        <mat-card style="margin-top: 10px">
            <p>Global filters :</p>
            <div class="globalFilterSelect" (clickOutside)="closeFilterValueSelector()">
                <div #filterList="cdkDropList" cdkDropList cdkDropListOrientation="horizontal" class="globalFilterField"
                    [cdkDropListConnectedTo]="[fieldsList]"
                    [cdkDropListData]="globalFilterSelectedField"
                    (cdkDropListDropped)="dropFilter($event)">
                    <button mat-flat-button color="accent" class="filter-box" matTooltip="Click me to list {{field}} data"
                            (click)="selectActiveFilter(field)" *ngFor="let field of globalFilterSelectedField"
                            cdkDrag>{{field}}
                        <mat-icon (click)="selectGlobalFilter(fieldsGlobalFilter.indexOf(field.split('\'').join('')))"
                                style="color: white !important; cursor: default">highlight_off
                        </mat-icon>
                    </button>
                </div>
                <div id="drop" class="dropdown-content" style="display: none;">
                    <input id="searchInField" style="height: 30px; font-size: 15px;" (input)="search()" autocomplete="off"
                        placeholder="Search...">
                    <div class="checkboxesDisplays" *ngFor="let filter of fieldsGlobalFilterSelect; let i = index">
                        <ul>
                            <label class="container">
                                <li><input type="checkbox" id="{{ 'valuefilter' + i }}"
                                        (change)="changeTheGlobalFilter(i, filter)">
                                    <span class="checkmark"></span>
                                    {{ filter }}</li>
                            </label>
                        </ul>
                    </div>
                </div>
                <p>Selected filters :</p>
            </div>

            <div #filterList="cdkDropList" cdkDropList cdkDropListOrientation="horizontal" class="globalFilterList"
                [cdkDropListConnectedTo]="fieldsList"
                [cdkDropListData]="filterValues"
                (cdkDropListDropped)="dropFilter($event)">
                <button mat-flat-button color="accent" class="filter-box" *ngFor="let field of filterValues"
                        cdkDrag>{{field.split('\'').join('')}}
                    <mat-icon
                            (click)="changeTheGlobalFilter(fieldsGlobalFilterSelect.indexOf(field.split('\'').join('')), field)"
                            style="color: white !important; cursor: default">highlight_off
                    </mat-icon>
                </button>
            </div>
        </mat-card>
    </mat-card>
</mat-card>


    <mat-card style="display: flex; position: static; margin-top: 20px;">
        <mat-card style="display: inline-block; width: 55%;">
            <h3>Drag and drop dimensions below</h3>
            <div #doneList="cdkDropList" cdkDropList cdkDropListOrientation="horizontal" class="order-list"
                [cdkDropListConnectedTo]="[fieldsList]"
                [cdkDropListData]="selected_fields"
                (cdkDropListDropped)="drop($event)">
                <button mat-flat-button color="accent" class="order-box" *ngFor="let field of selected_fields"
                        cdkDrag>{{field}}
                    <mat-icon (click)="removeItems(field)" style="color: white !important; cursor: default">highlight_off</mat-icon>
                </button>
            </div>
        </mat-card>

        <div style="margin-left: 20px; display: inline-grid;">
            <mat-card class="bookmarkCard">
                <div class="displayBookmark">
                    <nav>
                        <button class="display_btn" mat-stroked-button color="accent">Bookmarks
                            <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                        </button>
                        <div class="dropdown-contentBookmark">
                            <div *ngFor="let bookmark of bookmarks; let i = index">
                                <div id="wrapperBookmark">
                                    <mat-card style="display: inline-block; margin-right: 5px; flex: 0.98"
                                            matTooltip="{{bookmark['value']}}" matTooltipClass="tooltipBookmark"
                                            id="bookmarkList">
                                        <label (click)="redirectToBookmark(bookmark['value'])" class="containerBookmark">
                                            <li>{{ bookmark['name'] }}</li>
                                        </label>
                                    </mat-card>
                                    <mat-card style="display: inline-block; position:relative;"
                                            matTooltip="Delete bookmark">
                                        <mat-icon
                                                style="color: white !important; display: inline-block; flex: 0.01; top: 50%; margin: 0; position: absolute"
                                                (click)="deleteBookmark(bookmark['public_id'])" style="cursor: pointer">
                                            highlight_off
                                        </mat-icon>
                                    </mat-card>
                                </div>
                            </div>
                        </div>
                    </nav>
                </div>
                <div class="display-bookmark">
                        <div class="save_bookmark">
                            <form [formGroup]="bookmarkForm" style="display: flex;" (ngSubmit)="onSubmitBookmark()">
                                <input id="name" placeholder=" New bookmark name" type="text" formControlName="name"
                                    style="border-radius: 4px; margin-right: 10px;"><br><br>
                                <button mat-flat-button color="accent" class="button" type="submit">Save my Bookmark</button>
                            </form>
                        </div>
                </div>
            </mat-card>
            <app-ovp></app-ovp>
        </div>
    </mat-card>

<mat-card class="result_array">
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="sr-only" id="graph"></span>
        </div>
    </div>

    <div *ngIf="displayChoose == '0' && selected_fields.length > 0">
        <div class="filter-mat-table-header">
            <mat-form-field>
                <input matInput (keyup)="applyFilter($event.target.value)" placeholder="Search">
            </mat-form-field>
            <div *ngIf="consumption_value != ''" class="displayUnit">
                <nav>
                    <button mat-flat-button color="accent">Unit of measure</button>
                    <div class="dropdown-contentUnit">
                        <div *ngFor="let measure of consumption_values; let i = index">
                            <mat-card style="margin-top: 5px; cursor: pointer" (click)="changeUnit(measure)">
                                <div>
                                    {{measure}}
                                </div>
                            </mat-card>
                        </div>
                    </div>
                </nav>
            </div>
            <button mat-flat-button color="accent" class="data-format" style="float: right;" (click)="formatDownload()">
                Detailed percentages
            </button>
        </div>
        <!--    <button mat-flat-button color="accent" (click)="getCsvContent(result, result2, result3, result4, result5, result6) && getcsv()" class="csv_button">Download in csv</button>-->
        <div class="RESULT" [hidden]="selected_fields[val] == undefined">
            <table mat-table [dataSource]="dataSource" matSort matSortActive="result1" matSortDirection="asc"
                   matTableExporter #exporter="matTableExporter"
                   class="mat-elevation-z8">
                <ng-container matColumnDef="result1">
                    <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[0]}} </th>
                    <td mat-cell matTooltip="Data for {{selected_fields[0]}}"
                        *matCellDef="let element; let i = index">{{formatNumber(element.result1, 0)}} </td>
                </ng-container>
                <ng-container matColumnDef="result2">
                    <div *ngIf="selected_fields[1] != undefined; else elsep2">
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[1]}} </th>
                    </div>
                    <ng-template #elsep2>
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer"
                            mat-sort-header> {{ measure_to_display[measure] + (consumption_value !== "" ? " (" + consumption_value + ")" : "") }}</th>
                    </ng-template>
                    <td mat-cell matTooltip="Data for {{element.result1}}"
                        *matCellDef="let element; let i = index">{{formatNumber(element.result2, 1)}} </td>
                </ng-container>

                <ng-container matColumnDef="result3">
                    <div *ngIf="selected_fields[2] != undefined; else elsep3">
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[2]}} </th>
                    </div>
                    <ng-template #elsep3>
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer"
                        mat-sort-header> {{ measure_to_display[measure] + (consumption_value !== "" ? " (" + consumption_value + ")" : "") }}</th>
                    </ng-template>
                    <td mat-cell matTooltip="Data for ({{element.result1}} - {{formatNumber(element.result2, 1)}})"
                        *matCellDef="let element; let i = index">{{formatNumber(element.result3, 2)}} </td>
                </ng-container>
                <!--        <div *ngIf="1 == 0">-->
                <ng-container matColumnDef="result4">
                    <div *ngIf="selected_fields[3] != undefined; else elsep4">
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[3]}} </th>
                    </div>
                    <ng-template #elsep4>
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer"
                        mat-sort-header> {{ measure_to_display[measure] + (consumption_value !== "" ? " (" + consumption_value + ")" : "") }}</th>
                    </ng-template>
                    <td mat-cell matTooltip="Data for {{element.result1}}"
                        *matCellDef="let element; let i = index">{{formatNumber(element.result4, 3)}} </td>
                </ng-container>
                <!--        </div>-->

                <ng-container matColumnDef="result5">
                    <div *ngIf="selected_fields[4] != undefined; else elsep5">
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[4]}} </th>
                    </div>
                    <ng-template #elsep5>
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer"
                        mat-sort-header> {{ measure_to_display[measure] + (consumption_value !== "" ? " (" + consumption_value + ")" : "") }}</th>
                    </ng-template>
                    <td mat-cell matTooltip="Data for {{element.result1}}"
                        *matCellDef="let element; let i = index">{{formatNumber(element.result5, 4)}} </td>
                </ng-container>

                <ng-container matColumnDef="result6">
                    <div *ngIf="selected_fields[5] != undefined; else elsep6">
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> {{selected_fields[5]}} </th>
                    </div>
                    <ng-template #elsep6>
                        <th mat-header-cell *matHeaderCellDef style="cursor: pointer"
                        mat-sort-header> {{ measure_to_display[measure] + (consumption_value !== "" ? " (" + consumption_value + ")" : "") }}</th>
                    </ng-template>
                    <td mat-cell matTooltip="Data for {{element.result1}}"
                        *matCellDef="let element">{{formatNumber(element.result6, 5)}} </td>
                </ng-container>

                <ng-container matColumnDef="resultPercentage">
                    <th mat-header-cell *matHeaderCellDef style="cursor: pointer" mat-sort-header> Total percentage</th>
                    <td mat-cell matTooltip="Data for total" *matCellDef="let element">{{element.resultPercentage}}%
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>
        <mat-paginator [pageSizeOptions]="[50, 5, 10, 20, 30, 100, 500, 1000]" showFirstLastButtons></mat-paginator>

        <button mat-raised-button
                (click)="exporter.exportTable('xlsx', {fileName:'AdvancedAnalyticsPersonalized', sheet: 'sheet_name'})">Excel
        </button>
        <button mat-raised-button
                (click)="exporter.exportTable('csv', {fileName:'AdvancedAnalyticsPersonalized', sheet: 'sheet_name'})">Csv
        </button>
        <button mat-raised-button
                (click)="exporter.exportTable('json', {fileName:'AdvancedAnalyticsPersonalized', sheet: 'sheet_name'})">Json
        </button>
        <button mat-raised-button
                (click)="exporter.exportTable('txt', {fileName:'AdvancedAnalyticsPersonalized', sheet: 'sheet_name'})">Txt
        </button>
    </div>
</mat-card>


<mat-card *ngIf="displayChoose != '0'" class="chartdivmatcard">
    <mat-card-content>
        <div *ngIf="consumption_value != ''" class="displayUnit">
            <nav>
                <button mat-flat-button color="accent">Unit of measure</button>
                <div class="dropdown-contentUnit">
                    <div *ngFor="let measure of consumption_values; let i = index">
                        <mat-card style="margin-top: 5px; cursor: pointer" (click)="changeUnit(measure)">
                            <div>
                                {{measure}}
                            </div>
                        </mat-card>
                    </div>
                </div>
            </nav>
        </div>
        <h3>{{measure_to_display[measure]}} {{this.consumption_value}} / {{graphTimeGranularity}}</h3>
        <div *ngIf="selected_fields.length > 0">
            <div id="chartdivP"></div>
        </div>
    </mat-card-content>
</mat-card>
