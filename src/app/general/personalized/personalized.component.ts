import { Component, LOCALE_ID, OnInit, ViewChild } from '@angular/core';
import { GlobalConstants } from '../../common/global-constants';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { ConsumptionComponent } from '../consumption/consumption.component';
import { DOCUMENT } from '@angular/common';
import { DashboardComponent } from '../dashboard/dashboard.component';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import * as am4charts from '@amcharts/amcharts4/charts';
import { ActivatedRoute, Router } from '@angular/router';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { CdkDragDrop, copyArrayItem, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { FormBuilder } from '@angular/forms';
import { HttpUrlEncodingCodec } from '@angular/common/http';
import { NavbarComponent } from '../../layout/navbar/navbar.component';


function hide_graph_load() {
  document.getElementById('graph')
      .style.display = 'none';
}

function show_graph_load() {
  document.getElementById('graph')
      .style.display = 'inline';
}

function getDateGraphTime(elem) {
  let date;
  if (elem[1].toString().length < 2) {
    elem[1] = '0' + elem[1].toString();
  }
  if ((elem[3] != undefined || elem[3] != null) && elem[3].toString().length < 2) {
    elem[3] = '0' + elem[3].toString();
  }
  if ((elem[4] != undefined || elem[4] != null) && elem[4].toString().length < 2) {
    elem[4] = '0' + elem[4].toString();
  }
  if ((elem[5] != undefined || elem[5] != null) && elem[5].toString().length < 2) {
    elem[5] = '0' + elem[5].toString();
  }

  if (elem.length == 4) {
    date = '\'' + elem[3] + '-' + elem[1] + '-01T00:00:00';
  } else if (elem.length == 5) {
    date = '\'' + elem[4] + '-' + elem[3] + '-' + elem[1] + 'T00:00:00';
  } else if (elem.length == 6) {
    date = '\'' + elem[5] + '-' + elem[4] + '-' + elem[3] + 'T' + elem[1] + ':00:00';
  } else if (elem.length == 7) {
    date = '\'' + elem[6] + '-' + elem[5] + '-' + elem[4] + 'T' + elem[3] + ':' + elem[1] + ':00';
  } else if (elem.length == 3) {
    date = '\'' + elem[1] + '-' +  '01-01T00:00:00';
  }
  return date;
}

export interface Line {
  result1: string;
  result2: string;
  result3: string;
  result4: string;
  result5: string;
  result6: string;
  resultPercentage: string;
}

let LINE_DATA: Line[] = [];

@Component({
  selector: 'app-personalized',
  templateUrl: './personalized.component.html',
  styleUrls: ['./personalized.component.scss']
})
export class PersonalizedComponent implements OnInit {

  val;
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource(LINE_DATA);
  weAreInUrlData = true;
  topic = "\\\"all-filtered-nginx-log-entries-Xm\\\""
  // topic = GlobalConstants.topicNginxPersonalized;
  topic_viewing = GlobalConstants.topicViewing;
  topic_optimize = GlobalConstants.topicNginx;
  actual_topic;
  checkbox = [];
  result = [];
  result2 = [];
  result3 = [];
  result4 = [];
  result5 = [];
  result6 = [];
  resultPercentage = [];
  unique = [];
  measure = '';
  var_to_return_tmp = '';
  selected_fields = [];
  services = [];
  service = '';
  dash = new DashboardComponent(this.http);
  navbar = new NavbarComponent(null, this.http, null);
  granularity;
  where_global_filter = "";
  accept_optimize = ["asn", "browser_type", "device_type", "service_id", "client", "city_geoname_id", "country_geoname_id", "region_geoname_id", "status", "quality", "ssl_protocol", "ssl_cipher", "is_ipv6", "remote_addr"];
  granu = {'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  interval2 = {'HOUR': '\'2\' HOUR', 'DAY': '\'2\' DAY', 'WEEK': '\'14\' DAY', 'MONTH': '\'2\' MONTH', 'YEAR': '\'2\' YEAR'};
  timeZone = '';
  consumption_values_correspondance = {'Bytes': 1, 'MB': 1000000, 'GB': 1000000000, 'TB': 1000000000000};
  consumption_values = ["Bytes", "MB", "GB", "TB"];
  consumption_value = "";
  precision_display = '';
  fields = ["asn", "browser_type", "connection_type", "device_type", "playlist_id", "service_id", "client", "language", "city_geoname_id", "country_geoname_id", "region_geoname_id", "os_family", "os_name", "extension", "fromhost", "host", "http_accept_language", "quality",  "status", "ua_family", "upstream_addr", "upstream_cache_status", "http_referer", "ssl_protocol", "ssl_cipher", "is_ipv6", "remote_addr", "request_filename", "matched_regex"];
  fieldsGlobalFilter = ["none", "asn", "browser_type", "connection_type", "device_type", "playlist_id", "service_id", "client", "language", "city_geoname_id", "country_geoname_id", "region_geoname_id", "os_family", "os_name", "extension", "fromhost", "host", "http_accept_language", "quality",  "status", "ua_family", "upstream_addr", "upstream_cache_status", "http_referer", "ssl_protocol", "ssl_cipher", "is_ipv6", "remote_addr", "request_filename", "matched_regex"];
  fieldsGlobalFilterSelect = [];
  globalFilterSelectedField = [];
  bookmarks;
  filterFieldsMap = new Map();
  to_encode = false;

  // fieldsGlobalFilterSelectComplete = [];
  globalFilter = "";
  measures = ["Nb user", "Nb logs", "Data Body bytes sent", "Data bytes sent", "Nb Session", "Average session duration", "Total session duration", "Total second consumption", "Average segment load duration", "Average upstream response time", "Average upstream bandwidth", "Average duration between request and file generation",];
  measure_correspondance = {'Nb user': 'count(DISTINCT(remote_addr))', 'Nb logs': 'SUM(\\"count\\")', 'Data Body bytes sent': 'sum(sum_body_bytes_sent)', 'Data bytes sent': 'sum(sum_bytes_sent)', 'Nb Session': ' count(DISTINCT(remote_addr)) ', 'Average session duration': 'AVG(sum_viewing_duration_class_s)', 'Total session duration': 'SUM(sum_viewing_duration_class_s)', 'Total second consumption': 'SUM(sum_duration_s)', 'Average segment load duration': 'AVG(sum_request_time / \"count\")', 'Average upstream response time': 'AVG(sum_upstream_response_time)', 'Average upstream bandwidth': 'AVG(upstream_bandwith)', 'Average duration between request and file generation': 'AVG(delay_btw_req_and_filegen)'};
  measure_to_display = {'count(DISTINCT(remote_addr))': 'Nb user', 'SUM(\\"count\\")': 'Nb logs', 'sum(sum_body_bytes_sent)': 'Data Body bytes sent', 'sum(sum_bytes_sent)': 'Data bytes sent', ' count(DISTINCT(remote_addr)) ': 'Nb Session', 'AVG(sum_viewing_duration_class_s)': 'Average session duration', 'SUM(sum_viewing_duration_class_s)': 'Total session duration', 'SUM(sum_duration_s)': 'Total second consumption','AVG(sum_request_time / \"count\")': 'Average segment load duration', 'AVG(sum_upstream_response_time)': 'Average upstream response time', 'AVG(upstream_bandwith)': 'Average upstream bandwidth', 'AVG(delay_btw_req_and_filegen)': 'Average duration between request and file generation'};
  display_type = ["Table (Multiple dimensions)", "Bar-chart", "Pie-chart", "Time", "Time (Stacked)"];
  displayChoose;
  granuChoose;
  end;
  start;
  personalized;
  totalCount = 0;
  doNotSetDisplayInLocalStorage = false;
  doNotSetGranuInLocalStorage = false;
  filterValues = [];
  csvContent = [];
  selectTime1 = {
    'HOUR' : ', EXTRACT(MINUTE FROM __time) as a, ',
    'HOUR2' : ', EXTRACT(HOUR FROM __time) as b, EXTRACT(DAY FROM __time) as c, EXTRACT(MONTH FROM __time) as d, EXTRACT(YEAR FROM __time) as e FROM ',
    'DAY' : ', EXTRACT(HOUR FROM __time) as a, ',
    'DAY2' : ', EXTRACT(DAY FROM __time) as b, EXTRACT(MONTH FROM __time) as c, EXTRACT(YEAR FROM __time) as d FROM ',
    'WEEK' : ', EXTRACT(DAY FROM __time) as a,  ',
    'WEEK2' : ', EXTRACT(MONTH FROM __time) as b, EXTRACT(YEAR FROM __time) as c FROM ',
    'MONTH' : ', EXTRACT(DAY FROM __time) as a,  ',
    'MONTH2' : ', EXTRACT(MONTH FROM __time) as b, EXTRACT(YEAR FROM __time) as c FROM ',
    'YEAR' : ', EXTRACT(MONTH FROM __time) as a, ',
    'YEAR2' : ', EXTRACT(YEAR FROM __time) as b FROM ',

  };
  selectTime2 = {
    'HOUR' : ',  EXTRACT(MINUTE FROM __time),  EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'DAY' : ',  EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'WEEK' : ',  EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'MONTH' : ',  EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'YEAR' : ',  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
  };
  selectTime3 = {
    'HOUR' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time) limit 20000',
    'DAY' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) limit 20000',
    'WEEK' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) limit 20000',
    'MONTH' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) limit 20000',
    'YEAR' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) limit 20000',
  };

  selectTimePersonalized = {
    'MINUTE' : ', EXTRACT(MINUTE FROM __time) as a, ',
    'MINUTE2' : ', EXTRACT(HOUR FROM __time) as b, EXTRACT(DAY FROM __time) as c,  EXTRACT(MONTH FROM __time) as d, EXTRACT(YEAR FROM __time) as e FROM ',
    'HOUR' : ', EXTRACT(HOUR FROM __time) as a, ',
    'HOUR2' : ', EXTRACT(DAY FROM __time) as b,  EXTRACT(MONTH FROM __time) as c, EXTRACT(YEAR FROM __time) as d FROM ',
    'DAY' : ', EXTRACT(DAY FROM __time) as a, ',
    'DAY2' : ', EXTRACT(MONTH FROM __time) as b, EXTRACT(YEAR FROM __time) as c FROM ',
    'WEEK' : 'TODO',
    'WEEK2' : 'TODO',
    'MONTH' : ', EXTRACT(MONTH FROM __time) as a, ',
    'MONTH2' : ', EXTRACT(YEAR FROM __time) as b FROM ',
    'YEAR' : ', EXTRACT(YEAR FROM __time) as a, ',
    'YEAR2' : ' FROM ',
  };

  sessionSelectAlias = {
    'MINUTE' : 'b, c, d, e',
    'HOUR' : 'b, c, d',
    'DAY' : 'b, c, d ',
    'WEEK' : 'b, c ',
    'MONTH' : 'b, c',
    'YEAR' : 'b',
  };

  sessionSelectAliasPersonalized = {
    'MINUTE' : 'b, c, d, e',
    'HOUR' : 'b, c, d',
    'DAY' : 'b, c ',
    'MONTH' : 'b',
    'YEAR' : '',
  };

  selectTimePersonalized2 = {
    'MINUTE' : ',  EXTRACT(MINUTE FROM __time),  EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'HOUR' : ',  EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'DAY' : ',  EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'WEEK' : ',  EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'MONTH' : ',  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ',
    'YEAR' : ', EXTRACT(YEAR FROM __time) ORDER BY ',
  };
  selectTimePersonalized3 = {
    'MINUTE' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) , EXTRACT(MINUTE FROM __time) limit 20000',
    'HOUR' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) limit 20000',
    'DAY' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) limit 20000',
    'WEEK' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) limit 20000',
    'MONTH' : ', EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) limit 20000',
    'YEAR' : ', EXTRACT(YEAR FROM __time) limit 20000',
  };
  download = true;
  graphTimeGranularity = "";
  iso31662 = require("iso-3166-2");
  bookmarkForm = this.formBuilder.group({
    name: '',
  });


  drop(event: CdkDragDrop<string[]>) {

    if (event.previousContainer === event.container) {
      if (event.container.id !== "cdk-drop-list-0")
        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
      else
        return;
    } else {
      let field = this.fields[event.previousIndex];
      if (event.container.id === "cdk-drop-list-3" && !this.selected_fields.includes(field)) {
        if (this.selected_fields.length <= 4) {
          copyArrayItem(event.previousContainer.data,
              event.container.data,
              event.previousIndex,
              event.currentIndex);
        } else {
          alert("You cannot select more than 5 Dimensions");
        }
      } else {
        this.selected_fields.splice(event.previousIndex, 1);
      }
    }
    if (this.selected_fields.length == 0) {
      localStorage.setItem('authorizeUrlLoad', 'false');
      this.weAreInUrlData = false;
      // window.location.reload();
    }

    localStorage.setItem("selected_fields", this.selected_fields.toString());
    this.addUrlParams();
    this.getDatas(this.selected_fields.toString())
  }


  dropFilter(event: CdkDragDrop<string[]>) {
    if (event.previousContainer !== event.container) {
      let field = this.fields[event.previousIndex];

      if(!this.globalFilterSelectedField.includes(field)) {
        let dropdowncontent = document.getElementById("drop") as HTMLInputElement;
        dropdowncontent.style.display = "block";
      }
      this.globalFilterSelectedField.push(field);
      localStorage.setItem('actualFilter', field);
      this.selectGlobalFilter(this.fieldsGlobalFilter.indexOf(field));
    } else {
      this.selectActiveFilter(this.globalFilterSelectedField[event.previousIndex])
    }
  }


  selectActiveFilter(field) {

    let localStorageArray = [];
    let id = this.fieldsGlobalFilter.indexOf(field);
    if (localStorage.getItem("globalFilterDimensionChose"))
      localStorageArray = localStorage.getItem("globalFilterDimensionChose").split(',');
    if (localStorageArray.includes(this.fieldsGlobalFilter[id])) {
      localStorageArray.splice(localStorageArray.indexOf(this.fieldsGlobalFilter[id]), 1);
      localStorage.setItem('globalFilterDimensionChose', localStorageArray.toString());
      localStorage.setItem('filterFieldsMap', JSON.stringify([...this.filterFieldsMap]));
      this.filterValues = [];

      for (let key of this.filterFieldsMap.keys()) {
        for (let elem of this.filterFieldsMap.get(key)) {
          if (!this.filterValues.includes(elem))
            this.filterValues.push(elem)
        }
      }
      localStorage.setItem('filterDimensionValue', this.filterValues.toString());
    }
    this.addUrlParams();
    localStorage.setItem('authorizeUrlLoad', 'false');
    let changeFilter = false;
    if (localStorage.getItem('actualFilter') != field)
      changeFilter = true;
    localStorage.setItem('actualFilter', field);
    this.selectGlobalFilter(this.fieldsGlobalFilter.indexOf(field)).then(wait => {
      let dropdowncontent = document.getElementById("drop") as HTMLInputElement;
      if (dropdowncontent.style.display == "none" || changeFilter == true) {
        dropdowncontent.style.display = "block";
          if (localStorage.getItem('filterDimensionValue')) {
            for (let elem of this.filterValues) {
              elem = elem.slice(1);
              elem = elem.slice(0, -1);
              let index = localStorage.getItem('fieldsGlobalFilter').split('-,').indexOf(elem);
              let value = "valuefilter" + index;
              let elem2 = document.getElementById(value) as HTMLInputElement;
              if (elem2 !== null) {
                elem2.checked = true;
              }
            }
          }
        this.ngOnInit()
        } else
          dropdowncontent.style.display = "none";
    });
  }


  removeItems(field) {
    this.selected_fields.splice(this.selected_fields.indexOf(field), 1);
    if (this.selected_fields.length == 0) {
      localStorage.setItem('authorizeUrlLoad', 'true');
      this.weAreInUrlData = false;
      // window.location.reload();
    }
    localStorage.setItem("selected_fields", this.selected_fields.toString());
    this.addUrlParams();
    this.getDatas(this.selected_fields.toString())
  }

  private urlChecked: boolean;

  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatPaginator) paginator: MatPaginator;

  constructor(private http: HttpClient, private _route: ActivatedRoute,
              private _router: Router,
              private formBuilder: FormBuilder) { }

  ngOnInit(): void {

    //Init sorting accessor for table display
    this.dataSource.sortingDataAccessor = (data, sortHeaderId) => data[sortHeaderId].toLocaleLowerCase();
    //Retrieve in local the display type chose by user in localstorage
    if(localStorage.getItem('displayType')) {
      this.displayChoose = localStorage.getItem('displayType');
    }
    if (localStorage.getItem('granularity') == 'SECOND') {
      localStorage.setItem('granularity', 'MINUTE');
    }
    if(localStorage.getItem('granularity')) {
      this.granuChoose = localStorage.getItem('granularity');
    }

    //If refresh is set in localstorage reload page
    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      window.location.reload();
    }

    //Retrieve filter values in local variable from localstorage
    if (localStorage.getItem('globalFilterDimensionChose')) {
      this.globalFilterSelectedField = localStorage.getItem('globalFilterDimensionChose').split(',');
    }
    if (localStorage.getItem('filterFieldsMap'))
      this.filterFieldsMap = new Map(JSON.parse(localStorage.getItem('filterFieldsMap')))
    if (localStorage.getItem("filterDimensionValue") != null && localStorage.getItem("filterDimensionValue") != 'none' && localStorage.getItem("filterDimensionValue") != 'undefined' && localStorage.getItem("filterDimensionValue") != '') {
      if (localStorage.getItem("filterDimensionValue") == "undefined")
        localStorage.setItem("filterDimensionValue", "");
      // this.filterValues = localStorage.getItem('filterDimensionValue').split('\',');
      this.filterValues = localStorage.getItem('filterDimensionValue').split('\',');
      //for each elem in this.filtervalues add simple quote at the end
      for (let i = 0; i < this.filterValues.length; i++) {
        //if last char is no a simple quote add it
        if (this.filterValues[i].charAt(this.filterValues[i].length - 1) != "'")
          this.filterValues[i] = this.filterValues[i] + "'";
      }

      if (localStorage.getItem("filterDimensionValue") == "")
        localStorage.setItem("filterDimensionValue", "undefined");
    }

    //Set value in filter string that will be append to druid call to filter demands
    if (localStorage.getItem("filterFieldsMap") != null && localStorage.getItem("filterFieldsMap") != 'none' && localStorage.getItem("filterFieldsMap") != 'undefined' && localStorage.getItem("filterFieldsMap") != '') {
      this.where_global_filter = "";
      for (let key of this.filterFieldsMap.keys()) {
        if (this.filterFieldsMap.get(key).length > 0) {
          this.where_global_filter  = this.where_global_filter.concat(" and \\\"" + key + "\\\" in (" + this.filterFieldsMap.get(key) + ") ");
        }
      }
    } else {
      this.where_global_filter = "";
    }

    //If user selected no fields loading page by url is available
    if(!localStorage.getItem('authorizeUrlLoad') || localStorage.getItem('selected_fields') == "") {
      localStorage.setItem('authorizeUrlLoad', 'true')
    }
    //Retrieve the localstorage selected filter dimensions to local variable
    if (localStorage.getItem('fieldsGlobalFilter')) {
      this.fieldsGlobalFilterSelect = localStorage.getItem('fieldsGlobalFilter').split('-,').slice(0, -1);
    }

    //Retrieve some parameters variables stores in localstorage
    this.personalized = localStorage.getItem('personalized');
    this.start = localStorage.getItem('start_date');
    this.end = localStorage.getItem('end_date');
    this.precision_display = localStorage.getItem('precision');
    if(localStorage.getItem('TimeZone')) {
      this.timeZone = localStorage.getItem('TimeZone');
    }

    this.getServices().then(res => {
      if (localStorage.getItem('new_selected_services') != 'initializations')
        this.service = localStorage.getItem('new_selected_services');
      let elem = [];
      if (localStorage.getItem('measures')) {
        elem = localStorage.getItem('measures').split(',');
        for (let id of elem) {
          let value = "checkMeasure" + id;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      }

      //CHECKED AT LOADING THE NEW SERVICES CHECKBOXES
      let elem1 = [];
      if (localStorage.getItem('new_selected_services_checkboxes')) {
        elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
        for (let id of elem1) {
          let value = "serv" + id;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      } else {
        let value = "select-all";
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }

        let check = [];
        let services = '';
        for(let i = 1; i<= this.services.length; i++) {
          check.push(i);
          services = services +  '\'' + this.services[i-1] + '\',';
        }
        localStorage.setItem('new_selected_services_checkboxes', check.toString());
        localStorage.removeItem('new_selected_services');
        localStorage.setItem('new_selected_services', services.slice(0, -1));

        elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
        for (let id of elem1) {
          let value = "serv" + id;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      }
      //END NEW CHECKED

      if (localStorage.getItem('displayType')) {
        elem = localStorage.getItem('displayType').split(',');
        for (let id of elem) {
          let value = "displayType" + id;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      }

      if (localStorage.getItem('filterDimension')) {
        elem = localStorage.getItem('filterDimension').split(',');
        for (let id of elem) {
          let value = "filter" + id;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      }

      if (localStorage.getItem('filterDimensionValue') && localStorage.getItem('fieldsGlobalFilter')) {
        for (let elem of this.filterValues) {
          elem = elem.slice(1);
          elem = elem.slice(0, -1);
          let index = localStorage.getItem('fieldsGlobalFilter').split('-,').indexOf(elem);
          let value = "valuefilter" + index;
          let elem2 = document.getElementById(value) as HTMLInputElement;
          if (elem2 !== null) {
            elem2.checked = true;
          }
        }
      }
    });
    this.getBookmarks();
    this.addUrlParams();
  }


  closeFilterValueSelector() {
    let dropdowncontent = document.getElementById("drop") as HTMLInputElement;
    dropdowncontent.style.display = "none";
  }

  formatDownload() {
    this.download = !this.download;
  }

  ngAfterViewInit(): void {

    if (localStorage.getItem('notUpdateDisplay') == "true")
      this.doNotSetDisplayInLocalStorage = true;

    if (localStorage.getItem('notUpdateGranu') == "true")
      this.doNotSetGranuInLocalStorage = true;

    if (localStorage.getItem('authorizeUrlLoad') != 'false' ) {
      this.selectPersonalizedByUrl();
    }
    if(!localStorage.getItem('authorizeUrlLoad') || localStorage.getItem('selected_fields') == "")
      localStorage.setItem('authorizeUrlLoad', 'true');
  }

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.dataSource.filter = filterValue;
  }


  //Function to load the personalized page by the given url (bookmarks...)
  selectPersonalizedByUrl() {

    this.urlChecked = false;
    this._route.queryParams.subscribe(params => {
      //Get every url parameters to set it on localstorage
      if (this.urlChecked == false) {
        let Measure;
        let Dimensions = params['Dimensions'];
        let Display = params['Display'];
        let filterDimension = params['FilterDimension'];
        let filter = params['filter'];
        let precision = params['Precision'];
        let start_date= params['Start_date'];
        let end_date= params['End_date'];
        let comparison_date = params['Comparison_date'];
        let complete_date_factor = params['Factor'];
        let granularity = params['Granularity'];
        let timeZone = params['TimeZone'];

        if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadMeasure') {
          Measure = params['Measure'];
        } else {
          Measure = localStorage.getItem('measures');
        }

        if (Dimensions != undefined) {
          localStorage.setItem("selected_fields", Dimensions);
          localStorage.setItem('measures', Measure);
          if (complete_date_factor != undefined && localStorage.getItem('doNotLoadFactor') != 'true') {
            localStorage.setItem('complete_date_factor', complete_date_factor);
          } else
            localStorage.setItem('doNotLoadFactor', 'false');

          //Get the time / interval parameters
          this.precision_display = localStorage.getItem('precision');

          if (localStorage.getItem('loadUrlInterval') != 'false') {
            this.precision_display = precision;
            localStorage.setItem('TimeZone', timeZone);
            localStorage.setItem('precision', precision);
            if (precision == "Personalized") {
              localStorage.setItem('start_date', start_date);
              localStorage.setItem('end_date', end_date);
              localStorage.setItem('comparison_date', comparison_date);
              localStorage.setItem('displayCalendar', 'false');
              this.personalized = '1';
              if (localStorage.getItem('personalized') == '0') {
                localStorage.setItem('personalized', '1');
              }
            } else if (precision.includes('complete')) {
              this.navbar.setLastIntervalDate(precision)
            }
            localStorage.setItem('loadUrlInterval', 'true')
          } else {
            localStorage.setItem('loadUrlInterval', 'true')
          }

          //Get the filter parameters
          if (filterDimension != undefined) {
            if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadField' && localStorage.getItem('authorizeUrlLoad') != 'doNotLoadFilter') {
              localStorage.setItem('filterDimensionValue', filterDimension);
              this.filterFieldsMap = new Map(JSON.parse(filter));
              localStorage.setItem('filterFieldsMap', JSON.stringify([...this.filterFieldsMap]));
            }
          }
          if (filter != undefined && filter.length > 2) {
            let globalFilterDimensionChose = "";
            for (let key of this.filterFieldsMap.keys())
              globalFilterDimensionChose = globalFilterDimensionChose.concat(',', key);

            localStorage.setItem('globalFilterDimensionChose', globalFilterDimensionChose.substring(1));
            if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadFilter' && localStorage.getItem('globalFilterDimensionChose').split(',')[0] != "") {
              this.globalFilterSelectedField = localStorage.getItem('globalFilterDimensionChose').split(',');
            }
          } else {
            //If no filter in url clean the filter in localstorage
            // if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadFilter' && (localStorage.getItem('globalFilterDimensionChose') != null || localStorage.getItem('globalFilterDimensionChose').split(',')[0] != "")) {
            if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadFilter' && localStorage.getItem('authorizeUrlLoad') != 'doNotLoadField') {
              localStorage.removeItem('filterFieldsMap');
              localStorage.removeItem('globalFilterDimensionChose');
              localStorage.removeItem('filterDimensionValue');
              localStorage.removeItem('actualFilter');
              this.globalFilterSelectedField = [];
              this.filterValues = [];
            }
          }

          if (!this.doNotSetDisplayInLocalStorage) {
            localStorage.setItem('displayType', Display);
            this.displayChoose = Display;
          } else {
            this.doNotSetDisplayInLocalStorage = false;
            localStorage.setItem('notUpdateDisplay', 'false')
          }

          if (!this.doNotSetGranuInLocalStorage) {
            localStorage.setItem('granularity', granularity);
            this.granuChoose = granularity;
          } else {
            this.doNotSetGranuInLocalStorage = false;
            localStorage.setItem('notUpdateGranu', 'false')
          }

          let fields = Dimensions.split(',');

          this.selected_fields = fields;
          if (localStorage.getItem('authorizeUrlLoad') != 'doNotLoadFilter' && localStorage.getItem("filterDimensionValue") != null && localStorage.getItem("filterDimensionValue") != 'none' && localStorage.getItem("filterDimensionValue") != 'undefined' && localStorage.getItem("filterDimensionValue") != '') {
            if (localStorage.getItem("filterDimensionValue") == "undefined")
              localStorage.setItem("filterDimensionValue", "");
            this.filterValues = localStorage.getItem('filterDimensionValue').split('\','); // avoid creating multiple filters for one matched regex
            //for each elem in this.filtervalues add simple quote at the end
            for (let i = 0; i < this.filterValues.length; i++) {
              //if last char is no a simple quote add it
              if (this.filterValues[i].charAt(this.filterValues[i].length - 1) != "'")
                this.filterValues[i] = this.filterValues[i] + "'";
            }

            if (localStorage.getItem("filterDimensionValue") == "")
              localStorage.setItem("filterDimensionValue", "undefined");
          }

          this.where_global_filter = "";
          if (localStorage.getItem('authorizeUrlLoad') != 'false' && localStorage.getItem("filterFieldsMap") != null && localStorage.getItem("filterFieldsMap") != 'none' && localStorage.getItem("filterFieldsMap") != 'undefined' && localStorage.getItem("filterFieldsMap") != '') {
            for (let key of this.filterFieldsMap.keys()) {
              if (this.filterFieldsMap.get(key).length > 0) {
                this.where_global_filter  = this.where_global_filter.concat(" and \\\"" + key + "\\\" in (" + this.filterFieldsMap.get(key) + ") ");
              }
            }
          } else
            this.where_global_filter = "";

          if (localStorage.getItem('precision').includes('complete')) {
            this.navbar.setLastIntervalDate(localStorage.getItem('precision'));
          }

          //If you load personalized directly and you have no pre load services, it will get it here
          if (!this.service) {
            this.getServices().then(res => {
                  this.getDatas(localStorage.getItem(("selected_fields")));
                  this.urlChecked = true;
                  this.addUrlParams()
                }
            );
          } else {
            this.getDatas(localStorage.getItem(("selected_fields")));
            this.urlChecked = true;
            this.addUrlParams()
          }
        }
      }
    });
  }

  //Function to add the personalized selected items to the url
  addUrlParams(){
    let dimensions = localStorage.getItem('selected_fields');
    let measure = localStorage.getItem('measures');
    let display = localStorage.getItem('displayType');
    let filterDimension = localStorage.getItem('filterDimensionValue');
    let filter = localStorage.getItem('filterFieldsMap');
    let precision = localStorage.getItem('precision');
    let factor = localStorage.getItem('complete_date_factor');
    let granularity = localStorage.getItem('granularity');
    let timeZone = localStorage.getItem('TimeZone');

    let start_date;
    let end_date;
    let comparison_date;
    if (!precision.includes('complete')) {
      start_date = localStorage.getItem('start_date');
      end_date = localStorage.getItem('end_date');
      comparison_date = localStorage.getItem('comparison_date');
    }

    if (filterDimension == '' || filterDimension == "undefined" || filterDimension == "none")
      filterDimension  = null;
    if (dimensions == '' || dimensions == "undefined")
      dimensions = null;

    if (factor == '' || factor == "undefined")
      factor = null;

    if (granularity == '' || granularity == "undefined")
      granularity = null;

    if (filter == '' || filter == "undefined" || filter == "none")
      filter = null;
    if (start_date == '' || start_date == "undefined" || start_date == "none" || start_date == null) {
      start_date = null;
      end_date = null;
    }

    this._router.navigate([], {
      relativeTo: this._route,
      queryParams: {
        Measure: measure,
        TimeZone: timeZone,
        Dimensions: dimensions,
        Granularity: granularity,
        Display: display,
        Precision: precision,
        Factor: factor,
        Start_date: start_date,
        End_date: end_date,
        Comparison_date: comparison_date,
        FilterDimension: filterDimension,
        filter: filter
      },
      queryParamsHandling: 'merge',
      skipLocationChange: false
    });
  }

  onChangeMeasure(id) {

    for (let i = 0;i <= 11; i++)
    {
      let elem = document.getElementById("checkMeasure" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let elem2 = document.getElementById("checkMeasure" + id) as HTMLInputElement;
    elem2.checked = true;
    localStorage.setItem('measures', id);
    localStorage.setItem('authorizeUrlLoad', 'doNotLoadMeasure');
  }

  onChangeDisplay(id) {

    for (let i = 0;i <= 4; i++)
    {
      let elem = document.getElementById("displayType" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let elem2 = document.getElementById("displayType" + id) as HTMLInputElement;
    elem2.checked = true;
    localStorage.setItem('displayType', id);
    this.doNotSetDisplayInLocalStorage = true;
    localStorage.setItem('notUpdateDisplay', "true");
  }

  //Main function that join API to post the request and get the data
  async getDatas(field) {

    if (this.globalFilterSelectedField.includes("matched_regex")) { // This condition is to manage special characters in the matched_regex dimension
      this.to_encode = true;
    } else {
      this.to_encode = false;
    }

    show_graph_load();
    let body;
    let field_final = "";
    let order_final = "";
    let ovp_filter = "";
    this.measure = this.measures[localStorage.getItem("measures")];
    this.measure = this.measure_correspondance[this.measure];

    if (localStorage.getItem('use_ovp_filter') === 'true' && localStorage.getItem('ovp_where_statement') != null)
      ovp_filter = localStorage.getItem('ovp_where_statement');

    if (field.length > 1) {
      for (let elem of this.selected_fields) {
        field_final = field_final + "\\\"" + elem + "\\\", ";
      }
    }
    field_final = field_final.slice(0, -2);

    let unauthorized = false; // Used later to check wether the display changed is authorized or not depending on nb dimensions selected
    if ((localStorage.getItem('displayType') == "2" || localStorage.getItem('displayType') == "3" || localStorage.getItem('displayType') == "4") && localStorage.getItem('selected_fields').split(',').length > 1) {
      alert("You cannot select more than 1 dimension for this display");
      unauthorized = true;
    }

    if (localStorage.getItem('displayType') == "1" && localStorage.getItem('selected_fields').split(',').length > 2) {
      alert("You cannot select more than 2 dimensions for this display");
      unauthorized = true;
    }

    if (unauthorized) {
      let elem2 = document.getElementById("displayType0") as HTMLInputElement;
      elem2.checked = true;
      localStorage.setItem('displayType', "0");
      this.doNotSetDisplayInLocalStorage = true;
      localStorage.setItem('notUpdateDisplay', "true");
      window.location.reload();
      return;
    }

    if (this.selected_fields.length > 1) {
      let i = 0;
      for (let elem of this.selected_fields) {
        if (i == this.selected_fields.length - 2 && this.selected_fields.length > 1) {
          order_final = order_final + "\\\"" + elem + "\\\", ";
          order_final = order_final + "  " + this.measure + " DESC, ";
        } else {
          order_final = order_final + "\\\"" + elem + "\\\", ";
        }
        i = i + 1;
      }
    } else {
      order_final = this.measure + " DESC  ";
    }
    order_final = order_final.slice(0, -2);

    //Depending of the measure chosen, a variable is set to know which datasource to request
    if (this.measure === "count(DISTINCT(remote_addr))" || this.measure === "sum(sum_body_bytes_sent)" || this.measure === "sum(sum_bytes_sent)" || this.measure === "AVG(sum_request_time / \"count\")" || this.measure === 'SUM(\\"count\\")' || this.measure === 'AVG(delay_btw_req_and_filegen)' || this.measure === 'SUM(sum_duration_s)' || this.measure === 'AVG(sum_upstream_response_time)' || this.measure === 'AVG(upstream_bandwith)') {
      let is_ok = true;
      let array_to_compare = this.selected_fields.concat(this.globalFilterSelectedField);

      if (this.measure === 'AVG(sum_upstream_response_time)' || this.measure === 'AVG(upstream_bandwith)') {
        this.where_global_filter += " and \\\"sum_upstream_response_time\\\" > 0 ";
      }

      for (let meas of array_to_compare) {
        if(!this.accept_optimize.includes(meas) || this.measure === "sum(sum_body_bytes_sent)" || this.measure === "AVG(sum_request_time / \"count\")" || ovp_filter != "" || this.measure === 'AVG(sum_upstream_response_time)' || this.measure === 'AVG(upstream_bandwith)') {
          is_ok = false;
          break;
        }
      }
      if (is_ok && this.measure != "AVG(delay_btw_req_and_filegen)")
        this.actual_topic = this.topic_optimize;
      else
        this.actual_topic = this.topic;
    } else {
      this.actual_topic = this.topic_viewing;
    }

    let result;
    let start = localStorage.getItem('start_date');
    let end = localStorage.getItem('end_date');

    // ISSUE FOR MATCHED REGEX FILTER BECAUSE IT CONTAINS SPECIAL CHAR AS '+' SIGN THAT DISAPPEAR WHEN SENT CAUSE OF URI ENCODING, USE ENCODEURI SHOULD FIX IT BUT API IS NOT WORKING WITH IT

    if (this.measure !== "AVG(sum_viewing_duration_class_s)" && this.measure !== ' count(DISTINCT(remote_addr)) ' && localStorage.getItem('displayType') != "3" && localStorage.getItem('displayType') != "4") {
      if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
        let personalized_request = '(select ' + field_final + ', ' + this.measure + ' from ' + this.actual_topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP AND service_id IN (' + this.service + ') ' + this.where_global_filter + ovp_filter + ' GROUP BY ' + field_final + ' ORDER BY ' + order_final + ', ' + this.measure + ' DESC LIMIT 20000)'
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      } else {
        let personalized_request = '(select ' + field_final + ', ' + this.measure + ' from ' + this.actual_topic + ' WHERE __time >= \'' + start + '\'' + ' AND __time <= \'' + end + '\'' + ' AND service_id IN (' + this.service + ')  ' + this.where_global_filter + ovp_filter + ' GROUP BY ' + field_final + ' ORDER BY ' + order_final + ', ' + this.measure + ' DESC LIMIT 20000)'
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      }
      result = await this.http.post(GlobalConstants.apiURL + 'log/',
          body,
          {
            headers: new HttpHeaders()
                .set('accept', 'application/json')
                .set('Content-Type', 'application/x-www-form-urlencoded')
                .set('Authorization', localStorage.getItem('token'))
          }
      ).toPromise();

    } else if (this.measure === "AVG(sum_viewing_duration_class_s)" && localStorage.getItem('displayType') != "3" && localStorage.getItem('displayType') != "4") {
      if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
        let personalized_request = '(select ' + field_final + ', AVG(numbers) from(select ' + field_final + ', SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
            'FROM ' + this.topic_viewing + ' where __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' ' +
            ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' group by ' + field_final + ', remote_addr) GROUP BY ' + field_final + ' ORDER BY AVG(numbers) DESC)'
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      } else {
        let personalized_request = '(select ' + field_final + ', AVG(numbers) from(select ' + field_final + ', SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
            'FROM ' + this.topic_viewing + ' where __time < \'' +  end + '\'' + ' AND __time > \'' + start + '\'' + ' ' +
            ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' group by  ' + field_final + ', remote_addr) GROUP BY ' + field_final + ' ORDER BY AVG(numbers) DESC)'
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      }
      result = await this.http.post(GlobalConstants.apiURL + 'log/',
          body,
          {
            headers: new HttpHeaders()
                .set('accept', 'application/json')
                .set('Content-Type', 'application/x-www-form-urlencoded')
                .set('Authorization', localStorage.getItem('token'))
          }
      ).toPromise();
    } else if (this.measure === ' count(DISTINCT(remote_addr)) ' && localStorage.getItem('displayType') != "3" && localStorage.getItem('displayType') != "4") {

      // order_final = order_final.replace('SUM(\\"count\\")', 'count(DISTINCT(remote_addr))');
      if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
        let personalized_request = 'select ' + field_final + ', sum(measure) from(select ' + field_final + ', count(DISTINCT(remote_addr)) as measure ' +
          'FROM ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP' +
          ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 ' + this.where_global_filter + ovp_filter + ' group by ' + field_final + ', EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ' + order_final + ',  count(DISTINCT(remote_addr)) DESC) GROUP BY ' + field_final
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      } else {
        let personalized_request = 'select ' + field_final + ', sum(measure) from(select ' + field_final + ', count(DISTINCT(remote_addr)) as measure ' +
          'FROM ' + this.topic + ' where __time <= \'' +  end + '\'' + 'and __time >= \'' + start + '\'' + ' ' +
          ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 ' + this.where_global_filter + ovp_filter + ' group by ' + field_final + ', EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ' + order_final + ', count(DISTINCT(remote_addr)) DESC) GROUP BY ' + field_final
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      }
      result = await this.http.post(GlobalConstants.apiURL + 'log/',
          body,
          {
            headers: new HttpHeaders()
                .set('accept', 'application/json')
                .set('Content-Type', 'application/x-www-form-urlencoded')
                .set('Authorization', localStorage.getItem('token'))
          }
      ).toPromise();
    }
    if (localStorage.getItem('displayType') == "3" || localStorage.getItem('displayType') == "4") {
      if (this.actual_topic != this.topic_viewing) {
        if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
          let personalized_request = 'select ' + field_final + this.selectTime1[localStorage.getItem('precision')] + this.measure + this.selectTime1[localStorage.getItem('precision') + '2'] +
            this.actual_topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP AND service_id IN (' +
            this.service + ') ' + this.where_global_filter + ovp_filter + '  GROUP BY ' + field_final + this.selectTime2[localStorage.getItem('precision')] + field_final + this.selectTime3[localStorage.getItem('precision')]
          if (this.to_encode) { // If matched_regex dimension is used
            personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
          }
          body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
              .set('Request', personalized_request)
              .set('Encoded', this.to_encode.toString())
              .set('TimeZone', localStorage.getItem('TimeZone'));
        } else {
          let personalized_request = 'select ' + field_final + this.selectTimePersonalized[localStorage.getItem('granularity')] + this.measure + this.selectTimePersonalized[localStorage.getItem('granularity') + '2'] +
            this.actual_topic + ' WHERE __time >= \'' + start + '\'' + ' AND __time <= \'' + end + '\'' + ' AND service_id IN (' +
            this.service + ')  ' + this.where_global_filter + ovp_filter + ' GROUP BY ' + field_final + this.selectTimePersonalized2[localStorage.getItem('granularity')] + field_final + this.selectTimePersonalized3[localStorage.getItem('granularity')]
          if (this.to_encode) { // If matched_regex dimension is used
            personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
          }
          body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
              .set('Request', personalized_request)
              .set('Encoded', this.to_encode.toString())
              .set('TimeZone', localStorage.getItem('TimeZone'));
        }
        result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
              headers: new HttpHeaders()
                  .set('accept', 'application/json')
                  .set('Content-Type', 'application/x-www-form-urlencoded')
                  .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();

      } else if (this.actual_topic == this.topic_viewing) {
        if (this.measure === ' count(DISTINCT(remote_addr)) ') {

          let select_statement = ', ';
          let order_statement = ' ORDER BY ';

          if (localStorage.getItem('granularity') == 'YEAR') {
            select_statement = '';
            order_statement = '';
          }

          if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
            let personalized_request = 'select ' + field_final + ', a, ' + 'sum(measure)' + ', ' + this.sessionSelectAlias[localStorage.getItem('precision')] + ' from(select ' + field_final + this.selectTime1[localStorage.getItem('precision')] + this.measure + ' as measure ' + this.selectTime1[localStorage.getItem('precision') + '2'] +
              this.topic + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP AND service_id IN (' +
              this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' OR extension = \'mpd\') and status = 200 ' + this.where_global_filter + ovp_filter + ' GROUP BY EXTRACT(HOUR FROM __time), ' + field_final + ', EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ' + field_final + this.selectTime3[localStorage.getItem('precision')] + ')  GROUP BY ' + field_final +  ', ' + this.sessionSelectAlias[localStorage.getItem('precision')].split("").reverse().join("") + ', a ORDER BY '  + this.sessionSelectAlias[localStorage.getItem('precision')].split("").reverse().join("")
            if (this.to_encode) { // If matched_regex dimension is used
              personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
            }
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', personalized_request)
                .set('Encoded', this.to_encode.toString())
                .set('TimeZone', localStorage.getItem('TimeZone'));
          } else {
            let personalized_request = 'select ' + field_final + ', a, ' + 'sum(measure)' + select_statement + this.sessionSelectAliasPersonalized[localStorage.getItem('granularity')] + ' from(select ' + field_final + this.selectTimePersonalized[localStorage.getItem('granularity')] + this.measure + ' as measure ' + this.selectTimePersonalized[localStorage.getItem('granularity') + '2'] +
              this.topic + ' WHERE __time <= \'' + end + '\'' + ' AND __time >= \'' + start + '\'' + ' AND service_id IN (' +
              this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' OR extension = \'mpd\') and status = 200 ' + this.where_global_filter + ovp_filter + ' GROUP BY EXTRACT(HOUR FROM __time), ' + field_final + ', EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY ' + field_final + this.selectTimePersonalized3[localStorage.getItem('granularity')] + ') GROUP BY ' + field_final +  select_statement + this.sessionSelectAliasPersonalized[localStorage.getItem('granularity')] + ', a ' + order_statement  + this.sessionSelectAliasPersonalized[localStorage.getItem('granularity')].split("").reverse().join("")
            if (this.to_encode) { // If matched_regex dimension is used
              personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
            }
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', personalized_request)
                .set('Encoded', this.to_encode.toString())
                .set('TimeZone', localStorage.getItem('TimeZone'));
          }
        } else {
          if (this.measure === "AVG(sum_viewing_duration_class_s)") {
            let time_as = '';
            // This if else statement is to get the select alias time element of the subquery for each personnalized and non-personnalized ones
            if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
              for (let elem of this.selectTime1[localStorage.getItem('precision') + '2'].split('as ')) {
                if (elem.charAt(0) != ',')
                  time_as = time_as + ', ' + elem.charAt(0)
              }
            }
            else {
              for (let elem of this.selectTimePersonalized[localStorage.getItem('granularity') + '2'].split('as ')) {
                if (elem.charAt(0) != ',' && elem.charAt(0) != ' ')
                  time_as = time_as + ', ' + elem.charAt(0)
              }
            }
            // This following if else statement is for requesting average session duration displayed on time chart
            if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
              let personalized_request = 'select ' + field_final + ', a , AVG(numbers) ' + time_as + ' from(select ' + field_final + this.selectTime1[localStorage.getItem('precision')] +  'SUM(\\"sum_viewing_duration_class_s\\") as numbers ' + this.selectTime1[localStorage.getItem('precision') + '2'] +
                this.topic_viewing + ' WHERE __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] +
                ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' group by  ' + field_final + ' , remote_addr ' + this.selectTime2[localStorage.getItem('precision')] + field_final + this.selectTime3[localStorage.getItem('precision')] + '0000) GROUP BY a ' + time_as + ', ' + field_final + ' ORDER BY ' + time_as.split(',').reverse().join(",") + ' a ASC '
              if (this.to_encode) { // If matched_regex dimension is used
                personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
              }
              body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                  .set('Request', personalized_request)
                  .set('Encoded', this.to_encode.toString())
                  .set('TimeZone', localStorage.getItem('TimeZone'));
            } else {
              let personalized_request = 'select ' + field_final + ', a , AVG(numbers) ' + time_as + ' from(select ' + field_final + this.selectTimePersonalized[localStorage.getItem('granularity')] +  'SUM(\\"sum_viewing_duration_class_s\\") as numbers ' + this.selectTimePersonalized[localStorage.getItem('granularity') + '2'] +
                this.topic_viewing + ' where __time < \'' +  end + '\'' + ' AND __time > \'' + start + '\'' + ' ' +
                ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' group by  ' + field_final + ' , remote_addr ' + this.selectTimePersonalized2[localStorage.getItem('granularity')] + field_final + this.selectTimePersonalized3[localStorage.getItem('granularity')] + '0000) GROUP BY a ' + time_as + ', ' + field_final + ' ORDER BY ' + time_as.split(',').reverse().join(",") + ' a ASC '
              if (this.to_encode) { // If matched_regex dimension is used
                personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
              }
              body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                  .set('Request', personalized_request)
                  .set('Encoded', this.to_encode.toString())
                  .set('TimeZone', localStorage.getItem('TimeZone'));
            }
          }

          else if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
            let personalized_request = 'select ' + field_final + this.selectTime1[localStorage.getItem('precision')] + this.measure + this.selectTime1[localStorage.getItem('precision') + '2'] +
              this.actual_topic + ' WHERE __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND service_id IN (' +
              this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' GROUP BY ' + field_final + this.selectTime2[localStorage.getItem('precision')] + field_final + this.selectTime3[localStorage.getItem('precision')]
            if (this.to_encode) { // If matched_regex dimension is used
              personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
            }
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', personalized_request)
                .set('Encoded', this.to_encode.toString())
                .set('TimeZone', localStorage.getItem('TimeZone'));
          } else {
            let personalized_request = 'select ' + field_final + this.selectTimePersonalized[localStorage.getItem('granularity')] + this.measure + this.selectTimePersonalized[localStorage.getItem('granularity') + '2'] +
              this.actual_topic + ' where __time < \'' + end + '\'' + ' AND __time > \'' + start + '\'' + ' AND service_id IN (' +
              this.service + ') and sum_viewing_duration_class_s > 5  ' + this.where_global_filter + ovp_filter + ' GROUP BY ' + field_final + this.selectTimePersonalized2[localStorage.getItem('granularity')] + field_final + this.selectTimePersonalized3[localStorage.getItem('granularity')]
            if (this.to_encode) { // If matched_regex dimension is used
              personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
            }
            body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                .set('Request', personalized_request)
                .set('Encoded', this.to_encode.toString())
                .set('TimeZone', localStorage.getItem('TimeZone'));
          }
        }
        result = await this.http.post(GlobalConstants.apiURL + 'log/',
            body,
            {
              headers: new HttpHeaders()
                  .set('accept', 'application/json')
                  .set('Content-Type', 'application/x-www-form-urlencoded')
                  .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();
      }
    }

    if (result && result['data']['error'] && this.selected_fields.length != 0) {
      alert(this.measure_to_display[this.measure] + " not available for " + this.selected_fields)
    }
    if (result['data'].length > 19999) {
      alert("Number of results exceeds graph limit of 20000, some results could be missing in your graph.\nTry filtering.")
      hide_graph_load();
      return;
    }

    hide_graph_load();

    //Depending of the display type chosen it format the data as needed
    if (localStorage.getItem('displayType') == "1" && result['data']['error'] == undefined) {
      let arr = result['data'].slice(1);
      let jsonArr = [];
      if (localStorage.getItem('selected_fields').split(',').length == 1) {
        for (let item of arr) {
          if (item[1] !== 0) {
            jsonArr.push({
              'Value': item[0],
              'Number': item[1]
            })
          }
        }

        this.displaygraph(jsonArr);
      } else {
        for (let item of arr) {
          this.unique.push(item[1]);
          jsonArr.push({
            'category': item[0],
            [item[1]]: item[2]
          })
        }
        this.displayGraphDimensions(jsonArr);
      }
      return;
    } else if (localStorage.getItem('displayType') == "2" && result['data']['error'] == undefined) {
      let arr = result['data'].slice(1);
      let jsonArr = [];
      for (let item of arr) {
        if (item[1] !== 0) {
          jsonArr.push({
            'Value': item[0],
            'Number': item[1]
          })
        }
      }
      this.displayPieChartPersonalized(jsonArr);
      return;
    } else if ((localStorage.getItem('displayType') == "3" || localStorage.getItem('displayType') == "4") && result['data']['error'] == undefined) {
      let arr = result['data'].slice(1);
      if (localStorage.getItem('displayType') == "3")
        this.displayGraphTime(arr);
      else
        this.displayGraphTimeStacked(arr);
      return;
    }

    //If we reach this code, the display type is 1 (table) and we have to sort it
    if (result['data']['error'] != "Unknown exception") {
      let jsonArr = [];
      let jsonArr2 = [];
      let jsonArr3 = [];
      let jsonArr4 = [];
      let jsonArr5 = [];
      let jsonArr6 = [];
      let jsonArrPercentage = [];
      let arr = result['data'].slice(1);

      let count = 0;
      try {
        count = arr[0].length;
      } catch (e) {}

      for(let item of arr) {
        if (!isNaN(item [count - 1])) {
          this.totalCount = this.totalCount + Number(item[count - 1]);
        }
      }

      //Get the case where we need to translate region code
      let region = this.selected_fields.indexOf("region_geoname_id").toString();
      let country = this.selected_fields.indexOf("country_geoname_id").toString();

      for (let item of arr) {
        //If user selected country and region we can translate region_id to region name and country id too
        if (region  != "-1" && country != "-1") {
          if (this.iso31662.subdivision(item[country] + "-" + item[region]) != null && this.iso31662.subdivision(item[country] + "-" + item[region]).name != undefined) {
            item[region] = this.iso31662.subdivision(item[country] + "-" + item[region]).name;
            item[country] = this.iso31662.country(item[country]).name;
          }
        } else if (country  != "-1") {
          if (this.iso31662.country(item[country]) != null && this.iso31662.country(item[country]).name != undefined)
            item[country] = this.iso31662.country(item[country]).name;
        }
          jsonArr.push(item[0]);
          jsonArr2.push(item[1]);
          jsonArr3.push(item[2]);
          jsonArr4.push(item[3]);
          jsonArr5.push(item[4]);
          jsonArr6.push(item[5]);

          eval("jsonArr" + count).pop();
          if (this.measure === "AVG(sum_request_time / \"count\")") {
            eval("jsonArr" + count).push(this.formatNumber(parseFloat(item[count - 1]).toFixed(3), null));
          } else {
            eval("jsonArr" + count).push((item[count - 1]));
          }
          jsonArrPercentage.push(((((item[count - 1] * 100) / this.totalCount).toFixed(2).toString()).replace('.', '.')));
        }

      this.csvContent = [];
      this.result =  jsonArr;
      this.result2 = jsonArr2;
      this.result3 = jsonArr3;
      this.result4 = jsonArr4;
      this.result5 = jsonArr5;
      this.result6 = jsonArr6;
      this.resultPercentage = jsonArrPercentage;
      this.totalCount = 0;

      //Calculate the number of line that the mat-table should display depending of the dimensions selected by user
      this.val = this.selected_fields.length - 1;
      this.displayedColumns = [];
      for (let id = 0; id <= this.val + 1; id++) {
        this.displayedColumns.push("result" + (id + 1));
      }
      this.displayedColumns.push("resultPercentage");

      // Calculate sum total of all results
      let BIGTOTAL = 0;
      for (let elem = 0; elem < this.result.length; elem++) {
        BIGTOTAL += eval("this.result" + (this.displayedColumns.length - 1).toString())[elem]
      }

      // Calculate line by line the percentage of apparition of the prime column
      let total = 0;
      let resultArrP = {};
      let resultArrV = {};
      let resultArrV2 = {};
      for (let elem = 0; elem < this.result.length; elem++) {
        if (this.result[elem] == this.result[elem + 1] && elem <= this.result.length) {
          total += eval("this.result" + (this.displayedColumns.length - 1).toString())[elem];
        } else {
          total += eval("this.result" + (this.displayedColumns.length - 1).toString())[elem];
          resultArrP[this.result[elem]] = ((total * 100) / BIGTOTAL).toFixed(2).toString();
          resultArrV[this.result[elem]] = total.toString();
          total = 0;
        }
      }
      // ADD EVERY COLUMNS PERCENTAGES IF NOT AN AVERAGE MEASURES
      if (this.measure !== "AVG(sum_request_time / \"count\")" && this.measure !== "AVG(sum_viewing_duration_class_s)") {
        let total2 = 0;
        let resultArr1 = [];
        for (let elem = 0; elem < this.result.length; elem++) {
          if (this.selected_fields.length == 2) {
            this.result2[elem] = this.result2[elem].toString().concat(" (" + ((this.result3[elem] * 100) / resultArrV[this.result[elem]]).toFixed(2).toString() + " %)")
          } else if (this.selected_fields.length == 3) {
            if (this.result[elem] + ":" + this.result2[elem] == this.result[elem + 1] + ":" + this.result2[elem + 1] && elem <= this.result.length) {
              total2 += eval("this.result" + (this.displayedColumns.length - 1).toString())[elem];
            } else {
              total2 += eval("this.result" + (this.displayedColumns.length - 1).toString())[elem];
              resultArrV2[this.result[elem] + ":" + this.result2[elem]] = total2;
              resultArr1[this.result[elem] + ":" + this.result2[elem]] = ((total2 * 100) / resultArrV[this.result[elem]]).toFixed(2).toString();
              total2 = 0;
              }
            }
          }
          if (this.selected_fields.length == 3) {
            for (let elem = 0; elem < this.result.length; elem++) {
              this.result3[elem] = this.result3[elem].toString().concat(" (" + ((this.result4[elem] * 100) / resultArrV[this.result[elem]]).toFixed(2).toString() + "% - " + ((this.result4[elem] * 100) / resultArrV2[this.result[elem] + ":" + this.result2[elem]]).toFixed(2).toString() + "%)");
              this.result2[elem] = this.result2[elem].toString().concat(" (" + resultArr1[this.result[elem] + ":" + this.result2[elem]] + "%)");
            }
          }
          // END ADDING EVERY COLUMNS PERCENTAGES
        //Assign the total apparition percentage to prime column
        for (let elem = 0; elem < this.result.length; elem++) {
          this.result[elem] = this.result[elem].toString().concat(" (" + resultArrP[this.result[elem]] + "%)");
        }
      }

      //Fill the datasource that will be display in front array
      let index = 0;
      let line;
      LINE_DATA = [];
      for (let elem of this.result) {
        line = {"result1": elem, "result2": this.result2[index], "result3": this.result3[index], "result4": this.result4[index], "result5": this.result5[index], "result6": this.result6[index], "resultPercentage": this.resultPercentage[index]};
        LINE_DATA.push(line);
        index++;
      }
      this.dataSource = new MatTableDataSource(LINE_DATA);
    }

    //Create the mat table paginator
    this.dataSource.paginator = this.paginator;

    //Set the mat table sorting
    this.dataSource.sortingDataAccessor = (data: any, sortHeaderId: string): string => {
      if (typeof data[sortHeaderId] === 'string') {
        if(!isNaN(Number(data[sortHeaderId]))) {
          data[sortHeaderId] = Number(data[sortHeaderId])
        } else
          return data[sortHeaderId].toLocaleLowerCase();
      }
      return data[sortHeaderId];
    };
    this.dataSource.sort = this.sort;
  }


  //Format number to be more readable and sortable in table display
  formatNumber(x, measure) {
    let part1 = "";
    let part2 = "";
    let val = x.toString().split('(');
    if (val[0] == " " || val[0] === null || val[0].match(/^ *$/) !== null)
      part1 = "undefined ";
    else if(val[2])
      part1 = val[0] + " (" + val[1];
    else
      part1 = val[0];
    if (val[val.length -1] && val.length > 1)
      part2 = "(" + val[val.length -1];
    else
      part2 = "";

      if (this.download == false && typeof x !== "number")
        return part1 + part2;
      else {
        if (!isNaN(Number(part1))) {
          if (measure == this.selected_fields.length && (this.measure == "sum(sum_bytes_sent)" || this.measure == "sum(sum_body_bytes_sent)")) {
            if (localStorage.getItem("MeasureUnit"))
              this.consumption_value = localStorage.getItem("MeasureUnit");
            else {
              this.consumption_value = "Bytes";
              localStorage.setItem("MeasureUnit", this.consumption_value);
            }
            part1 = (parseFloat(part1) / this.consumption_values_correspondance[this.consumption_value]).toFixed(3).toString()
          }
          return parseFloat(part1).toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
        }
        return part1;
    }
  }

  // Function to get the user services
  async getServices() {
    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
      // if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.ngOnInit()
    }

    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }

    // CHECKED THE CHECKBOXES GRANULARITY
    let elemprec1;
    let prec1 = localStorage.getItem('granularity');
    for (let key in this.granu) {
      if (this.granu[key] == prec1) {
        elemprec1 = key;
      }
    }
    let granu1 = document.getElementById(elemprec1) as HTMLInputElement;
    if (granu1 !== null) {
      granu1.checked = true;
    }
    // CHECKED THE CHECKBOXES GRANULARITY

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      window.location.reload();
    }
  }

  //Function to display the time graph in front
  displayGraphTime(arr) {

    //Round values to decimal
    for (let elem of arr) {
      if (typeof elem[2] == 'number')
        elem[2] = elem[2].toFixed(2);
    }

    //am4core.useTheme(am4themes_dark);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivP", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    chart.cursor = new am4charts.XYCursor();
    chart.scrollbarX = new am4core.Scrollbar();
    chart.cursor.xAxis = dateAxis;

    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }
    this.graphTimeGranularity = dateAxis.baseInterval['timeUnit'];


    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsTime" + this.measure_to_display[this.measure] + this.consumption_value + '-' + this.graphTimeGranularity;
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = this.measure_to_display[this.measure] +  this.consumption_value + ' per ' + this.graphTimeGranularity + ' per ' + this.selected_fields + ' - Hexaglobe';

    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var datas = [];
    //GET LES DIFFERENTES DONNES A GRAPHER
    for(let elem of arr) {
      elem[2] = this.formatNumber(elem[2], 1);
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }

    for (let i of datas) {
      createSeries(i, i);
    }

    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = s + " : {valueY}";
      // series.tooltip.pointerOrientation = "vertical";
      series.tooltip.background.fillOpacity = 0.5;
      var segment = series.segments.template;
      segment.interactionsEnabled = true;
      var hoverState = segment.states.create("hover");
      hoverState.properties.strokeWidth = 3;
      var dimmed = segment.states.create("dimmed");
      dimmed.properties.stroke = am4core.color("#dadada");
      segment.events.on("over", function(event) {
        processOver(event.target.parent.parent.parent);
      });
      segment.events.on("out", function(event) {
        processOut(event.target.parent.parent.parent);
      });

      var data = [];
      let i = 0;
      for (let elem of arr) {
        if (elem[0] == s) {
          let value = elem[2];
          let new_date = getDateGraphTime(elem);

          if (new_date.charAt(0) == '\'')
            new_date = new_date.substring(1);

          var dataItem = { date: new_date };
          dataItem["value" + s] = parseFloat(value.replace(/ /g, '')); // remove space and parse to float
          data.push(dataItem);
        }
      }
      series.data = data;
      return series;
    }

    chart.exporting.adapter.add("data", function(data, target) {
      // Assemble data from series
      var data1 = [];
      chart.series.each(function(series) {
        for(var i = 0; i < series.data.length; i++) {
          series.data[i].name = series.name;
          data1.push(series.data[i]);
        }
      });

      return { data: data1 };
    });

    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;
    chart.legend.itemContainers.template.events.on("over", function(event) {
      processOver(event.target.dataItem.dataContext);
    });
    chart.legend.itemContainers.template.events.on("out", function(event) {
      processOut(event.target.dataItem.dataContext);
    });
    function processOver(hoveredSeries) {
      hoveredSeries.toFront();
      hoveredSeries.segments.each(function(segment) {
        segment.setState("hover");
      });
      chart.series.each(function(series) {
        if (series != hoveredSeries) {
          series.bulletsContainer.setState("dimmed");
        }
      });
    }

    function processOut(hoveredSeries) {
      chart.series.each(function(series) {
        series.bulletsContainer.setState("default");
      });
    }
    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      let totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // let date = new Date(series.tooltipDataItem.dataContext['date']);
          // if (series.tooltipDataItem.valueY != undefined && date.getHours() == parseInt(text.split(':')[0])) {
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) {
            // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }


  //Function to fill the gaps in stacked time graph
  fillStackedGraphGaps(arr) {
    //GET ALL THE DISTINCT LINE SERIES ELEMENT
    let distinct = [];
    let distinct2 = [];
    for (let elem of arr) {
      // let element = [...elem];
      let element = elem.slice();
      distinct.push(element[0]);
      element.shift();
      element.splice(1, 1);
      distinct2.push(element.join('-'))
    }
    distinct = [...new Set(distinct)];
    distinct2 = [...new Set(distinct2)];

    let dateArray = [];
    let dateArrayFinal = [];
    for (let date of distinct2) {
      let test = date.split('-')
      test.splice(0, 0 , " ")
      test.splice(2, 0 , " ")
      dateArray.push(getDateGraphTime(test).replace('\'', ''))
    }
    dateArray.sort((x, y) => +new Date(x) - +new Date(y));

    for (let item of dateArray) {
      let date = new Date(item);
      dateArrayFinal.push(date.getMinutes() + "-" + date.getHours() + "-" + date.getDate() + "-" + (date.getMonth()  + 1) + "-" + date.getFullYear())
    }

    for (let elem of distinct) {
      let index = 0;
      let indexDateArrayFinal = 0;
      while (indexDateArrayFinal < dateArrayFinal.length) { // In order to fill the missing values with 0, we loop through arr for each values of the dimensions, and check if the value exists in date array AND in arr, if it does not we create an 0 entry for it
        let element = arr[index++];
        let elemDate = dateArrayFinal[indexDateArrayFinal++];
        if (element != undefined && element[0] != elem) { // If the value does not match the dimension value we are working on we ignore it
          indexDateArrayFinal--;
          continue
        }
        let str1 = elem + " " + elemDate.replaceAll('-', ' ');
        if (element != undefined) {
          element = element.slice()
          element.splice(2, 1);
          let str2 = element.toString().replaceAll(',', ' ');
          elemDate = elemDate.replaceAll('-', ' ').split(' ')
          elemDate.splice(0, (elemDate.length - str2.split(' ').length) + 1)
          str1 = elem + " " + elemDate.join(' ');
          if (str1 !== str2) { // If the next value in arr does not match the date we are working on we create a 0 entry for it
            let str1ARR  = str1.split(' ');
            str1ARR.splice(2, 0, "0");
            arr.splice(index - 1, 0, str1ARR)
          }
        } else { //If we go through the else, we finished to loop and fill the arr object, so we are at the end we can just push the missing data
          let str1ARR  = str1.split(' ');
          str1ARR.splice(2, 0, "0");
          arr.push(str1ARR)
        }
      }
    }
  }

  //Function to display the stacked time graph in front
  displayGraphTimeStacked(arr) {

    //Round values to decimal
    for (let elem of arr) {
      if (typeof elem[2] == 'number')
        elem[2] = elem[2].toFixed(2);
    }

    this.fillStackedGraphGaps(arr);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivP", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }
    this.graphTimeGranularity = dateAxis.baseInterval['timeUnit'];

    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsTime" + this.measure_to_display[this.measure] + this.consumption_value + '-' + this.graphTimeGranularity;
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = this.measure_to_display[this.measure] +  this.consumption_value + ' per ' + this.graphTimeGranularity + ' per ' + this.selected_fields + ' - Hexaglobe';

    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in


    dateAxis.renderer.minGridDistance = 60;
    dateAxis.startLocation = 0.5;
    dateAxis.endLocation = 0.5;

    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && localStorage.getItem('precision') == 'Personalized')) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && localStorage.getItem('precision') == 'Personalized')) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && localStorage.getItem('precision') == 'Personalized')) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }
    this.graphTimeGranularity = dateAxis.baseInterval['timeUnit'];

    var datas = [];
    //GET LES DIFFERENTES DONNES A GRAPHER
    for(let elem of arr) {
      elem[2] = this.formatNumber(elem[2], 1);
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }
    for (let i of datas) {
      createSeries(i, i);
    }

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.xAxis = dateAxis;
    chart.scrollbarX = new am4core.Scrollbar();

    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = "[#000]" + s + " : {valueY.value}[/]";
      series.tooltip.background.fill = am4core.color("#FFF");
      series.tooltip.getStrokeFromObject = true;
      series.tooltip.getFillFromObject = false;
      series.tooltip.background.strokeWidth = 3;
      // series.tooltip.pointerOrientation = "vertical";
      series.sequencedInterpolation = true;
      series.tooltip.getFillFromObject = false;
      series.fillOpacity = 0.6;
      series.strokeWidth = 2;
      series.stacked = true;
      series.connect = true;
      var data = [];
      let i = 0;
      for (let elem of arr) {
        if (elem[0] == s) {
          let value = elem[2];
          let new_date = getDateGraphTime(elem);
          if (new_date.charAt(0) == '\'')
            new_date = new_date.substring(1);
          let final = new Date(new_date);

          // if (localStorage.getItem('signDecalage') === '+') {
          //   final.setHours(final.getHours() + parseInt(localStorage.getItem('timeZoneDecalage')));
          // } else {
          //   final.setHours(final.getHours() - parseInt(localStorage.getItem('timeZoneDecalage')));
          // }
          new_date = final.toISOString();
          var dataItem = { date: new_date };
          dataItem["value" + s] = value;
          data.push(dataItem);
        }
      }
      series.data = data;
      return series;
    }

    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;

    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      var totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // let date = new Date(series.tooltipDataItem.dataContext['date']);
          // if (series.tooltipDataItem.valueY != undefined && date.getHours() == parseInt(text.split(':')[0])) { // @ts-ignore
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) { // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }

  //Function to display the barchart graph in front
  displaygraph(arr) {
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivP", am4charts.XYChart);
    chart.scrollbarX = new am4core.Scrollbar();
    chart.data = arr;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsBarChart" + this.measure_to_display[this.measure] +  this.consumption_value + ' / ' + this.graphTimeGranularity;
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = this.measure_to_display[this.measure] +  this.consumption_value + ' per ' + this.selected_fields + ' - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;

    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Value";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 10;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = false;
    categoryAxis.renderer.minHeight = 110;

    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.minWidth = 50;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.sequencedInterpolation = true;
    series.dataFields.valueY = "Number";
    series.dataFields.categoryX = "Value";
    series.tooltipText = "[{categoryX}: bold]{valueY}[/]";
    series.columns.template.strokeWidth = 0;
    series.tooltip.pointerOrientation = "vertical";
    series.columns.template.column.cornerRadiusTopLeft = 10;
    series.columns.template.column.cornerRadiusTopRight = 10;
    series.columns.template.column.fillOpacity = 0.8;
    var hoverState = series.columns.template.column.states.create("hover");
    hoverState.properties.cornerRadiusTopLeft = 0;
    hoverState.properties.cornerRadiusTopRight = 0;
    hoverState.properties.fillOpacity = 1;
    series.columns.template.adapter.add("fill", function(fill, target) {
      return chart.colors.getIndex(target.dataItem.index);
    });
    categoryAxis.start = 0;
    categoryAxis.end = 0.8;
    chart.cursor = new am4charts.XYCursor();
  }

  //Function that returns an array of unique keys in key value array
  getUnique(array){
    var uniqueArray = [];
    for(let i=0; i < array.length; i++){
      if(uniqueArray.indexOf(array[i]) === -1) {
        uniqueArray.push(array[i]);
      }
    }
    return uniqueArray;
  }

  //Function to select the time graph granularity
  selectGranularity(id) {
    let consumption = new ConsumptionComponent(this.http, DOCUMENT);

    for (let i = 0;i <= 4; i++)
    {
      let elem = document.getElementById("CheckGranu" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let s = new Date(localStorage.getItem('start_date'));
    let e = new Date(localStorage.getItem('end_date'));
    var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date


    if (this.granu[id] === 'HOUR' && (diff > 7*24)) {
      alert("You cannot choose this granularity, reduce time interval to 7 days or less.");
      id = "CheckGranu2";
    }

    if (this.granu[id] === 'MINUTE' && (diff > 24)) {
      alert("You cannot choose this granularity, reduce hours interval to 24 hours or less.");
      id = "CheckGranu2";
    }

    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    id = this.granu[id];
    localStorage.setItem('granularity', id);
    this.doNotSetGranuInLocalStorage = true;
    localStorage.setItem('notUpdateGranu', "true");
    consumption.reload();
  }

  //Function to select the global filter fields and display it in front
  async selectGlobalFilter(id) {

    this.fieldsGlobalFilterSelect = [];
    let consumption = new ConsumptionComponent(this.http, DOCUMENT);
    let localStorageArray = [];

    if (localStorage.getItem("globalFilterDimensionChose"))
      localStorageArray = localStorage.getItem("globalFilterDimensionChose").split(',');

    if (localStorageArray.includes(this.fieldsGlobalFilter[id])) {
      localStorageArray.splice(localStorageArray.indexOf(this.fieldsGlobalFilter[id]), 1);
      localStorage.setItem('globalFilterDimensionChose', localStorageArray.toString());
      this.filterFieldsMap.delete(this.fieldsGlobalFilter[id]);
      localStorage.setItem('filterFieldsMap', JSON.stringify([...this.filterFieldsMap]));
      this.filterValues = [];
      for (let key of this.filterFieldsMap.keys()) {
        for (let elem of this.filterFieldsMap.get(key))
          this.filterValues.push(elem)
      }
      localStorage.setItem('filterDimensionValue', this.filterValues.toString());
      if (localStorageArray.length == 0) {
        this.globalFilterSelectedField = [];
        this.filterValues = [];
        this.fieldsGlobalFilterSelect = [];
        localStorage.removeItem('fieldsGlobalFilter');
        localStorage.removeItem('filterDimensionValue');
        localStorage.removeItem('globalFilterDimensionChose');
      }

      this.addUrlParams();
      this.ngOnInit();
      this.getDatas(this.selected_fields.toString())
      return;
    }

    localStorage.setItem('filterDimension', id);
    this.globalFilter = this.fieldsGlobalFilter[id];
    localStorage.setItem("globalFilterDimensionChose", this.globalFilterSelectedField.toString());
    localStorage.setItem('authorizeUrlLoad', 'doNotLoadFilter');

    if (id != 0) {
      this.actual_topic = this.topic;
      let body;

      if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
        let personalized_request = '(select DISTINCT(\\\"' + this.fieldsGlobalFilter[id] + '\\\")' +
          ' , SUM(\\\"count\\\") FROM ' + this.actual_topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
          ' AND service_id IN (' + this.service + ') GROUP BY \\\"' + this.fieldsGlobalFilter[id] + '\\\"' + 'order by SUM(\\\"count\\\") DESC limit 20000)'
          if (this.to_encode) { // If matched_regex dimension is used
            personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
          }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      } else {
        let personalized_request = '(select DISTINCT(\\\"' + this.fieldsGlobalFilter[id] + '\\\")' +
          ' , SUM(\\\"count\\\") FROM ' + this.actual_topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' and __time <= \'' + localStorage.getItem('end_date') +
          '\' AND service_id IN (' + this.service + ') GROUP BY \\\"' + this.fieldsGlobalFilter[id] + '\\\"' + 'order by SUM(\\\"count\\\") DESC limit 20000)'
        if (this.to_encode) { // If matched_regex dimension is used
          personalized_request = encodeURI(personalized_request).split('+').join('%2B').split('\\').join('');
        }
        body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
            .set('Request', personalized_request)
            .set('Encoded', this.to_encode.toString())
            .set('TimeZone', localStorage.getItem('TimeZone'));
      }

      const result = await this.http.post(GlobalConstants.apiURL + 'log/',
          body,
          {
            headers: new HttpHeaders()
                .set('accept', 'application/json')
                .set('Content-Type', 'application/x-www-form-urlencoded')
                .set('Authorization', localStorage.getItem('token'))
          }
      ).toPromise();

      this.fieldsGlobalFilterSelect = [];
      result['data'] = result['data'].slice(1);
      for (let elem of result['data']) {
        if (elem[0] == "")
          elem[0] = "undefined";

        this.fieldsGlobalFilterSelect.push(elem[0]+'-');
      }
      localStorage.setItem('fieldsGlobalFilter', this.fieldsGlobalFilterSelect.toString());
      this.fieldsGlobalFilterSelect = localStorage.getItem('fieldsGlobalFilter').split('-,').slice(0, -1);
      this.addUrlParams();
    } else {
      localStorage.removeItem('fieldsGlobalFilter');
      localStorage.removeItem('filterDimensionValue');
      localStorage.removeItem('globalFilterDimensionChose');
      this.globalFilterSelectedField = [];
      consumption.reload();
    }
  }

  //Function to select fields to filter in global filter
  changeTheGlobalFilter(id, deleted_field) {
    deleted_field = deleted_field.toString().split('\'').join('');
    deleted_field = "\'" + deleted_field + "\'";

    //Delete filter values in selected_fields and in filterfieldsMap
    let deletion = false;
    for (let key of this.filterFieldsMap.keys()) {
      let arrD = [];
      let deletedField = deleted_field;
      if (this.filterFieldsMap.get(key).includes(deletedField) || this.filterFieldsMap.get(key).includes(deletedField.toString().split('\'').join(''))) {
        arrD = this.filterFieldsMap.get(key);
        arrD.splice(arrD.indexOf(deletedField), 1);
        if (this.filterValues.includes(deletedField))
          this.filterValues.splice(this.filterValues.indexOf(deletedField), 1);
        this.filterFieldsMap.set(key, arrD);
        deletion = true;
      }
    }

    //Adding filter values to filtervalues and filterFieldsmap
    if (deletion == false) {
      let arr = [];
      if (this.filterFieldsMap.get(this.globalFilterSelectedField[this.globalFilterSelectedField.indexOf(localStorage.getItem('actualFilter'))]) != undefined)
        arr = this.filterFieldsMap.get(this.globalFilterSelectedField[this.globalFilterSelectedField.indexOf(localStorage.getItem('actualFilter'))]);
      if (arr.includes(this.fieldsGlobalFilterSelect[id])) {
        arr.splice(this.fieldsGlobalFilterSelect[id], 1);
      } else {
        arr.push("\'" + this.fieldsGlobalFilterSelect[id].toString() + "\'");
        this.filterValues.push("\'" + this.fieldsGlobalFilterSelect[id].toString() + "\'")
      }
      this.filterFieldsMap.set(this.globalFilterSelectedField[this.globalFilterSelectedField.indexOf(localStorage.getItem('actualFilter'))], arr);
    }
    localStorage.setItem('filterFieldsMap', JSON.stringify([...this.filterFieldsMap]));

    if (deletion == false) {
      try {
        let elem2 = document.getElementById("valuefilter" + id) as HTMLInputElement;
        elem2.checked = true;
      } catch (e) {
        console.log("Unchecked not available.")
      }
    } else {
      try {
        let elem2 = document.getElementById("valuefilter" + id) as HTMLInputElement;
        elem2.checked = false;
      } catch (e) {
        console.log("Unchecked not available.")
      }
    }

    localStorage.setItem('filterDimensionValue', this.filterValues.toString());
    localStorage.setItem('authorizeUrlLoad', 'doNotLoadField');
    //window.location.reload();
  }

  //Function to display the pie chart graph in front
  displayPieChartPersonalized(arr) {
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivP", am4charts.PieChart);
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    chart.exporting.filePrefix = "AdvancedAnalyticsPieChart" + this.measure_to_display[this.measure] +  this.consumption_value + ' / ' + this.graphTimeGranularity;
    let title = this.measure_to_display[this.measure] +  this.consumption_value + ' per ' + this.selected_fields + ' - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "Number";
    pieSeries.dataFields.category = "Value";

    chart.innerRadius = am4core.percent(30);

    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 2;
    pieSeries.slices.template.strokeOpacity = 1;
    pieSeries.slices.template
        .cursorOverStyle = [
      {
        "property": "cursor",
        "value": "pointer"
      }
    ];
    pieSeries.alignLabels = false;
    pieSeries.labels.template.bent = false;
    pieSeries.labels.template.radius = 10;
    pieSeries.labels.template.padding(10,10,10,10);
    pieSeries.ticks.template.disabled = true;
    pieSeries.labels.template.opacity = 0;

    var shadow = pieSeries.slices.template.filters.push(new am4core.DropShadowFilter);
    shadow.opacity = 0;
    var hoverState = pieSeries.slices.template.states.getKey("hover"); // normally we have to create the hover state, in this case it already exists
    var hoverShadow = hoverState.filters.push(new am4core.DropShadowFilter);
    hoverShadow.opacity = 0.7;
    hoverShadow.blur = 5;
    chart.legend = new am4charts.Legend();
    chart.data = arr;
  }


  //Function to display a barchart graph containing multiple dimensions
  displayGraphDimensions(data) {
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create('chartdivP', am4charts.XYChart);
    chart.colors.step = 2;
    chart.scrollbarX = new am4core.Scrollbar();
    var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    xAxis.dataFields.category = 'category';
    xAxis.renderer.cellStartLocation = 0.1;
    xAxis.renderer.cellEndLocation = 0.9;
    xAxis.renderer.grid.template.location = 0;
    var yAxis = chart.yAxes.push(new am4charts.ValueAxis());
    yAxis.min = 0;
    function createSeries(value, name) {
      var series = chart.series.push(new am4charts.ColumnSeries());
      series.dataFields.valueY = value;
      series.dataFields.categoryX = 'category';
      series.name = name;
      series.columns.template.tooltipText = "{name}: [bold]{valueY}[/]";
      series.events.on("hidden", arrangeColumns);
      series.events.on("shown", arrangeColumns);
      var bullet = series.bullets.push(new am4charts.LabelBullet());
      bullet.interactionsEnabled = false;
      bullet.dy = 30;
      // bullet.label.text = '{valueY}'
      bullet.label.fill = am4core.color('#ffffff');
      return series;
    }
    chart.data = data;
    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsBarChart" + this.measure_to_display[this.measure] +  this.consumption_value + ' / ' + this.graphTimeGranularity;
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = this.measure_to_display[this.measure] +  this.consumption_value + ' per ' + this.selected_fields + ' - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
    this.unique = this.getUnique(this.unique);

    for (let elem of this.unique) {
      createSeries([elem], [elem])
    }

    function arrangeColumns() {
      var series = chart.series.getIndex(0);
      var w = 1 - xAxis.renderer.cellStartLocation - (1 - xAxis.renderer.cellEndLocation);
      if (series.dataItems.length > 1) {
        var x0 = xAxis.getX(series.dataItems.getIndex(0), "categoryX");
        var x1 = xAxis.getX(series.dataItems.getIndex(1), "categoryX");
        var delta = ((x1 - x0) / chart.series.length) * w;
        if (am4core.isNumber(delta)) {
          var middle = chart.series.length / 2;
          var newIndex = 0;
          chart.series.each(function(series) {
            if (!series.isHidden && !series.isHiding) {
              series.dummyData = newIndex;
              newIndex++;
            }
            else {
              series.dummyData = chart.series.indexOf(series);
            }
          });
          var visibleCount = newIndex;
          var newMiddle = visibleCount / 2;

          chart.series.each(function(series) {
            var trueIndex = chart.series.indexOf(series);
            var newIndex = series.dummyData;
            var dx = (newIndex - trueIndex + middle - newMiddle) * delta;
            series.animate({ property: "dx", to: dx }, series.interpolationDuration, series.interpolationEasing);
            series.bulletsContainer.animate({ property: "dx", to: dx }, series.interpolationDuration, series.interpolationEasing);
          })
        }
      }
    }
  }

  //Function to search in filter fields
  search() {
    let elem2 = document.getElementById("searchInField") as HTMLInputElement;
    let val = elem2.value;
    this.fieldsGlobalFilterSelect = localStorage.getItem('fieldsGlobalFilter').split('-,').slice(0, -1);
    let tmp = [];
    for (let elem of this.fieldsGlobalFilterSelect) {
      if ((elem.toLowerCase().includes(val.toLowerCase())) && val != "") {
        tmp.push(elem);
      }
    }
    if (val == "")
      this.fieldsGlobalFilterSelect = localStorage.getItem('fieldsGlobalFilter').split('-,')
    else
      this.fieldsGlobalFilterSelect = tmp;
  }

  //Function to return all the user bookmarks
  async getBookmarks() {
    const bookmarks = await this.http.get<any>(GlobalConstants.apiURL + 'bookmark/user/' + localStorage.getItem('user_public_id'),
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    this.bookmarks = bookmarks['data'];
    return this.bookmarks;
  }


  //Function that redirect the user to his bookmark when click on it
  redirectToBookmark(url) {
    //Clean the local storage for every personalized aspect to avoid conflicts
    localStorage.setItem('personalized', '0');
    this.personalized = 0;
    localStorage.removeItem('precision');
    localStorage.removeItem('start_date');
    localStorage.removeItem('end_date');
    localStorage.removeItem('comparison_date');
    localStorage.removeItem('granularity');
    localStorage.setItem('authorizeUrlLoad', 'true');
    localStorage.setItem('loadUrlInterval', 'true');
    window.location.href = url;
  }


  //Function that post a new user bookmark in API
  async onSubmitBookmark() {
    if (this.bookmarkForm.value['name'] != "") {
      const bodyJSON = {
        "name": this.bookmarkForm.value['name'],
        "value": window.location.href,
        "user_public_id": localStorage.getItem('user_public_id'),
        "public_id": this.bookmarkForm.value['name']
      };
      var request = await this.http.post(GlobalConstants.apiURL + 'bookmark/',
          JSON.stringify(bodyJSON),
          {
            headers: new HttpHeaders()
                .set('Authorization', localStorage.getItem('token'))
                .set('accept', 'application/json')
                .set('Content-Type', 'application/json')
          }
      ).toPromise();
      this.bookmarkForm.reset();
    } else {
      alert("Please enter a bookmark name")
    }
    window.location.reload();
  }


  //Function to delete a user bookmark
  async deleteBookmark(bookmark_id) {
    var request = await this.http.delete(GlobalConstants.apiURL + 'bookmark/' + bookmark_id + '/delete',
        {
          headers: new HttpHeaders()
              .set('Authorization', localStorage.getItem('token'))
              .set('accept', 'application/json')
              .set('Content-Type', 'application/json')

        }
    ).toPromise()
    window.location.reload();
  }

  reload() {
    window.location.reload();
  }

  //Function to change the displayed unit measure for consumption (MB, GB, TB...)
  changeUnit(measure) {
    this.consumption_value = measure;
    localStorage.setItem("MeasureUnit", measure);
    window.location.reload()
  }
}
