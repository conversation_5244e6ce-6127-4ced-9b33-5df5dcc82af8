.fields {
  margin-bottom: 20px;
  float: left;
  margin-right: 5px;
  margin-left: 5px;
}

.title {
  margin-top: 0;
  margin-bottom: 0;
}

.fields_card {
  width: 55%;
  align-items: center;
  height: 320px;
  display: inline-block;
}

.measures_card {
  width: 38%;
  float: right;
  align-items: center;
  height: 320px!important;
  display: inline-block;
}

table
{
  border-collapse: collapse;
}

/* Change the text color of the tooltip */
::ng-deep .mat-tooltip {
  color: white !important;
}

::ng-deep .mat-select-panel {
  background-color: white;
}

th
{
  border: 1px solid black;
  //width: 100%;
}

td
{
  border: 1px solid black;
  min-width: 200px;
  max-width: 200px;
  //position: relative;
  text-align: right;
  padding-right: 10px;
  //justify-content: center;
}


.result_array {
  margin-top: 40px;
  margin-right: auto;
  margin-left: auto;
  align-items: center;
}

.array {
  width: 100%;
}

.array_title {
  width: 100%;
}

.measure {
  display: flex;
  margin-right: 5px;
}

.display {
  display: flex;
  margin-left: 5px;
  margin-right: 5px;
}

.display-bookmark {
  display: flex;
  margin-left: 5px;
  margin-right: 5px;
}

.displayBookmark {
  display: inline-block;
  margin-top: 5px;
}

.displayUnit {
  display: inline-block;
  margin-left: 10px;
  float: right;
}

.globalFilter {
  display: inline-block;
  margin-left: 5px;
}

.globalFilterSelect {
  //display: inline-block;
  //margin-left: 10px;
  margin-top: 10px;
}

.measure:hover .dropdown-content {
  display: block;
}

.display:hover .dropdown-content {
  display: block;
}


.displayBookmark:hover .dropdown-contentBookmark {
  display: block;
}

.displayUnit:hover .dropdown-contentUnit {
  display: block;
}

.globalFilter:hover .dropdown-content {
  display: block;
}

.globalFilterSelect:hover .dropdown-content {
  display: block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 500px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
  max-height: 500px;
  overflow-y: scroll;
}

.dropdown-contentBookmark {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 500px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
  max-height: 500px;
  overflow-y: scroll;
}

.dropdown-contentUnit{
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 100px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
  max-height: 500px;
}

.container {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.containerBookmark {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #2196F3;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

#chartdivP {
  width: 100%;
  height: 500px;
}

mat-icon {
  color: #737373;
}

#graph {
  position: absolute;
  left: 50%;
  top: 10%;
  z-index: 1;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  animation: spin 2s linear infinite;
  display: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result_precision {
  text-align: center;
}

.result_timezone {
  text-align: center;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

.sub_content {
  display: inline-block;
}

.personalizedTitleGranularity {
  display: flex;
}

.precision {
  margin-left: 20px;
  align-self: center;
}

.precision:hover .dropdown-content1 {
  display: block;
}

.dropdown-content1 {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.save_bookmark {
  min-width: 160px;
  display: inline-block;
  margin-left: 10px;
}

.checkboxes {
  display: inline-block;
}

.container1 {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

.container1:hover input ~ .checkmark {
  background-color: #2196F3;
}

.container1 input:checked ~ .checkmark {
  background-color: #2196F3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container1 input:checked ~ .checkmark:after {
  display: block;
}

.container1 .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

#searchInField {
  margin-left: 40px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.csv_button {
  margin-right: 5px;
  margin-bottom: 5px;
}

#ShowData {
  display: inline-block;
}


.mat-header-cell {
  text-align: center;
  color: #1d8ef1;
  max-width: 200px;
  min-width: 200px;
}

::ng-deep .mat-sort-header-container {
  justify-content: center;
}

.mat-cell {
  max-width: 200px;
  min-width: 200px;
}

::ng-deep th.mat-sort-header.mat-header-cell.cdk-header-cell.ng-tns-c341-3.cdk-column-result1.mat-column-result1.ng-star-inserted {
  padding-left: 0!important;
}

th.mat-header-cell:first-of-type {
  padding-left: 0!important;
}

td.mat-cell:first-of-type {
  padding-left: 0!important;
  padding-right: 10px;
  text-align: right;
}

.RESULT {
  display: grid;
}

.drop_zone {
  width: 20px;
}

.order-list {
  width: 800px;
  margin-top: 20px;
  max-width: 100%;
  border: dashed 1px #ccc;
  min-height: 60px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  //align-items: center;
}

.globalFilterList {
  width: 100%;
  max-width: 100%;
  border: dashed 1px #ccc;
  min-height: 60px;
  display: flex;
  border-radius: 4px;
  //overflow: hidden;
  overflow-x: scroll;
  //align-items: center;
}

.globalFilterField {
  width: 100%;
  max-width: 100%;
  border: dashed 1px #ccc;
  min-height: 60px;
  display: inline-flex;
  border-radius: 4px;
  overflow: hidden;
  overflow-x: scroll;
  //align-items: cent er;
}

.fields-list {
  //width: 1000px;
  margin-top: 20px;
  max-width: 100%;
  //border: solid 1px #ccc;
  min-height: 60px;
  //display: flex;
  border-radius: 4px;
  overflow: hidden;
  //align-items: center;
}

.order-box {
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: white;
  line-height: 36px;
  margin-top: auto;
  margin-bottom: auto;
  float: left;
  margin-right: 5px;
  margin-left: 5px;
  box-shadow: 5px 5px 10px 2px rgba(0,0,0,.2) !important;
}

.filter-box {
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  display: flex;
  flex-direction: row;
  min-width: auto;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background: white;
  line-height: 36px;
  margin-top: auto;
  margin-bottom: auto;
  float: left;
  margin-right: 5px;
  margin-left: 5px;
  //z-index: 2;
  box-shadow: 5px 5px 10px 2px rgba(0,0,0,.2) !important;
}

.field-box {
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  //display: flex;
  //flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  cursor: move;
  background: white;
  line-height: 36px;
  margin-bottom: 20px;
  float: left;
  margin-right: 5px;
  margin-left: 5px;
  box-shadow: 5px 5px 10px 2px rgba(0,0,0,.2) !important;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.order-box:last-child {
  border: none;
}

.field-box:last-child {
  border: none;
}

.order-list.cdk-drop-list-dragging .order-box:not(.cdk-drag-placeholder) .field-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.filter-mat-table-header {
  min-height: 64px;
  padding: 8px 24px 0;
}

.data-format {
  display: block;
  margin-bottom: 20px;
  margin-left: 20px;
}

tr.mat-row {
  height: 30px!important;
}

.bookmarkCard {
  display: flex;
  float: right;
}

#wrapperBookmark {
  position: relative;
  flex-direction: row;
  display: flex;
  margin-top: 5px;
}
