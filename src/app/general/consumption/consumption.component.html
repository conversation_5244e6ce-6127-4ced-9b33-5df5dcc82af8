<div class="d-flex justify-content-center">
    <div class="spinner-border" role="status" >
        <span class="sr-only" id="loading"></span>
    </div>
</div>

<mat-card class="page">
    <div class="consumptionTitleGranularity">
        <h1 class="title">CONSUMPTION STATISTICS</h1>
        <div class="precision">
            <div *ngIf="personalized === '1' || start != null">
                <nav><button class="precision_btn1" mat-stroked-button color="accent">Granularity
                    <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                </button>
                    <div class="dropdown-content1" >
                        <ul>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu0" value="Value1" (click)="selectOnlyThis('CheckGranu0')"/>Minute
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu1" value="Value1" (click)="selectOnlyThis('CheckGranu1')"/>Hour
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu2" value="Value1" (click)="selectOnlyThis('CheckGranu2')"/>Day
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu3" value="Value1" (click)="selectOnlyThis('CheckGranu3')"/>Month
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu4" value="Value1" (click)="selectOnlyThis('CheckGranu4')"/>Year
                                <span class="checkmark"></span></label></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <h4>Number of services selected : {{service.split(',').length}}</h4>
    <h4>Total consumption : {{ConsoTotal.toFixed(2)}} Go</h4>

    <section class="result_consp">
        <!--        <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle">Service(s) : </h2><h2 class="sub_content"> {{ service }}</h2></div>-->
        <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle"></h2><h2 class="sub_content"></h2></div>
        <ng-template class="elseBlock" #elseBlock1><mat-error>Please select one or more service(s) you have access to.</mat-error></ng-template>
    </section>
    <mat-card-content class="content">
        <section class="result_consp">
            <div *ngIf="(services?.length > 0); else elseBlock"></div>
            <ng-template #elseBlock><mat-error></mat-error></ng-template>
        </section>
        <div id="chartdiv"></div>
        <h2 class="graphTitle">Consumption In Go Per Date <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Consumption over time in Go" [matTooltipPosition]="'right'">info</mat-icon></h2>
    </mat-card-content>
</mat-card>
