import { Component, Inject, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import { AuthService } from '../../services/auth/auth.service';
import { AuthGuardService } from '../../services/auth/auth-guard.service';
import { DOCUMENT} from '@angular/common';
import { DashboardComponent } from '../dashboard/dashboard.component';
import {HttpParameterCodec} from "@angular/common/http";
import{ GlobalConstants } from '../../common/global-constants';
import { NavbarComponent } from '../../layout/navbar/navbar.component';

function hideloader() {
    document.getElementById('loading')
        .style.display = 'none';
}


export class HttpUrlEncodingCodec implements HttpParameterCodec {
    encodeKey(k: string): string { return standardEncoding(k); }
    encodeValue(v: string): string { return standardEncoding(v); }
    decodeKey(k: string): string { return decodeURIComponent(k); }
    decodeValue(v: string) { return decodeURIComponent(v); }
}
function standardEncoding(v: string): string {
    return encodeURIComponent(v);
}


@Component({
    selector: 'app-consumption-component',
    templateUrl: './consumption.component.html',
    styleUrls: ['./consumption.component.scss'],
    providers: [AuthService, AuthGuardService]

})
export class ConsumptionComponent implements OnInit {

    precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
    granu = {'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
    range = {'HOUR' : 61, 'DAY' : 25, 'WEEK' : 32, 'MONTH' : 32, 'YEAR' : 13, 'Personalized' : 100};
    topicMinute = GlobalConstants.topicNginx;
    topic = GlobalConstants.topicNginxLarge;
    granularity;
    personalized;
    requetes = {
        'HOUR1': '(select EXTRACT(MINUTE FROM __time), SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
            ' FROM ' + this.topicMinute + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL \'01\' HOUR AND CURRENT_TIMESTAMP  AND service_id IN (',
        'HOUR2': 'GROUP BY EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time) ASC LIMIT 1500)',

        'DAY1': '(select EXTRACT(HOUR FROM __time), SUM(sum_bytes_sent), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
            ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'01\' DAY  AND CURRENT_TIMESTAMP AND service_id IN (',
        'DAY2': 'GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

        'WEEK1': '(select EXTRACT(DAY FROM __time), SUM(sum_bytes_sent), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
            ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'06\' DAY AND CURRENT_TIMESTAMP AND service_id IN (',
        'WEEK2': 'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

        'MONTH1': '(select EXTRACT(DAY FROM __time), SUM(sum_bytes_sent), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
            ' FROM ' + this.topic + ' where __time > CURRENT_DATE - INTERVAL \'01\' MONTH + INTERVAL \'01\' DAY AND __time < CURRENT_DATE + INTERVAL \'01\' DAY AND service_id IN (',
        'MONTH2': 'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

        'YEAR1': '(select EXTRACT(MONTH FROM __time), SUM(sum_bytes_sent), EXTRACT(YEAR FROM __time)' +
            ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'01\' YEAR AND CURRENT_TIMESTAMP AND service_id IN (',
        'YEAR2': 'GROUP BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)) LIMIT 100)',
    };
    services = [];
    select1 = {
        'MINUTE' : '(select EXTRACT(MINUTE FROM __time), SUM(sum_bytes_sent), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
        'HOUR' : '(select EXTRACT(HOUR FROM __time), SUM(sum_bytes_sent), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
        'DAY' : '(select EXTRACT(DAY FROM __time), SUM(sum_bytes_sent), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
        'WEEK' : '(select EXTRACT(DAY FROM __time), SUM(sum_bytes_sent), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
        'MONTH' : '(select EXTRACT(MONTH FROM __time), SUM(sum_bytes_sent), EXTRACT(YEAR FROM __time)',
        'YEAR' : '(select EXTRACT(YEAR FROM __time), SUM(sum_bytes_sent)',
    };
    separation = {
        'HOUR': '(select \'SEPARATION\', \'SEPARATION\', \'SEPARATION\', \'SEPARATION\', \'SEPARATION\' FROM ' + this.topic + ' LIMIT 1)',
        'DAY': '(select \'SEPARATION\', \'SEPARATION\', \'SEPARATION\', \'SEPARATION\' FROM ' + this.topic + ' LIMIT 1)',
        'WEEK': '(select \'SEPARATION\', \'SEPARATION\', \'SEPARATION\', \'SEPARATION\' FROM ' + this.topic + ' LIMIT 1)',
        'MONTH': '(select \'SEPARATION\', \'SEPARATION\', \'SEPARATION\' FROM ' + this.topic + ' LIMIT 1)',
        'YEAR': '(select \'SEPARATION\', \'SEPARATION\'  FROM ' + this.topic + ' LIMIT 1)',
    };
    group = {
        'MINUTE': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time) ASC',
        'HOUR': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC',
        'DAY': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC ',
        'WEEK': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC',
        'MONTH': 'EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) ASC',
        'YEAR': 'EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time) ASC ',
    };
    checkbox = [];
    consumption;
    service = '';
    timeZone= '';
    var_to_return_tmp = '';
    precision_display = '';
    granu_display = '';
    dash = new DashboardComponent(this.http);
    navbar = new NavbarComponent(null, this.http, null);
    end;
    start;
    ConsoTotal = 0;


    constructor(private http: HttpClient, @Inject (DOCUMENT) document) {}

    ngOnInit(): void {

        this.start = localStorage.getItem('start_date');
        this.end = localStorage.getItem('end_date');
        this.precision_display = localStorage.getItem('precision');
        if (localStorage.getItem('granularity') == 'SECOND') {
            localStorage.setItem('granularity', 'MINUTE');
        }
        this.granu_display = localStorage.getItem('granularity');
        let date = localStorage.getItem('precision');
        this.service = localStorage.getItem('new_selected_services');

        if(localStorage.getItem('TimeZone')) {
            this.timeZone = localStorage.getItem('TimeZone');
        }

        this.personalized = localStorage.getItem('personalized');
        if (localStorage.getItem('granularity') === null || localStorage.getItem('granularity') === undefined) {
            this.granularity = 'DAY';
            localStorage.setItem('granularity', 'DAY');
        } else {
            this.granularity = localStorage.getItem('granularity')
        }


        if(localStorage.getItem('refresh')) {
            localStorage.removeItem('refresh');
            window.location.reload();
        }

        this.getServices().then( res => {

            let body;
            this.service = localStorage.getItem('new_selected_services');

            if (localStorage.getItem('precision').includes('complete')) {
                this.navbar.setLastIntervalDate(localStorage.getItem('precision'));
            }

            if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
                body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                    .set('Request', this.requetes[date + '1'] + this.service + ')' + this.requetes[date + '2'])
                    .set('TimeZone', localStorage.getItem('TimeZone'));
            } else {
                let start;
                let end;
                if (localStorage.getItem('granularity') === "DAY" || localStorage.getItem('granularity') === "MONTH" || localStorage.getItem('granularity') === "YEAR") {
                    start = localStorage.getItem('start_date');
                    end = localStorage.getItem('end_date');
                } else {
                    this.topic = GlobalConstants.topicNginx;
                    start = localStorage.getItem('start_date');
                    end = localStorage.getItem('end_date');
                }

                body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
                    .set('Request', this.select1[this.granularity] +
                        ' FROM ' + this.topic + ' where __time >= \'' + start + '\' AND __time <= \'' + end + '\' AND service_id IN (' + this.service +
                        ') GROUP BY ' + this.group[this.granularity] + ' LIMIT 1500)')
                    .set('TimeZone', localStorage.getItem('TimeZone'));
            }
            this.http.post(GlobalConstants.apiURL + 'log/',
                body,
                {
                    headers: new HttpHeaders()
                        .set('accept', 'application/json')
                        .set('Content-Type', 'application/x-www-form-urlencoded')
                        .set('Authorization', localStorage.getItem('token'))
                }).subscribe(response => {
                this.consumption = response['data'];
                let value: Array<any> = response['data'];
                if (value !== null && !value['error']) {
                    value = value.slice(1);
                    let jsonArr = [];
                    let jsonArrPrev = [];
                    let separation = false;
                    for (let item of value) {
                        if (item[0] !== 'SEPARATION' && separation === false) {
                            jsonArr.push(item);
                        } else {
                            separation = true;
                            jsonArrPrev.push(item);
                        }
                    }
                    jsonArrPrev = jsonArrPrev.slice(1);
                    let res = this.mergeArray(jsonArr, jsonArrPrev);
                    this.displayGraph(res);
                }
            });
        });
    }

    mergeArray(arr1, arr2) {
        // this.convertHour(arr1, arr2);
        let result = [];
        this.ConsoTotal = 0;
        for (let item of arr1) {
            this.ConsoTotal += item[1] / 1000000000;
            let date = this.getDate(item);
            result.push({
                'value': date,
                'present': item[1] / 1000000000,
            });
        }
        return result;
    }

    getDate(item) {
        let date;
        if (item.length == 2) {
            date = (item[0]).toString();
        } else if (item.length == 3) {
            date = item[0] + '/' + item[2];
        } else if (item.length == 4) {
            date = item[0] + '/' + item[2] + '/' + item[3];
        } else if (item.length == 5) {
            date = item[2] + '/' + item[3] + ' - ' + item[0] + 'h';
        } else if (item.length == 6) {
            if (item[0] < 10)
                date = item[2] + 'h0' + item[0];
            else
                date = item[2] + 'h' + item[0];
        }
        return date;
    }


    reload() {
        window.location.reload();
    }

    displayGraph(ArrResult: any) {

        let date = localStorage.getItem('precision');
        am4core.useTheme(am4themes_animated);
        var chart = am4core.create("chartdiv", am4charts.XYChart);

        chart.numberFormatter.numberFormat = "#.###'Go'";

        chart.data = ArrResult;
        // Create axes
        var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
        categoryAxis.dataFields.category = "value";
        categoryAxis.renderer.grid.template.location = 0;
        categoryAxis.renderer.minGridDistance = 30;
        categoryAxis.renderer.labels.template.horizontalCenter = "right";
        categoryAxis.renderer.labels.template.verticalCenter = "middle";
        categoryAxis.renderer.labels.template.rotation = 270;
        categoryAxis.tooltip.disabled = true;
        categoryAxis.renderer.minHeight = 110;

        var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
        valueAxis.title.text = "Consumption";
        valueAxis.min = 0;

        var series = chart.series.push(new am4charts.ColumnSeries());
        series.dataFields.valueY = "past";
        series.dataFields.categoryX = "value";
        series.clustered = false;
        // series.tooltipText = "consumption comparison not available";
        // series.tooltipText = "consumption a(n) " + date + " before (past): [bold]{valueY}[/]";

        var series2 = chart.series.push(new am4charts.ColumnSeries());
        series2.dataFields.valueY = "present";
        series2.dataFields.categoryX = "value";
        series2.clustered = false;
        // series2.columns.template.width = am4core.percent(65);
        series2.tooltipText = "consumption {categoryX} (present): [bold]{valueY}[/]";

        chart.cursor = new am4charts.XYCursor();
        chart.cursor.lineX.disabled = true;
        chart.cursor.lineY.disabled = true;
        chart.exporting.menu = new am4core.ExportMenu();
        chart.exporting.filePrefix = "AdvancedAnalyticsConsumption";
        chart.exporting.menu.align = "left";
        chart.exporting.menu.verticalAlign = "top";

        let title = 'Consumption report - Hexaglobe';
        let options = chart.exporting.getFormatOptions("pdf");
        options.addURL = false;
        chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
        pdf.doc.content.unshift({
            text: title,
            margin: [0, 30],
            style: {
            fontSize: 15,
            bold: true,
            }
        });
        // Add logo
        pdf.doc.content.unshift({
            image: GlobalConstants.hexaglobeLogo,
            fit: [119, 54]
        });
        return pdf;
        });
        chart.exporting.menu.items = [{
        "label": "...",
        "menu": [
            { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
            { "type": "png", "label": "PNG", "options": { "quality": 1 } },
            { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
            { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
            { "label": "Print", "type": "print" }
        ]
        }]; // Set the formats we want to allow the user to export in

        // CHECKED THE CHECKBOXES GRANULARITY
        let elemprec;
        let prec = localStorage.getItem('granularity');
        for (let key in this.granu) {
            if (this.granu[key] == prec) {
                elemprec = key;
            }
        }
        let granu1 = document.getElementById(elemprec) as HTMLInputElement;
        if (granu1 !== null) {
            granu1.checked = true;
        }
        // CHECKED THE CHECKBOXES GRANULARITY
        if (Response)
            hideloader()
    }

    validate() {
        this.ngOnInit();
    }

    async getServices() {

        if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
            this.service = localStorage.getItem('new_selected_services');
            let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
            for (let value of checkValues) {
                if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
                    this.checkbox.push(Number(value));
                }
            }
        }
        this.services = [];
        const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
            {
                headers: new HttpHeaders()
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/x-www-form-urlencoded')
                    .set('Authorization', localStorage.getItem('token'))
            }
        ).toPromise();


        for (let item of ret['data']) {
            this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
            this.services.push(item['name'])
        }

        //CHECKED AT LOADING THE NEW SREVICES CHECKBOXES
        let elem1 = [];

        if (localStorage.getItem('new_selected_services_checkboxes')) {
            elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
            for (let id of elem1) {
                let value = "serv" + id;
                let elem2 = document.getElementById(value) as HTMLInputElement;
                if (elem2 !== null) {
                    elem2.checked = true;
                }
            }
        } else {
            let value = "select-all";
            let elem2 = document.getElementById(value) as HTMLInputElement;
            if (elem2 !== null) {
                elem2.checked = true;
            }

            let check = [];
            let services = '';
            for(let i = 1; i<= this.services.length; i++) {
                check.push(i);
                services = services +  '\'' + this.services[i-1] + '\',';
            }
            localStorage.setItem('new_selected_services_checkboxes', check.toString());
            localStorage.removeItem('new_selected_services');
            localStorage.setItem('new_selected_services', services.slice(0, -1));
        }
        //END NEW CHECKED


        let elemprec;
        let prec = localStorage.getItem('precision');
        for (let key in this.precision) {
            if (this.precision[key] == prec) {
                elemprec = key;
            }
        }
        let precision = document.getElementById(elemprec) as HTMLInputElement;
        if (precision !== null) {
            precision.checked = true;
        }
        if (this.var_to_return_tmp.charAt(0) === ',') {
            this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
        }
        if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
            localStorage.setItem('initialized', '1');
            localStorage.setItem('new_selected_services', 'initializations');
            this.service = this.var_to_return_tmp;
            this.var_to_return_tmp = '';
            // this.validate();
        }

        if(localStorage.getItem('refresh')) {
            localStorage.removeItem('refresh');
            window.location.reload();
        }
    }

    selectOnlyThis(id) {
        let consumption = new ConsumptionComponent(this.http, DOCUMENT);

        for (let i = 0;i <= 4; i++)
        {
            let elem = document.getElementById("CheckGranu" + i) as HTMLInputElement;
            elem.checked = false;
        }
        let s = new Date(localStorage.getItem('start_date'));
        let e = new Date(localStorage.getItem('end_date'));
        var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date

        if (this.granu[id] === 'HOUR' && (diff > 7*24)) {
            alert("You cannot choose this granularity, reduce time interval to 7 days or less.");
            id = "CheckGranu2";
        }
        if (this.granu[id] === 'MINUTE' && (diff > 24)) {
            alert("You cannot choose this granularity, reduce hours interval to 24 hours or less.");
            id = "CheckGranu2";
        }

        let elem2 = document.getElementById(id) as HTMLInputElement;
        elem2.checked = true;
        id = this.granu[id];
        localStorage.setItem('granularity', id);
        consumption.reload();
    }

    // convertHour(arr1, arr2) {
    //     if (localStorage.getItem('precision') === "DAY" || localStorage.getItem('granularity') === "HOUR") {
    //         for (let elem of arr1) {
    //             if (elem[3] < 10) {
    //                 elem[3] = '0' + elem[3];
    //             }
    //             if (elem[2] < 10) {
    //                 elem[2] = '0' + elem[2];
    //             }
    //             if (elem[0] < 10) {
    //                 elem[0]  = '0' + elem[0];
    //             }
    //             let string_date = elem[4] + '-' + elem[3] + '-' + elem[2] + 'T' + elem[0] + ':00:00Z';
    //             let date = this.dash.personalizedTimeZoneHour(string_date);
    //             let date2 = new Date(date);
    //             elem[0] = date2.getUTCHours();
    //             elem[2] = date2.getUTCDate();
    //             elem[3] = date2.getUTCMonth() + 1;
    //             elem[4] = date2.getUTCFullYear();
    //         }
    //         for (let elem2 of arr2) {
    //             if (elem2[3] < 10) {
    //                 elem2[3] = '0' + elem2[3];
    //             }
    //             if (elem2[2] < 10) {
    //                 elem2[2] = '0' + elem2[2];
    //             }
    //             if (elem2[0] < 10) {
    //                 elem2[0]  = '0' + elem2[0];
    //             }
    //             let string_date = elem2[4] + '-' + elem2[3] + '-' + elem2[2] + 'T' + elem2[0] + ':00:00Z';
    //             let date = this.dash.personalizedTimeZoneHour(string_date);
    //             let date2 = new Date(date)
    //             elem2[0] = date2.getUTCHours();
    //             elem2[2] = date2.getUTCDate();
    //             elem2[3] = date2.getUTCMonth() + 1;
    //             elem2[4] = date2.getUTCFullYear();
    //         }
    //     } else if (localStorage.getItem('precision') === "HOUR" || localStorage.getItem('granularity') === "MINUTE") {
    //         for (let elem of arr1) {
    //             if (elem[3] < 10) {
    //                 elem[3] = '0' + elem[3];
    //             }
    //             if (elem[2] < 10) {
    //                 elem[2] = '0' + elem[2];
    //             }
    //             if (elem[0] < 10) {
    //                 elem[0]  = '0' + elem[0];
    //             }
    //             if (elem[4] < 10) {
    //                 elem[4]  = '0' + elem[4];
    //             }
    //             let string_date = elem[5] + '-' + elem[4] + '-' + elem[3] + 'T' + elem[2] + ':' + elem[0] + ':00Z';
    //             let date = this.dash.personalizedTimeZoneHour(string_date);
    //             let date2 = new Date(date)
    //             elem[0] = date2.getUTCMinutes();
    //             elem[2] = date2.getUTCHours();
    //             elem[3] = date2.getUTCDate() + 1;
    //             elem[4] = date2.getUTCMonth();
    //             elem[5] = date2.getUTCFullYear();
    //         }
    //         for (let elem2 of arr2) {
    //             if (elem2[3] < 10) {
    //                 elem2[3] = '0' + elem2[3];
    //             }
    //             if (elem2[2] < 10) {
    //                 elem2[2] = '0' + elem2[2];
    //             }
    //             if (elem2[0] < 10) {
    //                 elem2[0] = '0' + elem2[0];
    //             }
    //             if (elem2[4] < 10) {
    //                 elem2[4] = '0' + elem2[4];
    //             }
    //             let string_date = elem2[5] + '-' + elem2[4] + '-' + elem2[3] + 'T' + elem2[2] + ':' + elem2[0] + ':00Z';
    //             let date = this.dash.personalizedTimeZoneHour(string_date);
    //             let date2 = new Date(date)
    //             elem2[0] = date2.getUTCMinutes();
    //             elem2[2] = date2.getUTCHours();
    //             elem2[3] = date2.getUTCDate() + 1;
    //             elem2[4] = date2.getUTCMonth();
    //             elem2[5] = date2.getUTCFullYear();
    //         }
    //     }
    // }
}
