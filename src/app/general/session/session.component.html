<mat-card class="page">
    <div class="sessionTitleGranularity">
        <h1 class="title">SESSION</h1>
        <div class="precision">
            <div *ngIf="personalized === '1'">
                <nav><button class="precision_btn1" mat-stroked-button color="accent">Granularity
                    <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                </button>
                    <div class="dropdown-content1" >
                        <ul>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu0" value="Value1" (click)="selectOnlyThis('CheckGranu0')"/>Minute
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu1" value="Value1" (click)="selectOnlyThis('CheckGranu1')"/>Hour
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu2" value="Value1" (click)="selectOnlyThis('CheckGranu2')"/>Day
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu3" value="Value1" (click)="selectOnlyThis('CheckGranu3')"/>Month
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu4" value="Value1" (click)="selectOnlyThis('CheckGranu4')"/>Year
                                <span class="checkmark"></span></label></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <h4>Number of services selected : {{service.split(',').length}}</h4>

    <mat-card-content>
        <section class="result_consp">
            <!--            <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle">Service(s) : </h2><h2 class="sub_content"> {{ service }}</h2></div>-->
            <div *ngIf="(service !== '' && service !== 'initializations'); else elseBlock1 "><h2 class="subtitle"></h2><h2 class="sub_content"></h2></div>
            <ng-template class="elseBlock" #elseBlock1><mat-error></mat-error></ng-template>
        </section>
        <section class="numbers">
            <mat-card class="numbers_card">
                <div class="nb_stat" *ngIf="(nb_session !== [])">
                    <mat-icon class="identity">airline_seat_recline_normal</mat-icon>
                    <h2 class="stat_title">Sessions : <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique number of sessions according on the start of players" [matTooltipPosition]="'right'">info</mat-icon></h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="sessionsSessions"></span>
                        </div>
                    </div>
                    <h2 class="current_nb"> {{ nb_session[1] }}</h2>
                    <!--                    <hr>-->
                    <!--                    <h5>Comparing to the same previous interval</h5>-->
                    <!--                    <div class="nb_stat1" *ngIf="nb_session[1] - nb_session[2] < 0; else elseblock1"><h3 class="previous_nb2"> {{ nb_session[2] }}</h3><h4 class="previous_nb2"> {{ nb_session[0] }} %</h4></div>-->
                    <!--                    <ng-template class="elseblock" #elseblock1><h3 class="previous_nb1"> {{ nb_session[2] }} </h3><h4 class="previous_nb1"> + {{ nb_session[0] }} %</h4></ng-template>-->
                </div>
            </mat-card>
            <mat-card class="numbers_card">
                <div class="nb_stat" *ngIf="avg_duration !== []">
                    <mat-icon class="identity">query_builder</mat-icon>
                    <h2 class="stat_title">Average session duration : </h2>
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status" >
                            <span class="sr-only" id="averageSession"></span>
                        </div>
                    </div>
                    <h2 class="current_nb"> {{ formatDuration(avg_duration[1], true) }}</h2>
                    <!--                    <hr>-->
                    <!--                    <h5>Comparing to the same previous interval</h5>-->
                    <!--                    <div class="nb_stat1" *ngIf="avg_duration[1] - avg_duration[2] < 0; else elseblock1"><h3 class="previous_nb2"> {{ avg_duration[2] }} s</h3><h4 class="previous_nb2"> {{ avg_duration[0] }} %</h4></div>-->
                    <!--                    <ng-template class="elseblock" #elseblock1><h3 class="previous_nb1"> {{ avg_duration[2] }} s</h3><h4 class="previous_nb1"> + {{ avg_duration[0] }} %</h4></ng-template>-->
                </div>
            </mat-card>

        </section>
    </mat-card-content>

    <mat-card class="graph_card">
        <div id="chartdiv"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graph"></span>
            </div>
        </div>
        <h2 class="graphTitle">Number of Users through Time</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdiv2"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graph2"></span>
            </div>
        </div>
        <h2 class="graphTitle">Number of Sessions through Time</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdiv3"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graph3"></span>
            </div>
        </div>
        <h2 class="graphTitle">Number of Sessions depending of session time (seconds) <mat-icon class="p-r15" style="cursor: pointer" matTooltip="Unique numbers of session sort by session duration (seconds) interval" [matTooltipPosition]="'right'">info</mat-icon></h2>
    </mat-card>

</mat-card>

