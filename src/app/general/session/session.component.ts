import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { HttpUrlEncodingCodec } from '@angular/common/http';
import{ GlobalConstants } from '../../common/global-constants';
import { DashboardComponent } from '../dashboard/dashboard.component';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import * as am4charts from '@amcharts/amcharts4/charts';
import * as am4maps from '@amcharts/amcharts4/maps'
import { DOCUMENT } from '@angular/common';
import { isNumber } from '@amcharts/amcharts4/core';
import { NavbarComponent } from '../../layout/navbar/navbar.component';

function hidesessions() {
  document.getElementById('sessionsSessions')
      .style.display = 'none';
}

function hide_avgsessions() {
  document.getElementById('averageSession')
      .style.display = 'none';
}

function hide_graph() {
  document.getElementById('graph')
      .style.display = 'none';
}

function hide_graph2() {
  document.getElementById('graph2')
      .style.display = 'none';
}

function hide_graph3() {
  document.getElementById('graph3')
      .style.display = 'none';
}


@Component({
  selector: 'app-session',
  templateUrl: './session.component.html',
  styleUrls: ['./session.component.scss']
})
export class SessionComponent implements OnInit {

  checkbox = [];
  var_to_return_tmp = '';
  services = [];
  service = '';
  nb_session = [];
  avg_duration = [];
  topic_viewing = GlobalConstants.topicViewing;
  topic = GlobalConstants.topicNginx;
  topic_session = GlobalConstants.topicNginxPersonalized;
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  interval2 = {'HOUR': '\'2\' HOUR', 'DAY': '\'2\' DAY', 'WEEK': '\'14\' DAY', 'MONTH': '\'2\' MONTH', 'YEAR': '\'2\' YEAR'};
  timeZone = '';
  precision_display = '';
  dash = new DashboardComponent(this.http);
  navbar = new NavbarComponent(null, this.http, null);
  granularity;
  end;
  start;
  personalized;
  granu = {'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
  requetes = {
    'HOUR1': '(select EXTRACT(MINUTE FROM __time), count(DISTINCT(remote_addr)), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
        ' FROM ' + this.topic + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL \'01\' HOUR AND CURRENT_TIMESTAMP  AND service_id IN (',
    'HOUR2': 'GROUP BY EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC LIMIT 100)',

    'DAY1': '(select EXTRACT(HOUR FROM __time), count(DISTINCT(remote_addr)), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
        ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'01\' DAY  AND CURRENT_TIMESTAMP AND service_id IN (',
    'DAY2': 'GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

    'WEEK1': '(select EXTRACT(DAY FROM __time), count(DISTINCT(remote_addr)), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
        ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'06\' DAY AND CURRENT_TIMESTAMP AND service_id IN (',
    'WEEK2': 'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

    'MONTH1': '(select EXTRACT(DAY FROM __time), count(DISTINCT(remote_addr)), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)' +
        ' FROM ' + this.topic + ' where __time > CURRENT_DATE - INTERVAL \'01\' MONTH + INTERVAL \'01\' DAY AND __time < CURRENT_DATE + INTERVAL \'01\' DAY AND service_id IN (',
    'MONTH2': 'GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)), MIN(EXTRACT(DAY FROM __time)) LIMIT 100)',

    'YEAR1': '(select EXTRACT(MONTH FROM __time), count(DISTINCT(remote_addr)), EXTRACT(YEAR FROM __time)' +
        ' FROM ' + this.topic + ' where __time BETWEEN  CURRENT_TIMESTAMP - INTERVAL \'01\' YEAR AND CURRENT_TIMESTAMP AND service_id IN (',
    'YEAR2': 'GROUP BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) ORDER BY MIN(EXTRACT(YEAR FROM __time)), MIN(EXTRACT(MONTH FROM __time)) LIMIT 100)',
  };

  requetesSession = {
    'HOUR1': '(select _min, SUM(measure), hhour, dday, mmonth, yyear from(select DISTINCT(EXTRACT(MINUTE FROM __time)) as _min, count(DISTINCT(remote_addr)) as measure, EXTRACT(HOUR FROM __time) as hhour, EXTRACT(DAY FROM __time) as dday, EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear ' +
        ' FROM ' + this.topic_session + ' where __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL \'01\' HOUR  and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (',
    'HOUR2': ' GROUP BY EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC )',

    'DAY1': '(select hhour, SUM(measure), dday, mmonth, yyear from(select DISTINCT(EXTRACT(HOUR FROM __time)) as hhour, count(DISTINCT(remote_addr)) as measure, EXTRACT(DAY FROM __time) as dday, EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear ' +
        ' FROM ' + this.topic_session + ' where  __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL \'01\' DAY and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (',
    'DAY2': ' GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time))',

    'WEEK1': '(select dday, SUM(measure), mmonth, yyear from(select DISTINCT(EXTRACT(DAY FROM __time)) as dday, count(DISTINCT(remote_addr)) as measure,  EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear' +
        ' FROM ' + this.topic_session + ' where   __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL \'06\' DAY and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (',
    'WEEK2': ' GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(HOUR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time))',

    'MONTH1': '(select dday, SUM(measure), mmonth, yyear from(select DISTINCT(EXTRACT(DAY FROM __time)) as dday, count(DISTINCT(remote_addr)) as measure, EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear' +
        ' FROM ' + this.topic_session + ' where __time < CURRENT_TIMESTAMP + INTERVAL \'01\' DAY  AND __time > CURRENT_TIMESTAMP - INTERVAL \'01\' MONTH + INTERVAL \'01\' DAY and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (',
    'MONTH2': ' GROUP BY EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(HOUR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time))',

    'YEAR1': '(select mmonth, SUM(measure), yyear from(select DISTINCT(EXTRACT(MONTH FROM __time)) as mmonth, count(DISTINCT(remote_addr)) as measure, EXTRACT(YEAR FROM __time) as yyear ' +
        ' FROM ' + this.topic_session + ' where  __time < CURRENT_TIMESTAMP AND __time > CURRENT_TIMESTAMP - INTERVAL \'01\' YEAR and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (',
    'YEAR2': ' GROUP BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time))',
  };




  select1 = {
    'MINUTE' : '(select EXTRACT(MINUTE FROM __time), count(DISTINCT(remote_addr)), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
    'HOUR' : '(select EXTRACT(HOUR FROM __time), count(DISTINCT(remote_addr)), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
    'DAY' : '(select EXTRACT(DAY FROM __time), count(DISTINCT(remote_addr)), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
    'WEEK' : '(select EXTRACT(DAY FROM __time), count(DISTINCT(remote_addr)), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)',
    'MONTH' : '(select EXTRACT(MONTH FROM __time), count(DISTINCT(remote_addr)), EXTRACT(YEAR FROM __time)',
    'YEAR' : '(select EXTRACT(YEAR FROM __time), count(DISTINCT(remote_addr))',
  };

  select1Session = {
    'MINUTE' : '(select _min, sum(measure), hhour, dday, mmonth, yyear from(select EXTRACT(MINUTE FROM __time) as _min, count(DISTINCT(remote_addr)) as measure, EXTRACT(HOUR FROM __time) as hhour, EXTRACT(DAY FROM __time) as dday,  EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear',
    'HOUR' : '(select hhour, sum(measure),dday, mmonth, yyear from(select EXTRACT(HOUR FROM __time) as hhour, count(DISTINCT(remote_addr)) as measure, EXTRACT(DAY FROM __time) as dday,  EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear',
    'DAY' : '(select dday, sum(measure), mmonth, yyear from(select EXTRACT(DAY FROM __time) as dday, count(DISTINCT(remote_addr)) as measure, EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear',
    'WEEK' : '(select dday, sum(measure),mmonth, yyear from(select EXTRACT(DAY FROM __time) as dday, count(DISTINCT(remote_addr)) as measure, EXTRACT(MONTH FROM __time) as mmonth, EXTRACT(YEAR FROM __time) as yyear',
    'MONTH' : '(select mmonth, sum(measure), yyear from(select EXTRACT(MONTH FROM __time) as mmonth, count(DISTINCT(remote_addr)) as measure, EXTRACT(YEAR FROM __time) as yyear',
    'YEAR' : '(select yyear, sum(measure) from(select EXTRACT(YEAR FROM __time) as yyear, count(DISTINCT(remote_addr)) as measure',
  };

  sessionSelectAliasPersonalized = {
    'MINUTE' : 'yyear, mmonth, dday, hhour, _min',
    'HOUR' : 'yyear, mmonth, dday, hhour',
    'DAY' : 'yyear, mmonth, dday',
    'WEEK' : 'yyear, mmonth, dday',
    'MONTH' : 'yyear, mmonth',
    'YEAR' : 'yyear',
  };

  sessionSelectAlias = {
    'MINUTE' : 'yyear, mmonth, dday, hhour, _min',
    'HOUR' : 'yyear, mmonth, dday, hhour, _min',
    'DAY' : 'yyear, mmonth, dday, hhour',
    'WEEK' : 'yyear, mmonth, dday',
    'MONTH' : 'yyear, mmonth, dday',
    'YEAR' : 'yyear, mmonth',
  };

  group = {
    'MINUTE': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC',
    'HOUR': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC',
    'DAY': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC ',
    'WEEK': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC',
    'MONTH': 'EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) ASC',
    'YEAR': 'EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time) ASC ',
  };

  groupSession = {
    'MINUTE': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC',
    'HOUR': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time) ASC',
    'DAY': 'EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC ',
    'WEEK': 'EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ASC',
    'MONTH': 'EXTRACT(HOUR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time) ASC',
    'YEAR': 'EXTRACT(HOUR FROM __time), EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ORDER BY EXTRACT(YEAR FROM __time) ASC ',
  };

  constructor(private http: HttpClient) { }

  ngOnInit(): void {

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      window.location.reload();
    }
    this.start = localStorage.getItem('start_date');
    this.end = localStorage.getItem('end_date');
    this.personalized = localStorage.getItem('personalized');
    if (localStorage.getItem('granularity') == 'SECOND') {
      localStorage.setItem('granularity', 'MINUTE');
    }
    this.precision_display = localStorage.getItem('precision');
    if(localStorage.getItem('TimeZone')) {
      this.timeZone = localStorage.getItem('TimeZone');
    }
    if (localStorage.getItem('granularity') === null || localStorage.getItem('granularity') === undefined) {
      this.granularity = 'DAY';
      localStorage.setItem('granularity', 'DAY');
    } else {
      this.granularity = localStorage.getItem('granularity')
    }
    this.getServices().then(res => {

      if (localStorage.getItem('precision').includes('complete')) {
        this.navbar.setLastIntervalDate(localStorage.getItem('precision'));
      }


      if (localStorage.getItem('new_selected_services') != 'initializations')
        this.service = localStorage.getItem('new_selected_services');

      this.getUserGraphNumbers().then(res1 => {
        this.displayUserGraph(res1);
      });
      this.getSessionsGraphNumbers().then(res2 => {
        this.displaySessionGraph(res2);
      });
      this.getSessionsDurationGraphNumbers().then(res3 => {
        this.displaySessionDurationGraph(res3);
      });
      this.getNbSession().then(nb_session => {
        this.nb_session = nb_session;
      });
      this.getAvgDuration().then(avg => {
        this.avg_duration = avg;
      });
    });
  }

  async getNbSession() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select sum(measure) from(select count(DISTINCT(remote_addr)) as measure ' +
              'FROM ' + this.topic_session + ' WHERE __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  AND CURRENT_TIMESTAMP ' +
              ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)))')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select sum(measure) from(select count(DISTINCT(remote_addr)) as measure ' +
              'FROM ' + this.topic_session + ' WHERE __time <= \'' +  localStorage.getItem('end_date') + '\'' + 'and __time >= \'' + localStorage.getItem('start_date') + '\' ' +
              ' AND service_id IN (' + this.service + ') and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 GROUP BY EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time)))')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    result['data'][0] = this.getDifference(result['data']);
    if (result['data'][2] == undefined) {
      result['data'][0] = "No previous data";
    }
    if (result['data'][1] == null) {
      result['data'][1] = "No data available";
    }
    hidesessions()

    return (result['data']);
  }


  async getAvgDuration() {
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select AVG(numbers) from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
              'FROM ' + this.topic_viewing + ' where __time > CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + '  ' +
              ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 group by remote_addr) src )')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select AVG(numbers) from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
              'FROM ' + this.topic_viewing + ' where __time < \'' +  localStorage.getItem('end_date') + '\'' + ' AND __time > \'' + localStorage.getItem('start_date') + '\'' + ' ' +
              ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 group by remote_addr) src )')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const result = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    result['data'][0] = this.getDifference(result['data']);
    if (result['data'][2] == undefined) {
      result['data'][0] = "No previous data";
    }
    if (result['data'][1] == null) {
      result['data'][1] = "No data available";
    }

    //CHECKED AT LOADING THE NEW SREVICES CHECKBOXES
    let elem1 = [];
    if (localStorage.getItem('new_selected_services_checkboxes')) {
      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    } else {
      let value = "select-all";
      let elem2 = document.getElementById(value) as HTMLInputElement;
      if (elem2 !== null) {
        elem2.checked = true;
      }

      let check = [];
      let services = '';
      for(let i = 1; i<= this.services.length; i++) {
        check.push(i);
        services = services +  '\'' + this.services[i-1] + '\',';
      }
      localStorage.setItem('new_selected_services_checkboxes', check.toString());
      localStorage.removeItem('new_selected_services');
      localStorage.setItem('new_selected_services', services.slice(0, -1));

      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    }
    //END NEW CHECKED

    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }
    hide_avgsessions()
    return result['data'];
  }

  getDifference(arr) {
    let val1;
    let val2;
    if (arr[1] && arr[2]) {
      val1 = arr[1][0];
      val2 = arr[2][0];
    } else {
      val1 = arr[1];
      val2 = arr[2];
    }
    let result = (val1 - val2);
    let operation = Number(result / val2)*100;
    if (isNaN(operation)) {
      return 0;
    } else {
      return Math.round((operation) * 10) / 10;
    }
  }

  reload() {
    window.location.reload();
  }

  async getServices() {

    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();
    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.ngOnInit()
    }

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh')
      window.location.reload();
    }
  }

  async getSessionsGraphNumbers() {

    let result = [];
    let body;
    let date = localStorage.getItem('precision');
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', this.requetesSession[date + '1'] + this.service + ')' + this.requetesSession[date + '2'] + ' GROUP BY ' + this.sessionSelectAlias[date] + ' ORDER BY ' + this.sessionSelectAlias[date] + ')')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', this.select1Session[this.granularity] +
              ' FROM ' + this.topic_session + ' where __time <= \'' + localStorage.getItem('end_date') +  '\' AND __time >= \'' + localStorage.getItem('start_date') + '\' and extension in (\'m3u8\', \'mpd\') and (REVERSE(request_filename) LIKE \'8u3m.tsilyalp%\' or extension = \'mpd\') and status = 200 AND service_id IN (' + this.service +
              ') GROUP BY ' + this.groupSession[this.granularity] + ' LIMIT 2000) GROUP BY ' + this.sessionSelectAliasPersonalized[this.granularity] + ' ORDER BY ' + this.sessionSelectAliasPersonalized[this.granularity] + ')')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();
    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    // this.convertHour(value);
    for (let item of value) {
      let date = this.getDate(item);
      result.push({
        'hour': date,
        'Nb_Sessions': item[1],
      });
    }
    return result;
  }

  async getSessionsDurationGraphNumbers() {

    let result = [];
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select DISTINCT(numbers), count(*)  from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
              'FROM ' + this.topic_viewing + ' where __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
              ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 group by session_id, remote_addr) group by numbers limit 1000 ')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', 'select DISTINCT(numbers), count(*)  from(select SUM(\\"sum_viewing_duration_class_s\\") as numbers ' +
              'FROM ' + this.topic_viewing + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
              ' AND service_id IN (' + this.service + ') and sum_viewing_duration_class_s > 5 group by session_id, remote_addr) group by numbers limit 1000 ')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();


    if (resultat['data']['error'])
      return {};
    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    for (let item of value) {
      result.push({
        'Time': item[0],
        'Nb_Sessions': item[1],
      });
    }
    return result;
  }

  async getUserGraphNumbers() {

    let result = [];
    let body;
    let date = localStorage.getItem('precision');
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', this.requetes[date + '1'] + this.service + ')' + this.requetes[date + '2'])
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', this.select1[this.granularity] +
              ' FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' AND service_id IN (' + this.service +
              ') GROUP BY ' + this.group[this.granularity] + ' LIMIT 360)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    // this.convertHour(value);
    for (let item of value) {
      let date = this.getDate(item);
      result.push({
        'hour': date,
        'Nb_Users': item[1],
      });
    }
    return result;
  }


  formatDuration(duration: any, round: boolean = false): string {
      return isNaN(duration) ? duration : Math.round(duration) + ' s';
  }

  getDate(item) {
    let date;
    if (item.length == 3) {
      date = item[0] + '/' + item[2];
    } else if (item.length == 4) {
      date = item[0] + '/' + item[2] + '/' + item[3];
    } else if (item.length == 5) {
      date = item[2] + '/' + item[3] + ' - ' + item[0] + 'h';
    } else if (item.length == 5) {
      date = item[2] + 'h:' + item[0] + '-' + item[3] + '/' + item[4];
    } else if (item.length == 6) {
      date = item[2] + 'h:' + item[0] + '-' + item[3] + '/' + item[4];
    } else if (item.length == 2) {
      date = item[0].toString();
    }
    return date;
  }

  displayUserGraph( res)
  {
    hide_graph();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv", am4charts.XYChart);
    chart.data = res;
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "hour";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
      if (target.dataItem && target.dataItem.index && 2 == 2) {
        return dy + 25;
      }
      return dy;
    });
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = "Nb Users";
    valueAxis.min = 0;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = "Nb_Users";
    series.dataFields.categoryX = "hour";
    series.name = "Nb_Users";
    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    var columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsers";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Users through time - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    // CHECKED THE CHECKBOXES GRANULARITY
    let elemprec;
    let prec = localStorage.getItem('granularity');
    for (let key in this.granu) {
      if (this.granu[key] == prec) {
        elemprec = key;
      }
    }
    let granu1 = document.getElementById(elemprec) as HTMLInputElement;
    if (granu1 !== null) {
      granu1.checked = true;
    }
    // CHECKED THE CHECKBOXES GRANULARITY
  }


  displaySessionGraph(res)
  {
    hide_graph2();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv2", am4charts.XYChart);
    chart.data = res;
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "hour";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
      if (target.dataItem && target.dataItem.index && 2 == 2) {
        return dy + 25;
      }
      return dy;
    });
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = "Nb Sessions";
    valueAxis.min = 0;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = "Nb_Sessions";
    series.dataFields.categoryX = "hour";
    series.name = "Nb_Sessions";
    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    var columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsSessions";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Sessions through time - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

  }

  displaySessionDurationGraph( res)
  {
    hide_graph2();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdiv3", am4charts.XYChart);
    chart.data = res;
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Time";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 270;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
      if (target.dataItem && target.dataItem.index && 2 == 2) {
        return dy + 25;
      }
      return dy;
    });
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = "Nb Sessions";
    valueAxis.min = 0;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = "Nb_Sessions";
    series.dataFields.categoryX = "Time";
    series.name = "Nb_Sessions";
    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    var columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsSessionDuration";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = 'Number of Sessions depending of session time (seconds) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
    hide_graph3();
  }

  validate() {
    this.ngOnInit();
  }

  selectOnlyThis(id) {
    let session = new SessionComponent(this.http);

    for (let i = 0;i <= 4; i++)
    {
      let elem = document.getElementById("CheckGranu" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let s = new Date(localStorage.getItem('start_date'));
    let e = new Date(localStorage.getItem('end_date'));

    e.setHours(e.getHours() + (e.getTimezoneOffset() / 60));
    s.setHours(s.getHours() + (s.getTimezoneOffset() / 60));
    var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date

    if (this.granu[id] === 'HOUR' && (diff > 7*24)) {
      alert("You cannot choose this granularity, reduce time interval to 7 days or less.");
      id = "CheckGranu2";
    }
    if (this.granu[id] === 'MINUTE' && (diff > 24)) {
      alert("You cannot choose this granularity, reduce hours interval to 24 hours or less.");
      id = "CheckGranu2";
  }

    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    id = this.granu[id];
    localStorage.setItem('granularity', id);
    session.reload();
  }

  //
  // convertHour(arr1) {
  //   if (localStorage.getItem('precision') === "DAY" || localStorage.getItem('granularity') === "HOUR") {
  //     for (let elem of arr1) {
  //       if (elem[3] < 10) {
  //         elem[3] = '0' + elem[3];
  //       }
  //       if (elem[2] < 10) {
  //         elem[2] = '0' + elem[2];
  //       }
  //       if (elem[0] < 10) {
  //         elem[0]  = '0' + elem[0];
  //       }
  //       let string_date = elem[4] + '-' + elem[3] + '-' + elem[2] + 'T' + elem[0] + ':00:00';
  //       // let date = this.dash.personalizedTimeZoneHour(string_date);
  //       let date2 = new Date(string_date);
  //       elem[0] = date2.getHours();
  //       elem[2] = date2.getDate();
  //       elem[3] = date2.getMonth() + 1;
  //       elem[4] = date2.getFullYear();
  //     }
  //   } else if (localStorage.getItem('precision') === "HOUR") {
  //     for (let elem of arr1) {
  //       if (elem[3] < 10) {
  //         elem[3] = '0' + elem[3];
  //       }
  //       if (elem[2] < 10) {
  //         elem[2] = '0' + elem[2];
  //       }
  //       if (elem[0] < 10) {
  //         elem[0]  = '0' + elem[0];
  //       }
  //       if (elem[4] < 10) {
  //         elem[4]  = '0' + elem[4];
  //       }
  //       let string_date = elem[5] + '-' + elem[4] + '-' + elem[3] + 'T' + elem[2] + ':' + elem[0] + ':00Z';
  //       // let date = this.dash.personalizedTimeZoneHour(string_date);
  //       let date2 = new Date(string_date);
  //       elem[0] = date2.getMinutes();
  //       elem[2] = date2.getHours();
  //       elem[3] = date2.getDate() + 1;
  //       elem[4] = date2.getMonth();
  //       elem[5] = date2.getFullYear();
  //     }
  //   }
  // }
}
