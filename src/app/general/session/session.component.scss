.result_consp {
  text-align: center;
  padding-bottom: 20px;
}

/* Change the text color of the tooltip */
::ng-deep .mat-tooltip {
  color: white !important;
}

.title {
  margin-top: 0;
  margin-bottom: 0;
}

.stat_title {
  text-align: center;
  display: inline-block;
  padding-left: 60px;
  text-decoration: underline;
}

.previous_nb1 {
  margin-left: 10px;
  color: green;
}

mat-icon {
  color: #737373;
}

.previous_nb2 {
  margin-left: 10px;
  color: red;
}

.identity {
  font-size: 50px;
  position: absolute;
}

.nb_stat {
  width: 100%;
  text-align: center;
  display: inline-block;
}

.numbers_card {
  display: inline-block;
  width: 100%;
  margin-left: 20px;
  margin-right: 20px;
  background: white;
}

.graph_card {
  display: inline-block;
  width: 96%;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;
  background: white;
}


.numbers {
  display: flex;
  padding-bottom: 40px;
}

.current_nb {
  margin-left: 10px;
  color: #455da5;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

.sub_content {
  display: inline-block;
}

.page {
  background-color: whitesmoke;
}

.result_precision {
  text-align: center;
}

.result_timezone {
  text-align: center;
}

#loading{
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

#graph, #graph2, #graph3 {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

#sessionsSessions, #averageSession {
  position: absolute;
  left: 55%;
  top: 90%;
  z-index: 1;
  width: 55px;
  height: 55px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

#chartdiv {
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
}

#chartdiv2, #chartdiv3  {
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
}


.graphTitle {
  text-align: center;
  padding-top: 20px;
}

.sessionTitleGranularity {
  display: flex;
}

.precision {
  margin-left: 20px;
  align-self: center;
}

.precision:hover .dropdown-content1 {
  display: block;
}

.dropdown-content1 {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.precision_btn1 {
  display: inline-block;
  margin-right: 20px;
}

.dropdown-content1 {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.container1 {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

.container1:hover input ~ .checkmark {
  background-color: #2196F3;
}

.container1 input:checked ~ .checkmark {
  background-color: #2196F3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container1 input:checked ~ .checkmark:after {
  display: block;
}

.container1 .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
