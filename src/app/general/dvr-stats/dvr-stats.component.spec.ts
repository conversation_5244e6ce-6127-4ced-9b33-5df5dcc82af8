import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DvrStatsComponent } from './dvr-stats.component';

describe('DvrStatsComponent', () => {
  let component: DvrStatsComponent;
  let fixture: ComponentFixture<DvrStatsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ DvrStatsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DvrStatsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
