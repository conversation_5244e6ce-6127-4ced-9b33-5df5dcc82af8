

<mat-card class="page">
    <div class="DVRTitleGranularity">
        <h1 class="title">DVR</h1>
        <div class="precision">
            <div *ngIf="personalized === '1' || start != null">
                <nav><button class="precision_btn1" mat-stroked-button color="accent">Granularity
                    <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
                </button>
                    <div class="dropdown-content1" >
                        <ul>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu0" value="Value1" (click)="selectGranu('CheckGranu0')"/>Minute
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu1" value="Value1" (click)="selectGranu('CheckGranu1')"/>Hour
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu2" value="Value1" (click)="selectGranu('CheckGranu2')"/>Day
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu3" value="Value1" (click)="selectGranu('CheckGranu3')"/>Month
                                <span class="checkmark"></span></label></li>
                            <li><label class="container1">
                                <input type="checkbox" id="CheckGranu4" value="Value1" (click)="selectGranu('CheckGranu4')"/>Year
                                <span class="checkmark"></span></label></li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>



    <h4>Number of services selected : {{service.split(',').length}}</h4>


    <div *ngIf="hide_live == false; else showLiveData">
        <button mat-flat-button color="accent" (click)="hideShowLiveData()" style="margin-bottom: 20px">Hide Live Data</button>
    </div>
    <ng-template #showLiveData>
        <div>
            <button mat-flat-button color="accent" (click)="hideShowLiveData()" style="margin-bottom: 20px">Show Live Data</button>
        </div>
      </ng-template>


    <mat-card class="graph_card">
        <div id="chartdivUserPerInterval"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphUserPerIntervalSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">Nb user per time delay between request and file generation</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivBreakDown"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphDelayBreakDownSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">Users per time delay between request and file generation by services</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivDelay"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphDelaySpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">Nb user per DVR interval through time</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivTimeSeriesDvr"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphTimeSeriesDownSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">Nb user per DVR interval through time (Stacked)</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivTimeSeriesQuantiles"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphTimeSeriesQuantileDownSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">DVR time interval quantiles over time</h2>
    </mat-card>

    <mat-card class="graph_card">
        <mat-card-content>
            <div id="deviationDistributionChart"></div>
            <h2 class="graphTitle">DVR Standard deviation normal distribution<mat-icon class="p-r15" style="cursor: pointer" matTooltip="The normal distribution of the DVR time standard deviation." [matTooltipPosition]="'right'">info</mat-icon></h2>
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status" >
                    <span class="sr-only" id="deviationDistributionSpin"></span>
                </div>
            </div>
        </mat-card-content>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivTimeSeriesStdev"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphTimeSeriesStddevDownSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">DVR time interval standard deviation over time</h2>
    </mat-card>

    <mat-card class="graph_card">
        <div id="chartdivStackedConsumption"></div>
        <div class="d-flex justify-content-center">
            <div class="spinner-border" role="status" >
                <span class="sr-only" id="graphStackedDownSpinLoad"></span>
            </div>
        </div>
        <h2 class="graphTitle">Seconds consumption of logs depending of time delay between request and file generation by stacked services</h2>
    </mat-card>

</mat-card>
