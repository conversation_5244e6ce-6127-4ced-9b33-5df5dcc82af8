.result_timezone {
  text-align: center;
}

.title {
  margin-top: 0;
  margin-bottom: 0;
}

.subtitle {
  text-decoration: underline;
  display: inline-block;
  padding-right: 20px;
}

mat-icon {
  color: #737373;
}

.DVRTitleGranularity {
  display: flex;
}

.sub_content {
  display: inline-block;
}

#deviationDistributionChart {
  width: 100%;
  height: 400px;
}

#chartdivDelay, #chartdivBreakDown, #chartdivStackedConsumption, #chartdivTimeSeriesDvr, #chartdivUserPerInterval, #chartdivTimeSeriesQuantiles, #chartdivTimeSeriesStdev {
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
}

#graphDelaySpinLoad, #graphDelayBreakDownSpinLoad, #graphStackedDownSpinLoad, #graphTimeSeriesDownSpinLoad, #graphUserPerIntervalSpinLoad, #graphTimeSeriesQuantileDownSpinLoad, #graphTimeSeriesStddevDownSpinLoad, #deviationDistributionSpin {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 1;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.graphTitle {
  text-align: center;
  padding-top: 20px;
}

.graph_card {
  margin-bottom: 50px;
}

.precision {
  margin-left: 20px;
  align-self: center;
}

.precision:hover .dropdown-content1 {
  display: block;
}

.dropdown-content1 {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
}

.precision_btn1 {
  display: inline-block;
  margin-right: 20px;
}


.container1 {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

.container1:hover input ~ .checkmark {
  background-color: #2196F3;
}

.container1 input:checked ~ .checkmark {
  background-color: #2196F3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container1 input:checked ~ .checkmark:after {
  display: block;
}

.container1 .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
