import { Component, OnInit } from '@angular/core';
import { GlobalConstants } from '../../common/global-constants';
import { HttpClient, HttpHeaders, HttpParams, HttpUrlEncodingCodec } from '@angular/common/http';
import * as am4core from '@amcharts/amcharts4/core';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import * as am5plugins_exporting from "@amcharts/amcharts5/plugins/exporting";
import * as am4charts from '@amcharts/amcharts4/charts';
import * as am5 from "@amcharts/amcharts5";
import * as am5xy from "@amcharts/amcharts5/xy";
import { DOCUMENT } from '@angular/common';

function hide_graph_delay() {
  document.getElementById('graphDelaySpinLoad')
      .style.display = 'none';
}

function hide_graph_user_per_interval() {
  document.getElementById('graphUserPerIntervalSpinLoad')
      .style.display = 'none';
}


function hide_graph_delay_breakdown() {
  document.getElementById('graphDelayBreakDownSpinLoad')
      .style.display = 'none';
}

function hide_graph_delay_stacked() {
  document.getElementById('graphStackedDownSpinLoad')
      .style.display = 'none';
}

function hide_graph_delay_timeseries() {
  document.getElementById('graphTimeSeriesDownSpinLoad')
      .style.display = 'none';
}

function hide_graph_delay_timeseries_quantile() {
  document.getElementById('graphTimeSeriesQuantileDownSpinLoad')
      .style.display = 'none';
}

function hide_graph_delay_timeseries_stddev() {
  document.getElementById('graphTimeSeriesStddevDownSpinLoad')
      .style.display = 'none';
}

function hide_deviationDistribution() {
  document.getElementById('deviationDistributionSpin')
      .style.display = 'none';
}


@Component({
  selector: 'app-dvr-stats',
  templateUrl: './dvr-stats.component.html',
  styleUrls: ['./dvr-stats.component.scss']
})

export class DvrStatsComponent implements OnInit {

  hide_live = false;
  var_to_return_tmp = '';
  services = [];
  service = '';
  personalized;
  checkbox = [];
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  granu = {'CheckGranu0': 'MINUTE', 'CheckGranu1': 'HOUR', 'CheckGranu2': 'DAY', 'CheckGranu3': 'MONTH', 'CheckGranu4': 'YEAR'};
  timeZone = '';
  topic = GlobalConstants.topicNginxPersonalized;
  interval1 = {'HOUR': '\'1\' HOUR', 'DAY': '\'1\' DAY', 'WEEK': '\'7\' DAY', 'MONTH': '\'1\' MONTH', 'YEAR': '\'1\' YEAR'};
  precision_to_granu = {'HOUR': 'MINUTE', 'DAY': 'HOUR', 'WEEK': 'DAY', 'MONTH': 'DAY', 'YEAR': 'MONTH'};
  start;
  end;
  select = {
    'MINUTE' : ' EXTRACT(MINUTE FROM __time), count(DISTINCT \\\"remote_addr\\\"), EXTRACT(HOUR FROM __time), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'HOUR' : ' EXTRACT(HOUR FROM __time), count(DISTINCT \\\"remote_addr\\\"), EXTRACT(DAY FROM __time),  EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'DAY' : ' EXTRACT(DAY FROM __time), count(DISTINCT \\\"remote_addr\\\"), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'WEEK' : ' EXTRACT(DAY FROM __time), count(DISTINCT \\\"remote_addr\\\"), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'MONTH' : ' EXTRACT(MONTH FROM __time), count(DISTINCT \\\"remote_addr\\\"), EXTRACT(YEAR FROM __time) ',
    'YEAR' : ' EXTRACT(YEAR FROM __time), count(DISTINCT \\\"remote_addr\\\") ',
  };
  group = {
    'MINUTE': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MINUTE FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'HOUR': 'EXTRACT(DAY FROM __time), EXTRACT(HOUR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'DAY': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ',
    'WEEK': 'EXTRACT(YEAR FROM __time), EXTRACT(MONTH FROM __time), EXTRACT(DAY FROM __time) ',
    'MONTH': 'EXTRACT(MONTH FROM __time), EXTRACT(YEAR FROM __time) ',
    'YEAR': 'EXTRACT(YEAR FROM __time) ',
  };
  constructor(private http: HttpClient) { }

  ngOnInit(): void {
    this.start = localStorage.getItem('start_date');
    this.end = localStorage.getItem('end_date');

    if(localStorage.getItem('TimeZone')) {
      this.timeZone = localStorage.getItem('TimeZone');
    }
    if(localStorage.getItem('hide_live')) {
      this.hide_live = localStorage.getItem('hide_live') == "true";
    }
    if (localStorage.getItem('granularity') == 'SECOND') {
      localStorage.setItem('granularity', 'MINUTE');
    }

    this.getServices().then(res => {
      this.getDelayBetweenReqAndFileGen().then(result => {
        this.displayDelayGraph(result);
      });
      this.getDelayBetweenReqAndFileGen().then(result => {
        this.displayGraphTimeDvrStacked(result);
      });
      this.getDelayBetweenReqAndFileGenPerUser().then(result => {
        this.displayDelayGraphPerUser(result);
      });
      this.getDelayBetweenReqAndFileGenServicesBreakdown().then(final => {
        this.displayDVRStatsBreakdownByServices(final);
      });
      this.getDelayBetweenReqAndFileGenStackedSecondsConsumption().then(finalStacked => {
        this.displayDVRStatsStackedSecondsConsumption(finalStacked);
      });
      this.getQuantilesDelayDVROverTime().then(quantiles => {
        this.displayQuantilesDvr(quantiles);
      });
      this.getDeviationDistribution().then( result => {
        this.displayDeviationDistribution(result);
      });
      this.getStandardDeviationDVROverTime().then(standardDeviation => {
        this.displayStandardDeviationDvr(standardDeviation);
      });
    });
  }


  async getDelayBetweenReqAndFileGen() {

    let result = [];
    let granularity;

    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
      if (granularity == null) {
        granularity = 'HOUR';
        localStorage.setItem('granularity', 'HOUR');
      }

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_10_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_10_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_10_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_10_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();


    //CHECKED AT LOADING THE NEW SERVICES CHECKBOXES
    let elem1 = [];
    if (localStorage.getItem('new_selected_services_checkboxes')) {
      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    } else {
      let value = "select-all";
      let elem2 = document.getElementById(value) as HTMLInputElement;
      if (elem2 !== null) {
        elem2.checked = true;
      }

      let check = [];
      let services = '';
      for(let i = 1; i<= this.services.length; i++) {
        check.push(i);
        services = services +  '\'' + this.services[i-1] + '\',';
      }
      localStorage.setItem('new_selected_services_checkboxes', check.toString());
      localStorage.removeItem('new_selected_services');
      localStorage.setItem('new_selected_services', services.slice(0, -1));

      elem1 = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let id of elem1) {
        let value = "serv" + id;
        let elem2 = document.getElementById(value) as HTMLInputElement;
        if (elem2 !== null) {
          elem2.checked = true;
        }
      }
    }
    //END NEW CHECKED

    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H', 'total'];
    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    let global = [];
    for (let item of value) {
      // Extraire la date
      const date = item[0];
      // Transformer le reste du tableau
      const transformedArray = item.slice(1).map((value, index) => {
        return [time_interval[index], value, date];
      });

      for (let elem of transformedArray) {
        if (elem) {
          global.push(elem);
        }
      }
    }
    return global;
  }


  displayDelayGraph(res)  {

    hide_graph_delay();

    //am4core.useTheme(am4themes_dark);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivDelay", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    chart.cursor = new am4charts.XYCursor();
    chart.scrollbarX = new am4core.Scrollbar();
    chart.cursor.xAxis = dateAxis;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsersPerDVRTimeInterval";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = ' DVR - User number per DVR time interval over time - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }

    var datas = [];

    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of res) {
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }
    for (let i of datas) {
      createSeries(i, i);
    }
    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = s + " : {valueY}";
      // series.tooltip.pointerOrientation = "vertical";
      series.tooltip.background.fillOpacity = 0.5;
      var segment = series.segments.template;
      segment.interactionsEnabled = true;
      var hoverState = segment.states.create("hover");
      hoverState.properties.strokeWidth = 3;
      var dimmed = segment.states.create("dimmed");
      dimmed.properties.stroke = am4core.color("#dadada");
      segment.events.on("over", function(event) {
        processOver(event.target.parent.parent.parent);
      });
      segment.events.on("out", function(event) {
        processOut(event.target.parent.parent.parent);
      });

      var data = [];
      let i = 0;
      for (let elem of res) {
        if (elem[0] == s) {
          let value = elem[1];
          elem[2] = elem[2].split('.')[0]
          if (elem[2].charAt(0) == '\'')
            elem[2] = elem[2].substring(1);
          let final = new Date(elem[2]);
          elem[2] = final.toISOString();
          var dataItem = { date: elem[2].substring(0, final) };
          dataItem["value" + s] = value;
          data.push(dataItem);
        }
      }
      series.data = data;
      return series;
    }

    chart.exporting.adapter.add("data", function(data, target) {
      var data1 = [];
      chart.series.each(function(series) {
        for(var i = 0; i < series.data.length; i++) {
          series.data[i].name = series.name;
          data1.push(series.data[i]);
        }
      });
      return { data: data1 };
    });

    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;
    chart.legend.itemContainers.template.events.on("over", function(event) {
      processOver(event.target.dataItem.dataContext);
    });
    chart.legend.itemContainers.template.events.on("out", function(event) {
      processOut(event.target.dataItem.dataContext);
    });
    function processOver(hoveredSeries) {
      hoveredSeries.toFront();
      hoveredSeries.segments.each(function(segment) {
        segment.setState("hover");
      });
      chart.series.each(function(series) {
        if (series != hoveredSeries) {
          series.bulletsContainer.setState("dimmed");
        }
      });
    }
    function processOut(hoveredSeries) {
      chart.series.each(function(series) {
        series.bulletsContainer.setState("default");
      });
    }
    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      let totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) {
            // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }




  async getDelayBetweenReqAndFileGenPerUser() {

    let result = [];
    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_10_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_10_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_10_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_10_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();


    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H', 'total'];
    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1)[0];
    let x  = 0;

    if (this.hide_live) {
      value = value.slice(1);
      time_interval.shift();
    }
    for (let item of value) {
      result.push({
        'Time': time_interval[x],
        'count': item,
      });
      x += 1;
    }
    return result;
  }



  displayDelayGraphPerUser( res)
  {
    hide_graph_user_per_interval();
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivUserPerInterval", am4charts.XYChart);
    chart.data = res;
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "Time";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.minGridDistance = 30;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.verticalCenter = "middle";
    categoryAxis.renderer.labels.template.rotation = 0;
    categoryAxis.tooltip.disabled = true;
    categoryAxis.renderer.minHeight = 110;
    categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
      if (target.dataItem && target.dataItem.index && 2 == 2) {
        return dy + 25;
      }
      return dy;
    });
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.title.text = "Nb Users";
    // valueAxis.min = 0;
    // valueAxis.max = 100;
    var series = chart.series.push(new am4charts.ColumnSeries());
    series.dataFields.valueY = "count";
    series.dataFields.categoryX = "Time";
    series.name = "count";
    // series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]%";
    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
    series.columns.template.fillOpacity = .8;
    var columnTemplate = series.columns.template;
    columnTemplate.strokeWidth = 2;
    columnTemplate.strokeOpacity = 1;

    chart.cursor = new am4charts.XYCursor();
    chart.cursor.lineX.disabled = true;
    chart.cursor.lineY.disabled = true;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsDelay";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = ' DVR - Nb Users depending of time delay between request and file generation - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
  }


  async getDelayBetweenReqAndFileGenServicesBreakdown() {

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select service_id, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_0_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by service_id limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select service_id, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN remote_addr END) AS range_0_10_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN remote_addr END) AS range_10_30_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN remote_addr END) AS range_30_60_s, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN remote_addr END) AS range_1_2_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN remote_addr END) AS range_2_5_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN remote_addr END) AS range_5_10_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN remote_addr END) AS range_10_30_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN remote_addr END) AS range_30_60_m, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN remote_addr END) AS range_1_2_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN remote_addr END) AS range_2_12h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN remote_addr END) AS range_12_24_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN remote_addr END) AS range_24_48_h, ' +
          'COUNT(DISTINCT CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN remote_addr END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by service_id limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};
    let value: Array<any> = resultat['data'];
    value = value.slice(1);

    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H', 'total'];

    if (this.hide_live) {
      for (let val of value) {
        val.splice(1, 1)
      }
    }

    let final = [];
    for (let arr of value) {
      const transformedArray = arr.slice(1).map((value, index) => {
        return {'timeInterval': time_interval[index], [arr[0]]: value};
      });
      for (let elem of transformedArray) {
        final.push(elem);
      }
    }

    return final;
  }


  displayDVRStatsBreakdownByServices(final) {

    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H'];
    let sample =  [{"timeInterval": "<10s", "": 0,},{"timeInterval": "<30s", "": 0,}, {"timeInterval": "<1m", "": 0,},{"timeInterval": "<2m", "": 0}, {"timeInterval": "<5m", "": 0}, {"timeInterval": "<10m", "": 0},
      {"timeInterval": "<30m", "": 0}, {"timeInterval": "<1H", "": 0}, {"timeInterval": "<2H", "": 0}, {"timeInterval": "<12H", "": 0}, {"timeInterval": "<24H", "": 0}, {"timeInterval": "<48H", "": 0}, {"timeInterval": "<72H", "": 0}];

    if (this.hide_live) {
      time_interval.shift();
      sample.shift();
    }

    for (let elem of final) {
      if (elem[Object.keys(elem)[1]] > 0 && time_interval.indexOf(elem["timeInterval"]) != -1)
        sample[time_interval.indexOf(elem["timeInterval"])][Object.keys(elem)[1]] = elem[Object.keys(elem)[1]]
    }

    hide_graph_delay_breakdown();

    var root = am5.Root.new("chartdivBreakDown");
    root.setThemes([
      am5themes_Animated.new(root)
    ]);

    var chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      layout: root.verticalLayout
    }));

    var legend = chart.children.push(
        am5.Legend.new(root, {
          centerX: am5.p50,
          x: am5.p50
        })
    );
    let data = sample;
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
      categoryField: "timeInterval",
      renderer: am5xy.AxisRendererX.new(root, {
        minGridDistance: 30
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    xAxis.data.setAll(data);

    let yAxis = chart.yAxes.push(
        am5xy.ValueAxis.new(root, {
          // min: 0,
          // max: 100,
          renderer: am5xy.AxisRendererY.new(root, {})
        })
    );

    chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    let display_legend = true;
    if (this.service.split(',').length  > 10)
      display_legend = false;

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnalyticsServicesDVRBreakdown"
    });


    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Nb Users depending of time delay between request and file generation by services - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function makeSeries(name, fieldName) {
      var series = chart.series.push(am5xy.ColumnSeries.new(root, {
        name: name,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: fieldName,
        categoryXField: "timeInterval"
      }));

      series.columns.template.setAll({
        // tooltipText: "{name} : [bold]{valueY}%",
        tooltipText: "{name} : [bold]{valueY}",
        // width: am5.percent(95),
        tooltipY: 0
      });
      series.data.setAll(data);
      series.appear();
      series.bullets.push(function () {
        return am5.Bullet.new(root, {
          locationY: 0,
          sprite: am5.Label.new(root, {
            text: "{valueY}",
            fill: root.interfaceColors.get("alternativeText"),
            centerY: 0,
            centerX: am5.p50,
            populateText: true
          })
        });
      });

      if (display_legend)
        legend.data.push(series);
    }
    for (let elem of this.service.split(',')) {
      elem = elem.replace('\'', '');
      elem = elem.replace('\'', '');
      makeSeries(elem, elem);
      }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));
    chart.appear(1000, 100);
  }



  hideShowLiveData() {
    this.hide_live = !this.hide_live;
    localStorage.setItem('hide_live', String(this.hide_live));
    window.location.reload();
  }


  async getDelayBetweenReqAndFileGenStackedSecondsConsumption() {

    let body;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select service_id, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN sum_duration_s END) AS range_0_10_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN sum_duration_s END) AS range_10_30_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN sum_duration_s END) AS range_30_60_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN sum_duration_s END) AS range_1_2_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN sum_duration_s END) AS range_2_5_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN sum_duration_s END) AS range_5_10_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN sum_duration_s END) AS range_10_30_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN sum_duration_s END) AS range_30_60_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN sum_duration_s END) AS range_1_2_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN sum_duration_s END) AS range_2_12h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN sum_duration_s END) AS range_12_24_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN sum_duration_s END) AS range_24_48_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN sum_duration_s END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by service_id limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select service_id, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 0 AND delay_btw_req_and_filegen < 10 THEN sum_duration_s END) AS range_0_10_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 10 AND delay_btw_req_and_filegen < 30 THEN sum_duration_s END) AS range_10_30_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 30 AND delay_btw_req_and_filegen < 60 THEN sum_duration_s END) AS range_30_60_s, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 60 AND delay_btw_req_and_filegen < 120 THEN sum_duration_s END) AS range_1_2_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 120 AND delay_btw_req_and_filegen < 300 THEN sum_duration_s END) AS range_2_5_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 300 AND delay_btw_req_and_filegen < 600 THEN sum_duration_s END) AS range_5_10_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 600 AND delay_btw_req_and_filegen < 1800 THEN sum_duration_s END) AS range_10_30_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 1800 AND delay_btw_req_and_filegen < 3600 THEN sum_duration_s END) AS range_30_60_m, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 3600 AND delay_btw_req_and_filegen < 7200 THEN sum_duration_s END) AS range_1_2_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 7200 AND delay_btw_req_and_filegen < 12*3600 THEN sum_duration_s END) AS range_2_12h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 12*3600 AND delay_btw_req_and_filegen < 24*3600 THEN sum_duration_s END) AS range_12_24_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 24*3600 AND delay_btw_req_and_filegen < 48*3600 THEN sum_duration_s END) AS range_24_48_h, ' +
          'SUM( CASE WHEN delay_btw_req_and_filegen >= 48*3600 AND delay_btw_req_and_filegen < 72*3600 THEN sum_duration_s END) AS range_48_72_h ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by service_id limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};
    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H'];

    if (this.hide_live) {
      for (let val of value) {
        val.splice(1, 1)
      }
    }

    let final = [];
    for (let arr of value) {
      const transformedArray = arr.slice(1).map((value, index) => {
        return {'timeInterval': time_interval[index], [arr[0]]: value};
      });
      for (let elem of transformedArray) {
        final.push(elem);
      }
    }

    return final;
  }


  displayDVRStatsStackedSecondsConsumption(final) {

    let time_interval = ['<10s', '<30s', '<1m', '<2m', '<5m', '<10m', '<30m', '<1H', '<2H', '<12H', '<24H', '<48H', '<72H'];
    let sample =  [{"timeInterval": "<10s", "": 0,}, {"timeInterval": "<30s", "": 0,}, {"timeInterval": "<1m", "": 0,},{"timeInterval": "<2m", "": 0}, {"timeInterval": "<5m", "": 0}, {"timeInterval": "<10m", "": 0},
      {"timeInterval": "<30m", "": 0}, {"timeInterval": "<1H", "": 0}, {"timeInterval": "<2H", "": 0}, {"timeInterval": "<12H", "": 0}, {"timeInterval": "<24H", "": 0}, {"timeInterval": "<48H", "": 0}, {"timeInterval": "<72H", "": 0}];

    if (this.hide_live) {
      time_interval.shift();
      sample.shift();
    }

    for (let elem of final) {
      if (elem[Object.keys(elem)[1]] > 0 && time_interval.indexOf(elem["timeInterval"]) != -1)
        sample[time_interval.indexOf(elem["timeInterval"])][Object.keys(elem)[1]] = elem[Object.keys(elem)[1]]
    }

    hide_graph_delay_stacked();
    var root = am5.Root.new("chartdivStackedConsumption");
    root.setThemes([
      am5themes_Animated.new(root)
    ]);
    var chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
    }));
    var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "none"
    }));
    cursor.lineY.set("visible", false);

    var data = sample;
    var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
      categoryField: "timeInterval",
      startLocation: 0.5,
      endLocation: 0.5,
      renderer: am5xy.AxisRendererX.new(root, {}),
      tooltip: am5.Tooltip.new(root, {})
    }));
    xAxis.data.setAll(data);
    var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {})
    }));

    chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));

    let exporting = am5plugins_exporting.Exporting.new(root, {
      menu: am5plugins_exporting.ExportingMenu.new(root, {
        align: "left",
      }),
      jpgOptions: {
        disabled: true
      },
      pdfOptions: {
        addURL: false
      },
      filePrefix: "AdvancedAnaltyicsServicesDVRStackedSecondConsumption"
    });

    exporting.events.on("pdfdocready", function(event) {
    // Add title to the beginning
    event.doc.content.unshift({
      text:  'Seconds consumption of logs depending of time delay between request and file generation by stacked services - Hexaglobe',
      margin: [0, 30],
      style: {
        fontSize: 15,
        bold: true,
      }
      });
      // Add logo
      event.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
    });

    function createSeries(name, field) {
      var series = chart.series.push(am5xy.LineSeries.new(root, {
        name: name,
        xAxis: xAxis,
        yAxis: yAxis,
        stacked:true,
        valueYField: field,
        categoryXField: "timeInterval",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "[bold]{name}[/]\n{valueY}s"
        })
      }));
      series.fills.template.setAll({
        fillOpacity: 0.5,
        visible: true
      });

      series.data.setAll(data);
      series.appear(1000);
    }
    for (let elem of this.service.split(',')) {
      elem = elem.replace('\'', '');
      elem = elem.replace('\'', '');
      createSeries(elem, elem);
    }
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));
    chart.appear(1000, 100);
  }


  // Function to get the user services
  async getServices() {
    if (localStorage.getItem('new_selected_services') !== null && localStorage.getItem('new_selected_services_checkboxes') !== null) {
      this.service = localStorage.getItem('new_selected_services');
      let checkValues = localStorage.getItem('new_selected_services_checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name'])
    }
    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    //initialization below code is use to do not begin with no value when connect
    if (!localStorage.getItem('new_selected_services') || localStorage.getItem('new_selected_services') === 'initialization') {
      // if (!localStorage.getItem('new_selected_services') && !localStorage.getItem('initialized')) {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('new_selected_services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.ngOnInit()
    }

    let elemprec;
    let prec = localStorage.getItem('precision');
    for (let key in this.precision) {
      if (this.precision[key] == prec) {
        elemprec = key;
      }
    }
    let precision = document.getElementById(elemprec) as HTMLInputElement;
    if (precision !== null) {
      precision.checked = true;
    }

    // CHECKED THE CHECKBOXES GRANULARITY
    let elemprec1;
    let prec1 = localStorage.getItem('granularity');
    for (let key in this.granu) {
      if (this.granu[key] == prec1) {
        elemprec1 = key;
      }
    }
    let granu1 = document.getElementById(elemprec1) as HTMLInputElement;
    if (granu1 !== null) {
      granu1.checked = true;
    }
    // CHECKED THE CHECKBOXES GRANULARITY

    if(localStorage.getItem('refresh')) {
      localStorage.removeItem('refresh');
      window.location.reload();
    }
  }


  //Function to display the time graph in front
  displayGraphTimeDvrStacked(res) {


    hide_graph_delay_timeseries();
    //am4core.useTheme(am4themes_dark);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivTimeSeriesDvr", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    chart.cursor = new am4charts.XYCursor();
    chart.scrollbarX = new am4core.Scrollbar();
    chart.cursor.xAxis = dateAxis;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsersPerDVRTimeInterval";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";

    let title = ' DVR - User number per DVR time interval over time (stacked) - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in

    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }

    var datas = [];

    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of res) {
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }
    for (let i of datas) {
      createSeries(i, i);
    }
    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = s + " : {valueY}";
      series.stacked =  true;
      // series.tooltip.pointerOrientation = "vertical";
      series.tooltip.background.fillOpacity = 0.5;
      var segment = series.segments.template;
      segment.interactionsEnabled = true;
      var hoverState = segment.states.create("hover");
      hoverState.properties.strokeWidth = 3;
      var dimmed = segment.states.create("dimmed");
      dimmed.properties.stroke = am4core.color("#dadada");
      segment.events.on("over", function(event) {
        processOver(event.target.parent.parent.parent);
      });
      segment.events.on("out", function(event) {
        processOut(event.target.parent.parent.parent);
      });

      series.fillOpacity = 0.5;

      var data = [];
      let i = 0;
      for (let elem of res) {
        if (elem[0] == s) {
          let value = elem[1];
          elem[2] = elem[2].split('.')[0]
          if (elem[2].charAt(0) == '\'')
            elem[2] = elem[2].substring(1);
          let final = new Date(elem[2]);
          elem[2] = final.toISOString();
          var dataItem = { date: elem[2].substring(0, final) };
          dataItem["value" + s] = value;
          data.push(dataItem);
        }
      }

      series.data = data;
      return series;
    }

    chart.exporting.adapter.add("data", function(data, target) {
      var data1 = [];
      chart.series.each(function(series) {
        for(var i = 0; i < series.data.length; i++) {
          series.data[i].name = series.name;
          data1.push(series.data[i]);
        }
      });
      return { data: data1 };
    });

    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;
    chart.legend.itemContainers.template.events.on("over", function(event) {
      processOver(event.target.dataItem.dataContext);
    });
    chart.legend.itemContainers.template.events.on("out", function(event) {
      processOut(event.target.dataItem.dataContext);
    });
    function processOver(hoveredSeries) {
      hoveredSeries.toFront();
      hoveredSeries.segments.each(function(segment) {
        segment.setState("hover");
      });
      chart.series.each(function(series) {
        if (series != hoveredSeries) {
          series.bulletsContainer.setState("dimmed");
        }
      });
    }
    function processOut(hoveredSeries) {
      chart.series.each(function(series) {
        series.bulletsContainer.setState("default");
      });
    }
    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      let totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) {
            // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }


  async getQuantilesDelayDVROverTime() {

    let body;
    let granularity;

    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
      if (granularity == null) {
        granularity = 'HOUR';
        localStorage.setItem('granularity', 'HOUR');
      }

    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.1)) AS P10, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.25)) AS P25, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.5)) AS P50, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.75)) AS P75, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.9)) AS P90, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.99)) AS P99, ' +
          'ROUND(AVG(delay_btw_req_and_filegen)) AS AVERAGE ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.1)) AS P10, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.25)) AS P25, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.5)) AS P50, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.75)) AS P75, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.9)) AS P90, ' +
          'ROUND(APPROX_QUANTILE_DS(delay_btw_req_and_filegen, 0.99)) AS P99, ' +
          'ROUND(AVG(delay_btw_req_and_filegen)) AS AVERAGE ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();


    let quantiles = ['<Q10', '<Q25', '<Q50', '<Q75', '<Q90', '<Q99', 'AVERAGE'];
    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    value = value.slice(1);

    let global = [];
    for (let item of value) {
      // Extraire la date
      const date = item[0];
      // Transformer le reste du tableau
      const transformedArray = item.slice(1).map((value, index) => {
        return [quantiles[index], value, date];
      });

      for (let elem of transformedArray) {
        if (elem) {
          global.push(elem);
        }
      }
    }
    return global;
  }


  //Function to display the time graph in front
  displayQuantilesDvr(res) {
    hide_graph_delay_timeseries_quantile();
    //am4core.useTheme(am4themes_dark);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivTimeSeriesQuantiles", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    chart.cursor = new am4charts.XYCursor();
    chart.scrollbarX = new am4core.Scrollbar();
    chart.cursor.xAxis = dateAxis;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsersPerDVRTimeInterval";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = ' DVR - DVR time interval quantiles over time - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.logarithmic = true;
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }
    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of res) {
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }
    for (let i of datas) {
      createSeries(i, i);
    }
    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = s + " : {valueY}";
      // series.stacked =  true;
      // series.fillOpacity = 0.5;
      // series.tooltip.pointerOrientation = "vertical";
      series.tooltip.background.fillOpacity = 0.5;
      var segment = series.segments.template;
      segment.interactionsEnabled = true;
      var hoverState = segment.states.create("hover");
      hoverState.properties.strokeWidth = 3;
      var dimmed = segment.states.create("dimmed");
      dimmed.properties.stroke = am4core.color("#dadada");
      segment.events.on("over", function(event) {
        processOver(event.target.parent.parent.parent);
      });
      segment.events.on("out", function(event) {
        processOut(event.target.parent.parent.parent);
      });
      var data = [];
      let i = 0;
      for (let elem of res) {
        if (elem[0] == s) {
          let value = elem[1];
          elem[2] = elem[2].split('.')[0]
          if (elem[2].charAt(0) == '\'')
            elem[2] = elem[2].substring(1);
          let final = new Date(elem[2]);
          elem[2] = final.toISOString();
          var dataItem = { date: elem[2].substring(0, final) };
          dataItem["value" + s] = value;
          data.push(dataItem);
        }
      }
      series.data = data;
      return series;
    }
    chart.exporting.adapter.add("data", function(data, target) {
      var data1 = [];
      chart.series.each(function(series) {
        for(var i = 0; i < series.data.length; i++) {
          series.data[i].name = series.name;
          data1.push(series.data[i]);
        }
      });
      return { data: data1 };
    });
    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;
    chart.legend.itemContainers.template.events.on("over", function(event) {
      processOver(event.target.dataItem.dataContext);
    });
    chart.legend.itemContainers.template.events.on("out", function(event) {
      processOut(event.target.dataItem.dataContext);
    });
    function processOver(hoveredSeries) {
      hoveredSeries.toFront();
      hoveredSeries.segments.each(function(segment) {
        segment.setState("hover");
      });
      chart.series.each(function(series) {
        if (series != hoveredSeries) {
          series.bulletsContainer.setState("dimmed");
        }
      });
    }
    function processOut(hoveredSeries) {
      chart.series.each(function(series) {
        series.bulletsContainer.setState("default");
      });
    }
    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      let totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) {
            // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }


  async getDeviationDistribution() {
    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
    if (granularity == null) {
      granularity = 'HOUR';
      localStorage.setItem('granularity', 'HOUR');
    }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      let request = '(select ROUND(AVG(delay_btw_req_and_filegen)) AS mean, ' +
      'ROUND(STDDEV(delay_btw_req_and_filegen)) AS stddev, ' +
      'ROUND(AVG(delay_btw_req_and_filegen) - STDDEV(delay_btw_req_and_filegen)) AS lower_bound_1_stddev, ' +
      'ROUND(AVG(delay_btw_req_and_filegen) + STDDEV(delay_btw_req_and_filegen)) AS upper_bound_1_stddev ' +
      'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP ' +
      'AND service_id IN ('+ this.service +') and stream_time_ms > 0)';

      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', encodeURI(request).split('+').join('%2B').split('\\').join(''))
          .set('Encoded', true.toString())
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      let request = '(select ROUND(AVG(delay_btw_req_and_filegen)) AS mean, ' +
      'ROUND(STDDEV(delay_btw_req_and_filegen)) AS stddev, ' +
      'ROUND(AVG(delay_btw_req_and_filegen) - STDDEV(delay_btw_req_and_filegen)) AS lower_bound_1_stddev, ' +
      'ROUND(AVG(delay_btw_req_and_filegen) + STDDEV(delay_btw_req_and_filegen)) AS upper_bound_1_stddev ' +
      'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\'' +
      'AND service_id IN ('+ this.service +') and stream_time_ms > 0)';
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', encodeURI(request).split('+').join('%2B').split('\\').join(''))
          .set('Encoded', true.toString())
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }

    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();

    if (resultat['data']['error'])
      return {};

    let value: Array<any> = resultat['data'];
    return value;

  }


  displayDeviationDistribution(result) {
    hide_deviationDistribution();
    var chart = am4core.create("deviationDistributionChart", am4charts.XYChart);
    var valueAxisX = chart.xAxes.push(new am4charts.ValueAxis());
    valueAxisX.min = 0;
    var valueAxisY = chart.yAxes.push(new am4charts.ValueAxis());
    var series = chart.series.push(new am4charts.LineSeries());
    series.dataFields.valueX = "x";
    series.dataFields.valueY = "y";
    series.dataFields.value = "value";
    series.sequencedInterpolation = true;
    series.tooltip.pointerOrientation = "vertical";
    series.tooltipText = "x:{x} y:{y}";
    series.tensionX = 0.95;
    chart.cursor = new am4charts.XYCursor();
    chart.cursor.snapToSeries = series;
    var lowerBound = result[1][2], upperBound = result[1][3];
    var normalY = (x, mean, stdDev) => Math.exp((-0.5) * Math.pow((x - mean) / stdDev, 2));
    var generatePoints = (lowerBound, upperBound) => {
      var stdDev = result[1][1];
      var min = lowerBound - 2 * stdDev;
      var max = upperBound + 2 * stdDev;
      var unit = (max - min) / 6;
      var points = []
      for(var i = min; i < max; i = i + unit){
          points.push(i);
      }
      return points;
    }
    var mean = result[1][0];
    var stdDev = result[1][1];
    var points = generatePoints(lowerBound, upperBound);
    series.data = points.map(x => ({ x, y: normalY(x, mean, stdDev)}));
  }


  async getStandardDeviationDVROverTime() {

    let body;
    let granularity;
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null)
      granularity = this.precision_to_granu[localStorage.getItem('precision')];
    else
      granularity = localStorage.getItem('granularity');
      if (granularity == null) {
        granularity = 'HOUR';
        localStorage.setItem('granularity', 'HOUR');
      }
    if (localStorage.getItem('start_date') === null && localStorage.getItem('end_date') === null) {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'ROUND(STDDEV(delay_btw_req_and_filegen)) AS stdev ' +
          'FROM ' + this.topic + ' where  __time BETWEEN CURRENT_TIMESTAMP - INTERVAL ' + this.interval1[localStorage.getItem('precision')] + ' AND CURRENT_TIMESTAMP and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    } else {
      body = new HttpParams({encoder: new HttpUrlEncodingCodec()})
          .set('Request', '(select DATE_TRUNC(\'' + granularity + '\', __time), ' +
          'ROUND(STDDEV(delay_btw_req_and_filegen)) AS stdev ' +
          'FROM ' + this.topic + ' where __time >= \'' + localStorage.getItem('start_date') + '\' AND __time <= \'' + localStorage.getItem('end_date') +  '\' and stream_time_ms > 0 ' +
          'AND service_id IN ('+ this.service +') group by DATE_TRUNC(\'' + granularity + '\', __time) ORDER BY DATE_TRUNC(\'' + granularity + '\', __time) ASC limit 50000)')
          .set('TimeZone', localStorage.getItem('TimeZone'));
    }
    const resultat = await this.http.post(GlobalConstants.apiURL + 'log/',
        body,
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }).toPromise();
    if (resultat['data']['error'])
      return {};
    let value: Array<any> = resultat['data'];
    value = value.slice(1);
    let global = [];
    for (let item of value) {
      // Extraire la date
      const date = item[0];
      // Transformer le reste du tableau
      const transformedArray = item.slice(1).map((value) => {
        return ['stdev', value, date];
      });
      for (let elem of transformedArray) {
        if (elem) {
          global.push(elem);
        }
      }
    }
    return global;
  }


  //Function to display the time graph in front
  displayStandardDeviationDvr(res) {
    hide_graph_delay_timeseries_stddev();
    //am4core.useTheme(am4themes_dark);
    am4core.useTheme(am4themes_animated);
    var chart = am4core.create("chartdivTimeSeriesStdev", am4charts.XYChart);
    chart.dateFormatter.inputDateFormat = "yyyy-MM-ddTHH:mm:ss.SSSZ";
    chart.cursor = new am4charts.XYCursor();
    chart.scrollbarX = new am4core.Scrollbar();
    chart.cursor.xAxis = dateAxis;
    chart.exporting.menu = new am4core.ExportMenu();
    chart.exporting.filePrefix = "AdvancedAnalyticsUsersPerDVRTimeInterval";
    chart.exporting.menu.align = "left";
    chart.exporting.menu.verticalAlign = "top";
    let title = ' DVR - DVR time interval standard deviation over time - Hexaglobe';
    let options = chart.exporting.getFormatOptions("pdf");
    options.addURL = false;
    chart.exporting.adapter.add("pdfmakeDocument", function(pdf, target) {
      pdf.doc.content.unshift({
        text: title,
        margin: [0, 30],
        style: {
          fontSize: 15,
          bold: true,
        }
      });
      // Add logo
      pdf.doc.content.unshift({
        image: GlobalConstants.hexaglobeLogo,
        fit: [119, 54]
      });
      return pdf;
    });
    chart.exporting.menu.items = [{
      "label": "...",
      "menu": [
        { "type": "pdf", "label": "PDF", "options": { "quality": 1 } },
        { "type": "png", "label": "PNG", "options": { "quality": 1 } },
        { "type": "json", "label": "JSON", "options": { "indent": 2, "useTimestamps": true } },
        { "type": "csv", "label": "CSV", "options": { "indent": 2, "useTimestamps": true } },
        { "label": "Print", "type": "print" }
      ]
    }]; // Set the formats we want to allow the user to export in
    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    if (localStorage.getItem('precision') == 'WEEK' || localStorage.getItem('precision') == 'MONTH' || (localStorage.getItem('granularity') == 'DAY' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'DAY' || (localStorage.getItem('granularity') == 'HOUR' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "hour",
        "count": 1
      }
    } else if (localStorage.getItem('precision') == 'HOUR' || (localStorage.getItem('granularity') == 'MINUTE' && (localStorage.getItem('precision') == 'Personalized' || this.start != null))) {
      dateAxis.baseInterval = {
        "timeUnit": "minute",
        "count": 1
      }
    } else {
      dateAxis.baseInterval = {
        "timeUnit": "day",
        "count": 1
      }
    }
    var datas = [];
    //GET LES DIFFERENTES DONNÉES A GRAPHER
    for(let elem of res) {
      if(!datas.includes(elem[0])) {
        datas.push(elem[0])
      }
    }
    for (let i of datas) {
      createSeries(i, i);
    }
    function createSeries(s, name) {
      var series = chart.series.push(new am4charts.LineSeries());
      series.dataFields.valueY = "value" + s;
      series.dataFields.dateX = "date";
      series.name = name;
      series.tooltipText = s + " : {valueY}";
      // series.stacked =  true;
      // series.fillOpacity = 0.5;
      // series.tooltip.pointerOrientation = "vertical";
      series.tooltip.background.fillOpacity = 0.5;
      var segment = series.segments.template;
      segment.interactionsEnabled = true;
      var hoverState = segment.states.create("hover");
      hoverState.properties.strokeWidth = 3;
      var dimmed = segment.states.create("dimmed");
      dimmed.properties.stroke = am4core.color("#dadada");
      segment.events.on("over", function(event) {
        processOver(event.target.parent.parent.parent);
      });
      segment.events.on("out", function(event) {
        processOut(event.target.parent.parent.parent);
      });
      var data = [];
      let i = 0;
      for (let elem of res) {
        if (elem[0] == s) {
          let value = elem[1];
          elem[2] = elem[2].split('.')[0]
          if (elem[2].charAt(0) == '\'')
            elem[2] = elem[2].substring(1);
          let final = new Date(elem[2]);
          elem[2] = final.toISOString();
          var dataItem = { date: elem[2].substring(0, final) };
          dataItem["value" + s] = value;
          data.push(dataItem);
        }
      }
      series.data = data;
      return series;
    }
    chart.exporting.adapter.add("data", function(data, target) {
      var data1 = [];
      chart.series.each(function(series) {
        for(var i = 0; i < series.data.length; i++) {
          series.data[i].name = series.name;
          data1.push(series.data[i]);
        }
      });
      return { data: data1 };
    });
    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.scrollable = true;
    chart.legend.itemContainers.template.events.on("over", function(event) {
      processOver(event.target.dataItem.dataContext);
    });
    chart.legend.itemContainers.template.events.on("out", function(event) {
      processOut(event.target.dataItem.dataContext);
    });
    function processOver(hoveredSeries) {
      hoveredSeries.toFront();
      hoveredSeries.segments.each(function(segment) {
        segment.setState("hover");
      });
      chart.series.each(function(series) {
        if (series != hoveredSeries) {
          series.bulletsContainer.setState("dimmed");
        }
      });
    }
    function processOut(hoveredSeries) {
      chart.series.each(function(series) {
        series.bulletsContainer.setState("default");
      });
    }
    /* AXIS TOOLTIP FOR TOTAL VALUE */
    dateAxis.adapter.add("getTooltipText", (text) => {
      let totalValue = 0;
      chart.series.each(function(series) {
        if (series.tooltipDataItem.dataContext != undefined) {
          // @ts-ignore
          if (series.tooltipDataItem.valueY != undefined) {
            // @ts-ignore
            totalValue += series.tooltipDataItem.valueY;
          }
        }
      });
      return text + " - Total : " + chart.numberFormatter.format(totalValue);
    });
  }


  selectGranu(id) {

    for (let i = 0;i <= 4; i++)
    {
      let elem = document.getElementById("CheckGranu" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let s = new Date(localStorage.getItem('start_date'));
    let e = new Date(localStorage.getItem('end_date'));
    var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date

    if (this.granu[id] === 'HOUR' && (diff > 7*24)) {
      alert("You cannot choose this granularity");
      id = "CheckGranu2";
    }
    if (this.granu[id] === 'MINUTE' && (diff > 24)) {
      alert("You cannot choose this granularity, reduce hours interval");
      id = "CheckGranu2";
    }

    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    id = this.granu[id];
    localStorage.setItem('granularity', id);
    window.location.reload();
  }

}
