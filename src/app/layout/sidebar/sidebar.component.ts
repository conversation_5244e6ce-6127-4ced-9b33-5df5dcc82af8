import { Component, Input, OnInit } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { PersonalizedComponent } from '../../general/personalized/personalized.component';
import { GlobalConstants } from '../../common/global-constants';
import { ConsumptionComponent } from '../../general/consumption/consumption.component';
import { DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder } from '@angular/forms';

@Component({
    selector: 'app-sidebar',
    templateUrl: './sidebar.component.html',
    styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {

    isAdmin = 0;
    sidebarBookmarks = [];
    personalized;
    @Input() isExpanded: any;

    constructor(private http: HttpClient) {
    }

    ngOnInit() {
        this.isExpanded = true;
        this.loadBookmarks();
        this.getAdmin().then(result => {
            if (result != 401)
                this.isAdmin = 1;
        });
    }


    async getAdmin() {
        let result;
        try {
            result = await this.http.get(GlobalConstants.apiURL + 'user/',
                {
                    headers: new HttpHeaders()
                        .set('Authorization', localStorage.getItem('token'))
                },
            ).toPromise();
        } catch (e) {
            result = e.status;
        }
        return result;
    }

    loadBookmarks() {
        this.personalized = new PersonalizedComponent(this.http, new ActivatedRoute, null, new FormBuilder);
        this.personalized.getBookmarks().then(result => {
            this.sidebarBookmarks = result;
        })

    }

    //Function to delete a user bookmark
    async deleteBookmark(bookmark_id) {
        var request = await this.http.delete(GlobalConstants.apiURL + 'bookmark/' + bookmark_id + '/delete',
            {
                headers: new HttpHeaders()
                    .set('Authorization', localStorage.getItem('token'))
                    .set('accept', 'application/json')
                    .set('Content-Type', 'application/json')

            }
        ).toPromise();
        window.location.reload();
    }

    refresh() {
        localStorage.setItem("refresh", "true");
    }

}
