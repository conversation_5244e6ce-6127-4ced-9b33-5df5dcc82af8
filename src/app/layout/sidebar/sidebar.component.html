<mat-nav-list>
  <a mat-list-item matTooltip="Dashboard" [matTooltipPosition]="'right'"  (click)="refresh()" routerLink="/work" routerLinkActive="active">
    <mat-icon class="p-r15">equalizer</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature1">Dashboard</span>
  </a>
  <a mat-list-item matTooltip="Consumption" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/consumption" routerLinkActive="active">
    <mat-icon class="p-r15" >assessment</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">Consumption</span>
  </a>
  <a mat-list-item matTooltip="Session" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/session" routerLinkActive="active">
    <mat-icon class="p-r15">sort</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">Session</span>
  </a>
  <a mat-list-item matTooltip="Quality" [matTooltipPosition]="'right'"  routerLink="/quality" routerLinkActive="active" (click)="refresh()">
    <mat-icon class="p-r15">show_chart</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">Quality</span>
  </a>
  <a mat-list-item matTooltip="Personalized" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/personalized" routerLinkActive="active">
    <mat-icon class="p-r15">filter_vintage</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">Personalized</span>
  </a>
  <a mat-list-item matTooltip="Digital Video Recorder" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/dvr" routerLinkActive="active">
    <mat-icon class="p-r15">schedule</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">DVR Stats</span>
  </a>
  <a mat-list-item matTooltip="Ovp" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/ovp" routerLinkActive="active">
    <mat-icon class="p-r15">visibility</mat-icon>
    <span *ngIf="isExpanded" i18n="@@feature2">OVP Stats (beta)</span>
  </a>
<!--  <a mat-list-item matTooltip="Pivot" [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/pivot" routerLinkActive="active">-->
<!--    <mat-icon class="p-r15">visibility</mat-icon>-->
<!--    <span *ngIf="isExpanded" i18n="@@feature2">Pivot Stats (beta)</span>-->
<!--  </a>-->

  <hr>

  <div *ngIf="isAdmin === 1">
    <a mat-list-item matTooltip="Admin" [matTooltipPosition]="'right'" routerLink="/table" (click)="refresh()" routerLinkActive="active">
      <mat-icon class="p-r15">table_chart</mat-icon>
      <span *ngIf="isExpanded" i18n="@@admin">Admin</span>
    </a>
    <hr>
  </div>

  <!-- <a mat-list-item matTooltip="Help" [matTooltipPosition]="'right'" routerLink="/help" routerLinkActive="active">
    <mat-icon class="p-r15">help</mat-icon>
    <span *ngIf="isExpanded" i18n="@@help">Help</span>
  </a>
  <a mat-list-item matTooltip="About" [matTooltipPosition]="'right'" routerLink="/about" routerLinkActive="active">
    <mat-icon class="p-r15">info</mat-icon>
    <span *ngIf="isExpanded" i18n="@@about">About</span>
  </a>
  <hr> -->

  <a mat-list-item matTooltip="Bookmarks"  [matTooltipPosition]="'right'" (click)="refresh()" routerLink="/personalized" routerLinkActive="active">
    <mat-icon class="p-r15">star</mat-icon>
    <div *ngIf="isExpanded">Bookmarks</div>
  </a>

      <div *ngFor="let bookmark of sidebarBookmarks">
        <mat-card *ngIf="isExpanded" style="display: flex; font-size: 16px; margin: 4px; flex: 0.98; max-width: 150px; color: #737373 !important; cursor: pointer; background-color: #e1ecff; padding-bottom: 0px; padding-top: 0px" matTooltip="{{bookmark['name']}}" (click)="personalized.redirectToBookmark(bookmark['value'])" matTooltipClass="tooltipBookmark" id="bookmarkList">
          <span  mat-menu-item  style="width: 100%; font-size: 15px; color: #737373 !important; background-color: #e1ecff; display: block;">{{bookmark['name']}}</span>
          <div>
            <mat-icon style="display: inline-block; flex: 0.01; top: 50%; margin: 0; position: absolute"
                    (click)="deleteBookmark(bookmark['public_id'])" style="cursor: pointer; margin-top: 50%">
              highlight_off
            </mat-icon>
          </div>
        </mat-card>
      </div>

</mat-nav-list>
