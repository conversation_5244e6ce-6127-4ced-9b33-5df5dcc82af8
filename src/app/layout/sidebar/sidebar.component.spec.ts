import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SidebarComponent } from './sidebar.component';
import { MaterialModule } from '../../modules/shared/material.module';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('Components', () => {
  describe('Layouts', () => {
    describe('SidebarComponent', () => {
      let component: SidebarComponent;
      let fixture: ComponentFixture<SidebarComponent>;

      beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
          imports: [
            MaterialModule,
            NoopAnimationsModule
          ],
          declarations: [SidebarComponent]
        })
          .compileComponents();
      }));

      beforeEach(() => {
        fixture = TestBed.createComponent(SidebarComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });

      it('should create', () => {
        expect(component).toBeTruthy();
      });
    });
  });
});
