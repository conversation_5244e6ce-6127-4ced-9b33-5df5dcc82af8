import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FooterComponent } from './footer.component';
import { MaterialModule } from '../../modules/shared/material.module';

describe('Components', () => {
  describe('Layouts', () => {
    describe('FooterComponent', () => {
      let component: FooterComponent;
      let fixture: ComponentFixture<FooterComponent>;

      beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
          imports: [MaterialModule],
          declarations: [FooterComponent]
        })
          .compileComponents();
      }));

      beforeEach(() => {
        fixture = TestBed.createComponent(FooterComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });

      it('should create', () => {
        expect(component).toBeTruthy();
      });
    });
  });
});
