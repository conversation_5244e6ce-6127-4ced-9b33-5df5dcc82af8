<div class="app-container" [ngClass]="themeClass">
  <!-- Header Navigation -->
  <app-navbar (toggleNavigation)="isExpanded = !isExpanded"></app-navbar>

  <mat-sidenav-container class="app-sidenav-container sidenav-container" autosize>
    <mat-sidenav class="app-sidenav"
                 [opened]="true"
                 [mode]="'side'">
      <!-- Side Navigation -->
      <app-sidebar [isExpanded]="isExpanded"></app-sidebar>
    </mat-sidenav>
    <mat-sidenav-content>
      <!-- Main content -->
      <div class="app-container">
        <router-outlet></router-outlet>
      </div>
      <!-- Footer -->
      <app-footer [theme]="themeClass"></app-footer>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
