import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import { Subscription } from 'rxjs';
import { BreakpointObserver, Breakpoints, BreakpointState } from '@angular/cdk/layout';

import { ThemesService } from '../../services/themes.service';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit, OnDestroy {

  isExpanded = true;
  breakpointSubscription: Subscription;

  themeClass: string;

  constructor(private breakpointObserver: BreakpointObserver,
              private themesService: ThemesService) {
    this.breakpointSubscription = breakpointObserver.observe([
      Breakpoints.Large,
      Breakpoints.Medium
    ]).subscribe((result: BreakpointState) => {
      this.isExpanded = result.matches;
    });

  }

  ngOnInit() {
    this.themeClass = 'light-theme';
    this.themesService.getTheme().subscribe((data) => {
      data ? this.themeClass = 'dark-theme' : this.themeClass = 'light-theme';
    });
  }

  ngOnDestroy(): void {
    this.breakpointSubscription.unsubscribe();
  }
}
