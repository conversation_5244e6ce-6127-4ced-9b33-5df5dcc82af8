import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { MaterialModule } from '../../modules/shared/material.module';

import { MainComponent } from './main.component';

import { ThemesService } from '../../services/themes.service';

// import { getThemesServiceStub } from '../../testing/advr/themes.service.stub';

describe('Components', () => {
  describe('Layouts', () => {
    describe('MainComponent', () => {
      let component: MainComponent;
      let fixture: ComponentFixture<MainComponent>;

      beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
          imports: [MaterialModule, RouterTestingModule, BrowserAnimationsModule],
          declarations: [MainComponent],
          providers: [
         //   {provide: ThemesService, useValue: getThemesServiceStub()}
          ],
          schemas: [CUSTOM_ELEMENTS_SCHEMA],
        })
          .compileComponents();
      }));

      beforeEach(() => {
        fixture = TestBed.createComponent(MainComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });

      it('should create', () => {
        expect(component).toBeTruthy();
      });
    });
  });
});
