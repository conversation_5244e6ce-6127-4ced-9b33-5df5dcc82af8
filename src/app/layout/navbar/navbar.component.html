<mat-toolbar class="mat-elevation-z2">
  <button mat-icon-button (click)="openNavigation()">
    <mat-icon>menu</mat-icon>
  </button>
  <a mat-button class="app-logo" routerLink="/">
    <span fxLayout="row">
      <img class="logo" src="./assets/images/logo_in_square.svg">
      <h1 i18n="@@advanced_replay" class="mat-h1 m-l5">Advanced Analytics</h1>
    </span>
  </a>

  <div class="big">
    <div class="servicess">
      <nav><button class="service_btn" mat-stroked-button color="accent">Services
        <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
      </button>
        <div class="dropdown-content_services">
          <mat-form-field>
            <mat-label>Filter</mat-label>
            <input matInput (keyup)="applyFilter($event)" autocomplete="off" placeholder="Ex. Live" #input>
          </mat-form-field>
          <button class="refresh_btn" style="float: right; margin-top: 20px;" mat-flat-button color="validate" (click)="reload()">Validate</button>
          <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
            <!-- Checkbox Column -->
            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef>
                <input type="checkbox" id="select-all" (change)="$event ? masterToggle() : null"
                       [checked]="selection.hasValue() && isAllSelected()"
                       [indeterminate]="selection.hasValue() && !isAllSelected()"
                />
              </th>
              <td mat-cell *matCellDef="let row">
                <input type="checkbox" id="{{ 'serv' + row.position}}"
                       (click)="$event.stopPropagation()"
                       [checked]="selection.isSelected(row)"
                       (click)="set_service(row)"
                       (click)="checkboxLabel(row)"/>

              </td>
            </ng-container>

            <!-- Position Column -->
            <ng-container style="pointer-events: none;" matColumnDef="position">
              <th mat-header-cell *matHeaderCellDef> No. </th>
              <td mat-cell *matCellDef="let element"> {{element.position}} </td>
            </ng-container>

            <!-- Name Column -->
            <ng-container matColumnDef="client">
              <th mat-header-cell *matHeaderCellDef> Client </th>
              <td mat-cell *matCellDef="let element"> {{element.client}} </td>
            </ng-container>

            <!-- Weight Column -->
            <ng-container matColumnDef="service">
              <th mat-header-cell *matHeaderCellDef> Service </th>
              <td mat-cell *matCellDef="let element"> {{element.service}} </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="type">
              <th mat-header-cell *matHeaderCellDef> Type </th>
              <td mat-cell *matCellDef="let element"> {{element.type}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr  mat-row *matRowDef="let row; columns: displayedColumns;"
                 (click)="selection.toggle(row)"
                 (click)="set_service(row)">
            </tr>
          </table>
        </div>
      </nav>
    </div>


  <div class="precision">
    <div *ngIf="personalized === '0'; else elseblock1">
      <nav><button class="precision_btn" mat-stroked-button color="accent">Time Filter
        <mat-icon style="margin-left: 10px;">{{'arrow_drop_down'}}</mat-icon>
      </button>
        <div style="margin-left: 20px;" class="dropdown-content">
          <ul>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision0" value="Value1" (click)="selectTimeFilter('CheckPrecision0')"/>Last Hour
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision1" value="Value1" (click)="selectTimeFilter('CheckPrecision1')"/>Last 24H
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision2" value="Value1" (click)="selectTimeFilter('CheckPrecision2')"/>Last 7 Days
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision3" value="Value1" (click)="selectTimeFilter('CheckPrecision3')"/>Last 30 Days
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision4" value="Value1" (click)="selectTimeFilter('CheckPrecision4')"/>Last 12 Months
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision5" value="Value1" (click)="selectTimeFilter('CheckPrecision5')"/>Current Day
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision6" value="Value1" (click)="selectTimeFilter('CheckPrecision6')"/>Current Week
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision7" value="Value1" (click)="selectTimeFilter('CheckPrecision7')"/>Current Month
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision8" value="Value1" (click)="selectTimeFilter('CheckPrecision8')"/>Current Year
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision9" value="Value1" (click)="selectTimeFilter('CheckPrecision9')"/>Last x Days
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision10" value="Value1" (click)="selectTimeFilter('CheckPrecision10')"/>Last x Weeks
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision11" value="Value1" (click)="selectTimeFilter('CheckPrecision11')"/>Last x Months
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision12" value="Value1" (click)="selectTimeFilter('CheckPrecision12')"/>Last x Years
              <span class="checkmark"></span></label></li>
            <li><label class="container">
              <input type="checkbox" id="CheckPrecision13" value="Value1" (click)="selectTimeFilter('CheckPrecision13')"/>Personalized
              <span class="checkmark"></span></label></li>
          </ul>
        </div>
      </nav>
    </div>

    <ng-template #elseblock1>

      <button class="precision_btn" mat-flat-button color="accent" (click)="leavePersonalize()">Leave Personalized</button>
      <div class="dateRange">
        <mat-form-field class="formDate" appearance="fill">
          <mat-label>Enter a date range</mat-label>
          <mat-date-range-input class="dateRangeInput" [dateFilter]="myDateFilter" [formGroup]="range" [rangePicker]="picker">
            <input matStartDate formControlName="start" (click)="picker.open()" placeholder="Start date" >
            <input id="rangeEndDate" matEndDate formControlName="end" (click)="picker.open()" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matSuffix [for]="picker"disabled="false"></mat-datepicker-toggle>
          <mat-date-range-picker id="picker" #picker [opened]=displayCalendar  (closed)="getIsoDate(range.value)" disabled="false"></mat-date-range-picker>
          <mat-error *ngIf="range.controls.start.hasError('matStartDateInvalid')">Invalid start date</mat-error>
          <mat-error *ngIf="range.controls.end.hasError('matEndDateInvalid')">Invalid end date</mat-error>
        </mat-form-field>
      </div>

      <div class="startendHour">
        <!-- <label style="display: block;" for="timestart">Start :</label> -->
        <input id="timestart" class="time" aria-label="24hr format" [ngxTimepicker]="fullTime" [format]="24" [value]="'00:00'" >
        <ngx-material-timepicker #fullTime (closed)="toNext()"></ngx-material-timepicker>
      </div>-
      <!-- <span></span> -->
      <div class="startendHour">
        <!-- <label style="display: block;" for="timeend">End :</label> -->
        <input id="timeend" class="time" aria-label="24hr format" [ngxTimepicker]="fullTime1" [format]="24" [value]="'23:59'">
        <ngx-material-timepicker #fullTime1 (closed)="getIsoDate(range.value)"></ngx-material-timepicker>
      </div>
      <span></span>
    </ng-template>
  </div>

    <div *ngIf="display_factor" class="factor">
      <form name="myForm" ng-controller="ExampleController">
        <label>x :
          <input style="width: 30px" type="number" value="{{factor_value}}" name="input" ng-model="example.value"
                 min="1" max="99" (change)="changeFactor($event.target.value)" required>
        </label>
      </form>
    </div>

    <button *ngIf="factor_value > 0" class="refresh_btn" mat-flat-button color="validate" (click)="reload()">Validate</button>
  </div>

  <mat-icon class="update-icon" (click)="reload()" matTooltip="Refresh dashboard">update</mat-icon>

  <div *ngIf="personalized != 1" class="TimeFilterDisplay">
    <h2 style="color: gray;">Last {{getTimeFilterDisplay()}} data</h2>
  </div>

  <span class="fill-space"></span>

  <ng-moment-timezone-picker id="tzselector" class="timezone" (onselect)="selectTimeZone($event)" [customPlaceholderText]="time_zone_input">
  </ng-moment-timezone-picker>

  <mat-menu #profile="matMenu" yPosition="below" xPosition="after" class="advr-popup" [overlapTrigger]="false">
<!--    <button mat-menu-item routerLink="#/profile" routerLinkActive="active">-->
<!--      <mat-icon>account_circle</mat-icon>-->
<!--      <span i18n="@@my_profile"> My Profile </span>-->
<!--    </button>-->
    <button mat-menu-item (click)="logout()" routerLink="/login">
      <mat-icon>exit_to_app</mat-icon>
      <span i18n="@@logout">Logout</span>
    </button>
  </mat-menu>
  <button mat-button [matMenuTriggerFor]="profile" class="user-logo-btn advr-logo">
    <img class="user-logo" src="./assets/images/profile.jpg">
    <span fxHide.lt-sm class="user-name m-l5">{{username}}</span>
    <mat-icon>keyboard_arrow_down</mat-icon>
  </button>
</mat-toolbar>
