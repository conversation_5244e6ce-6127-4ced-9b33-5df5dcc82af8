import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { NavbarComponent } from './navbar.component';
import { MaterialModule } from '../../modules/shared/material.module';
import { RouterTestingModule } from '@angular/router/testing';
/*import { AuthenticationService } from '../../services/authentication/authentication.service';
import { getAuthenticationServiceStub } from '../../../../testing/advr/authentication.service.stub';*/

describe('Components', () => {
  describe('Layouts', () => {
    describe('NavbarComponent', () => {
      let component: NavbarComponent;
      let fixture: ComponentFixture<NavbarComponent>;

      beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
          imports: [MaterialModule, RouterTestingModule],
          declarations: [NavbarComponent],
          providers: [
        //    {provide: AuthenticationService, useValue: getAuthenticationServiceStub()}
          ]
        })
          .compileComponents();
      }));

      beforeEach(() => {
        fixture = TestBed.createComponent(NavbarComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });

      it('should create', () => {
        expect(component).toBeTruthy();
      });
    });
  });
});
