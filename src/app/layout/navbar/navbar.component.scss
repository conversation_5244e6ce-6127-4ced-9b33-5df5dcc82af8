@import "../../../assets/scss/variables/colors/colors";

mat-toolbar {

  button, mat-icon, span {
    color: $font-color;
  }

  box-shadow: 0 5px 5px #999;
  z-index: 2;
  position: relative;
}

/* Style for disabled cells */
::ng-deep .mat-calendar-body-disabled {
  pointer-events: none;
  opacity: 0.5;
  background-color: #f0f0f0;
  color: #999;
}

h1 {
  color: $advr-focus-element-color;
}

img {
  &.user-logo {
    margin-right: 10px;
  }
}

.update-icon {
  margin-left: 20px;
  cursor: pointer;
  color: black !important;
}

.logo {
  width: auto;
  height: 32px;
  margin-right: 26px;
}

.precision {
  //position: relative;
  display: inline-block;
}

.precision:hover .dropdown-content {
  display: block;
}

.factor {
  display: inline-block;
  padding-left: 5px;
  font-size: 15px;
}

.TimeFilterDisplay {
  display: inline-block;
  padding-left: 20px;
  font-size: 15px;
}

.servicess:hover .dropdown-content_services {
  display: block;
}

.dropdown-content_services {
  width: 700px;
  height: 700px;
}


table {
  width: 100%;
}


.big {
  display: inline-block;
}

.servicess {
  position: relative;
  display: inline-block;
  padding-left: 20px;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
  overflow-y: scroll;
  scrollbar-color: white;
  scrollbar-width: grey;
  max-height: 500px;
}

.dropdown-content_services {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 360px;
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
  padding: 12px 16px;
  z-index: 1;
  overflow-y: scroll;
  scrollbar-color: white;
  scrollbar-width: grey;
  max-height: 500px;
}


.container {
  display: inline-block;
  position: relative;
  padding-left: 35px;
  padding-right: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #ccc;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #2196F3;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: #2196F3;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.precision_btn {
  display: inline-block;
  margin-left: 20px;
}

.refresh_btn {
  right: 0;
  margin-left: 20px;
}

.dateRange {
  display: inline-block;
  margin-left: 20px;
}

.formDate {
  //display: inline-block;
  //margin-top: 5px;
  width: 320px;
  margin-right: 20px;
}

.validate_btn {
  display: inline-block;
  margin-left: 20px;
  margin-right: 20px;
}

.dateDisplay {
  display: inline-block;
  margin-left: 20px;
}

ng-moment-timezone-picker.timezone {
  margin-top: auto;
  margin-left: 20px;
  margin-right: 60px;
  width: 150px;
}

ng-moment-timezone-picker.timezone ::ng-deep .ng-select-container {
  width: 150px;
  height: auto !important;
 }

ng-moment-timezone-picker.timezone ::ng-deep .wrapper {
  width: 320px;
  height: auto !important;
}

::ng-deep .mat-calendar#mat-datepicker-0 {
  background-color: whitesmoke;
}


::ng-deep .mat-calendar-body-selected {
  background-color: #3f51b5;
  color : #fff
}

::ng-deep .mat-calendar-body-in-range {
  background: rgba(63,81,181,.2);
  border-top-left-radius: 999px;
  border-top-right-radius: 999px;
  border-bottom-left-radius: 999px;
  border-bottom-right-radius: 999px;
}

mat-form-field.formDate ::ng-deep .mat-form-field-flex {
  background-color: whitesmoke;
}

::ng-deep .light-theme .mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex {
    background-color: rgba(0, 0, 0, 0);
}

::ng-deep .mat-date-range-input-start-wrapper {
  max-width: 110px !important;
  position: relative !important;
}

::ng-deep .mat-date-range-input-inner {
  position: unset !important;
}


.dateRangeInput {
  height: 5px;
  width: 350px;
}

.time {
  width: 45px;
  text-align: center;
  margin-right: 10px;
  background-color: rgba(255,255,255,0.1);
  border-top-color: initial;
  border-top-style: initial;
  border-top-width: 2px;
  border-right-color: initial;
  border-right-style: initial;
  border-right-width: 2px;
  //border-bottom-color: initial;
  //border-bottom-style: initial;
  padding-bottom: 5px;
  border-bottom-width: 2px;
  border-left-color: initial;
  border-left-style: initial;
  border-left-width: 2px;
  //font-size: 13px;
}

.time2 {
  width: 45px;
  text-align: center;
  margin-right: 10px;
}

.textHour {
  display: inline-block;
  width: 45px;
  text-align: center;
  margin-right: 10px;
  height: 10px;
}

//.timepicker {
//  width: 200px;
//  height: 105px;
//}
//
//.mat-elevation-z2 {
//  height: 100px;
//}

::ng-deep .timepicker__header {
  background-color: #2196F3 !important;
  //border-radius: 5px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

::ng-deep .timepicker__actions {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

::ng-deep .timepicker-button {
  color: #2196F3 !important;
}

::ng-deep .clock-face__number > span.active {
  background-color: #2196F3 !important;
}

::ng-deep .clock-face__clock-hand {
  background-color: #2196F3 !important;
}

.startendHour {
  display: inline-block;
  font-size: 14px;
  text-decoration: underline;
  color: grey;
}

.mat-validate {
  background-color: #09802d;
  color: white !important;
}
