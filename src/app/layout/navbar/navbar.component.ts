import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { DOCUMENT} from '@angular/common';
import { FormControl, FormGroup } from '@angular/forms';
import { decoratorArgument } from 'codelyzer/util/astQuery';
import{ GlobalConstants } from '../../common/global-constants';
import {ElementRef,Renderer2} from '@angular/core';
import {SelectionModel} from '@angular/cdk/collections';
import {MatTableDataSource} from '@angular/material/table';
import { element } from 'protractor';

export interface ServicesElement {
  position: number;
  client: string;
  service: string;
  type: string;
}

const ELEMENT_DATA: ServicesElement[] = [];

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent implements OnInit {

  range = new FormGroup({
    start: new FormControl(),
    end: new FormControl()
  });
  displayCalendar = true;
  services = [];
  servicesSelected = [];
  service = '';

  display_factor = false;
  new_selected_services = '';
  new_selected_services_arr = [];
  new_selected_services_checkboxes = [];
  factor_value = 1;
  check = [];
  end_date;
  start_date;
  checkbox = [];
  personalized;
  tmp = '';
  var_to_return_tmp = '';
  precision = {'CheckPrecision0': 'HOUR', 'CheckPrecision1': 'DAY', 'CheckPrecision2': 'WEEK', 'CheckPrecision3': 'MONTH', 'CheckPrecision4': 'YEAR', 'CheckPrecision5': 'currentDay', 'CheckPrecision6': 'currentWeek','CheckPrecision7': 'currentMonth', 'CheckPrecision8': 'currentYear', 'CheckPrecision9': 'completeDay', 'CheckPrecision10': 'completeWeek', 'CheckPrecision11': 'completeMonth', 'CheckPrecision12': 'completeYear', 'CheckPrecision13': 'Personalized'};
  precision_display = {'HOUR': 'Hour', 'DAY': '24h', 'WEEK': '7 days', 'MONTH': '30 days', 'YEAR': '12 months', 'currentDay': 'current day', 'currentWeek': 'current week','currentMonth': 'current month', 'currentYear': 'current year', 'completeDay': 'complete x day(s)', 'completeWeek': 'complete x week(s)', 'completeMonth': 'complete x month(s)', 'completeYear': 'complete x year(s)', 'Personalized': 'Personalized'};
  username: string;
  time_zone_input="Time Zone";
  tz = '';

  myDateFilter = (d: Date | null): boolean => {
    const currentDate = new Date();
    const inputDate = d || new Date();
    // If the input date is in the past or today, allow selection
    return inputDate <= currentDate;
  };

  @Output() readonly toggleNavigation: EventEmitter<boolean>;

  @Output() readonly toggleThemeEvent: EventEmitter<string>;

  constructor(private route: Router, private http: HttpClient, private renderer: Renderer2) {
    this.toggleNavigation = new EventEmitter<boolean>();
    this.toggleThemeEvent = new EventEmitter<string>();
  }

  async ngOnInit() {

    if (localStorage.getItem('displayCalendar'))
      this.displayCalendar = (localStorage.getItem('displayCalendar') == "true");
    else
      localStorage.setItem('displayCalendar', 'true');

    this.tz = localStorage.getItem("TimeZone");
    this.time_zone_input = this.tz;
    if (localStorage.getItem('start_date') !== null && localStorage.getItem('end_date') !== null) {
      this.start_date = localStorage.getItem('start_date').substring(0, 10).toString();
      this.end_date = localStorage.getItem('end_date').substring(0, 10).toString();
    }

    if (localStorage.getItem('start_date') && !localStorage.getItem('precision').includes('complete')) {
      localStorage.setItem('personalized', '1');
      localStorage.setItem('precision', 'Personalized');
    }

    this.username = localStorage.getItem('name');
    if(localStorage.getItem('precision') !== 'Personalized') {
      localStorage.setItem('personalized', '0');
    }
    this.personalized = localStorage.getItem('personalized');

    this.getServices().then(r => {
      this.service = r;
      if (!localStorage.getItem('new_selected_services')) {
        this.dataSource.filteredData.forEach(row => this.selection.select(row));
      } else {
        let init = localStorage.getItem('new_selected_services').split(',');

        for (let elem of this.dataSource.filteredData) {
          if (init.indexOf('\'' + elem.client + '-' + elem.service + '-' + elem.type + '\'') != -1 || init.indexOf('\'' + elem.client + '-' + elem.service + '\'') != -1) {
            this.selection.select(elem)
          }
        }
      }

      if (localStorage.getItem('services') === null || localStorage.getItem('services') === 'initializations') {
        for (let item of this.services) {
          this.tmp = this.tmp + ',\'' + item + '\'';
        }
        if (this.tmp.charAt(0) === ',') {
          this.tmp = this.tmp.substr(1);
        }
        localStorage.setItem('services', this.tmp);

        let i = 0;
        while(i < this.services.length) {
          this.check.push(i++);
        }
        localStorage.setItem('checkboxes', this.check.toString());
        window.location.reload();
      }
    });

    if (localStorage.getItem('precision') === null) {
      localStorage.setItem('precision', 'DAY')
    }
    this.factor_value = Number(localStorage.getItem('complete_date_factor'))
    this.display_factor = this.factor_value != 0;

  }

  selectTimeZone($event) {
    localStorage.setItem('loadUrlInterval', 'false');
    let decalage = $event["timeValue"];
    let sign = decalage.substring(0, 1);
    decalage = decalage.substring(1, 3);
    localStorage.setItem('timeZoneDecalage', decalage);
    localStorage.setItem('signDecalage', sign);
    localStorage.setItem('TimeZone', $event["name"].split(' ')[0]);
    localStorage.setItem('doNotLoadFactor', 'true');
    window.location.reload();
  }

  openNavigation(): void {
    this.toggleNavigation.emit(true);
  }

  // setLightTheme(): void {
  //   this.toggleThemeEvent.emit('light-theme');
  // }
  //
  // setDarkTheme(): void {
  //   this.toggleThemeEvent.emit('dark-theme');
  // }

  selectTimeFilter(id) {

    localStorage.setItem('loadUrlInterval', 'false');
    localStorage.removeItem('start_date');
    localStorage.removeItem('end_date');
    localStorage.removeItem('comparison_date');
    localStorage.removeItem('complete_date_factor');
    localStorage.setItem('doNotLoadFactor', 'true');
    this.display_factor = false;

    for (let i = 0;i <= 13; i++)
    {
      let elem = document.getElementById("CheckPrecision" + i) as HTMLInputElement;
      elem.checked = false;
    }
    let elem2 = document.getElementById(id) as HTMLInputElement;
    elem2.checked = true;
    id = this.precision[id];
    localStorage.setItem('precision', id);
    if (id === 'Personalized') {
      localStorage.setItem('personalized', '1');
      this.personalized = 1;
      return;

    } else if (id.includes('current')) {
      localStorage.setItem('personalized', '1');
      this.personalized = 1;
      this.displayCalendar = false;
      var curr = new Date;
      if (id == 'currentYear') {
        var firstday = new Date(curr.getFullYear(), 0, 1);
        var lastday = new Date(curr.getFullYear() + 1, 0, 0);
      } else if (id == 'currentMonth') {
        var firstday = new Date(curr.getFullYear(), curr.getMonth(), 1);
        var lastday = new Date(curr.getFullYear(), curr.getMonth() + 1, 0);
      } else if (id == 'currentWeek') {
        var first = curr.getDate() - curr.getDay() + 1;
        var firstday = new Date(curr.setDate(first));
        var lastday = new Date(curr.setDate(curr.getDate() + 6));
      } else if (id == 'currentDay') {
        var firstday = curr;
        var lastday = curr;
      }

      var tzStart = firstday.getTimezoneOffset() / 60;
      lastday = new Date(lastday.setHours(23, 59, 0, 0));
      var tzLast = lastday.getTimezoneOffset() / 60;
      var finalFirstday = firstday.setHours(-tzStart, 0, 0, 0);
      var finalLastday = lastday.setHours(23 - tzLast, 59, 0, 0);
      localStorage.setItem('start_date', new Date(finalFirstday).toISOString().replace('Z', ''));
      localStorage.setItem('end_date', new Date(finalLastday).toISOString().replace('Z', ''));
      let s = new Date(localStorage.getItem('start_date'));
      let e = new Date(localStorage.getItem('end_date'));
      localStorage.setItem('comparison_date', this.getDateComparison(s, e).replace('Z', ''))
      localStorage.setItem('displayCalendar', "false");
      window.location.reload();


    } else if (id.includes('complete')) {
      localStorage.setItem('doNotLoadFactor', 'false');
      this.display_factor = true;
      this.factor_value = 1;
      var curr = new Date;
      if (id == 'completeYear') {
        curr.setFullYear(curr.getFullYear() - 1)
        var firstday = new Date(curr.getFullYear(), 0, 1);
        var lastday = new Date(curr.getFullYear() + 1, 0, 0);
      } else if (id == 'completeMonth') {
        curr.setMonth(curr.getMonth() - 1)
        var firstday = new Date(curr.getFullYear(), curr.getMonth(), 1);
        var lastday = new Date(curr.getFullYear(), curr.getMonth() + 1, 0);
      } else if (id == 'completeWeek') {
        curr.setDate(curr.getDate() - 7)
        var first = curr.getDate() - curr.getDay() + 1;
        var firstday = new Date(curr.setDate(first));
        var lastday = new Date(curr.setDate(curr.getDate() + 6));
      } else if (id == 'completeDay') {
        curr.setDate(curr.getDate() - 1)
        var firstday = curr;
        var lastday = curr;
      }

      var tzStart = firstday.getTimezoneOffset() / 60;
      lastday = new Date(lastday.setHours(23, 59, 0, 0));
      var tzLast = lastday.getTimezoneOffset() / 60;
      var finalFirstday = firstday.setHours(-tzStart, 0, 0, 0);
      var finalLastday = lastday.setHours(23 - tzLast, 59, 0, 0);
      localStorage.setItem('start_date', new Date(finalFirstday).toISOString().replace('Z', ''));
      localStorage.setItem('end_date', new Date(finalLastday).toISOString().replace('Z', ''));
      localStorage.setItem('complete_date_factor', '1');
      let s = new Date(localStorage.getItem('start_date'));
      let e = new Date(localStorage.getItem('end_date'));
      localStorage.setItem('comparison_date', this.getDateComparison(s, e).replace('Z', ''))
    } else {
      window.location.reload();
    }
  }


  changeFactor(value) {
    localStorage.setItem('complete_date_factor', value)
    localStorage.setItem('notUpdateDisplay', "true");
    localStorage.setItem('doNotLoadFactor', 'true')
  }

  reload() {
    window.location.reload();
  }

  async getServices() {

    if (localStorage.getItem('services') !== null && localStorage.getItem('checkboxes') !== null) {
      this.service = localStorage.getItem('services');
      let checkValues = localStorage.getItem('checkboxes').split(',');
      for (let value of checkValues) {
        if (value !== undefined && value !== 'undefined' && checkValues.indexOf(value) !== -1 && this.checkbox.indexOf(Number(value)) === -1 && !isNaN(Number(value))) {
          this.checkbox.push(Number(value));
        }
      }
    }
    this.services = [];
    const ret = await this.http.get<any>(GlobalConstants.apiURL + 'service/',
        {
          headers: new HttpHeaders()
              .set('accept', 'application/json')
              .set('Content-Type', 'application/x-www-form-urlencoded')
              .set('Authorization', localStorage.getItem('token'))
        }
    ).toPromise();

    let pos = 1;
    for (let item of ret['data']) {
      this.var_to_return_tmp = this.var_to_return_tmp + ',\'' + item['name'] + '\'';
      this.services.push(item['name']);
      //SET THE SERVICES TO THE NEW SERVICES SELECTOR
      let servInfos = item['name'].split("-", 4);

      if (servInfos[3]) {
        servInfos[2] = servInfos[2] + "-" + servInfos[3];
      }

      let serv = {position: pos, client: servInfos[0], service: servInfos[1], type: servInfos[2]};
      ELEMENT_DATA.push(serv);
      pos++;
    }
    this.dataSource.data = ELEMENT_DATA;
    //END OF THE NEW SERVICES SELECTOR

    if (this.var_to_return_tmp.charAt(0) === ',') {
      this.var_to_return_tmp = this.var_to_return_tmp.substr(1);
    }
    if (!localStorage.getItem('services') || localStorage.getItem('services') === 'initialization') {
      localStorage.setItem('initialized', '1');
      localStorage.setItem('services', 'initializations');
      this.service = this.var_to_return_tmp;
      this.var_to_return_tmp = '';
      // this.validate();
      return this.service;
    }

    // FILL THE DATE RANGE INPUT
    let start = localStorage.getItem("start_date");

    if (start != null) {
      let new_start = start.split('T')[0].split('-');
      start = new_start[2] + "/" + new_start[1] + "/" + new_start[0];
      let end = localStorage.getItem("end_date")
      let new_end = end.split('T')[0].split('-');
      end = new_end[2] + "/" + new_end[1] + "/" + new_end[0];
      let elemStartDate = document.getElementById("mat-date-range-input-0") as HTMLInputElement;
      elemStartDate.value = start;
      let elemEndDate = document.getElementById("rangeEndDate") as HTMLInputElement;
      elemEndDate.value = end;
      //FILL THE HOUR RANGE INPUTS
      let hour = localStorage.getItem("start_date").split('T')[1].split(':')[0]
      let minute = localStorage.getItem("start_date").split('T')[1].split(':')[1]
      let elemStartHour = document.getElementById("timestart") as HTMLInputElement;
      elemStartHour.value = hour + ':' + minute;

      let hourend = localStorage.getItem("end_date").split('T')[1].split(':')[0]
      let minuteend = localStorage.getItem("end_date").split('T')[1].split(':')[1]
      let elemEndHour = document.getElementById("timeend") as HTMLInputElement;
      elemEndHour.value = hourend + ':' + minuteend;
    }
  }

  validate() {
    this.ngOnInit();
  }

  leavePersonalize() {
    // this.displayCalendar = true;
    localStorage.setItem('loadUrlInterval', 'false');
    localStorage.setItem('displayCalendar', 'true');
    localStorage.setItem('precision', 'DAY');
    localStorage.setItem('personalized', '0');
    this.personalized = 0;
    localStorage.removeItem('precision');
    localStorage.removeItem('start_date');
    localStorage.removeItem('end_date');
    localStorage.removeItem('comparison_date');
    localStorage.removeItem('granularity');
    window.location.reload();
  }

  getIsoDate(res) {

    localStorage.setItem('loadUrlInterval', 'false');
    if (res['start'] == null) {
      let start = new Date(localStorage.getItem("start_date"));
      let end = new Date(localStorage.getItem("end_date"));
      let tz = new Date();
      let userTimezoneOffset = tz.getTimezoneOffset() / 60;
      start.setHours(0);
      start.setMinutes(0);
      end.setHours(end.getHours()  + userTimezoneOffset);
      end.setHours(0);
      end.setMinutes(0);
      let replace = {"start": start, "end": end};
      res = replace;
    }

    this.displayCalendar = false;
    localStorage.setItem('displayCalendar', "false");
    let timestart = document.getElementById('timestart') as HTMLInputElement;
    let timeend = document.getElementById('timeend') as HTMLInputElement;
    let hourfirst  = timestart.value.split(':');
    let hourlast  = timeend.value.split(':');
    let firstToAdd = ((Number(hourfirst[0]) * 60) + Number(hourfirst[1])) * 60 * 1000;
    let secondToAdd = ((Number(hourlast[0]) * 60) + Number(hourlast[1])) * 60 * 1000;
    let start = res['start'].toISOString();
    let date = new Date(start);

    date.setHours(0);
    date.setTime(date.getTime() + firstToAdd);
    date.setHours(date.getHours() - (date.getTimezoneOffset() / 60));
    start = date.toISOString();
    let end = res['end'].toISOString();
    let date1 = new Date(end);
    let tztmp1 =  date1.getTimezoneOffset();
    date1.setTime(date1.getTime() + secondToAdd);
    let tztmp2 = date1.getTimezoneOffset();
    date1.setHours(date1.getHours() - (date1.getTimezoneOffset() / 60) - ((tztmp1 - tztmp2) / 60));
    end = date1.toISOString();

    let s = new Date(start.replace('Z', ''));
    let e = new Date(end.replace('Z', ''));

    var diff = Math.abs(e.getTime() - s.getTime()) / 3600000; // hours difference between start and end date
    let newOne = this.getDateComparison(s, e);

    if (localStorage.getItem('granularity') === 'HOUR' && (diff > 7*24)) {
        alert("You cannot choose this granularity, reduce time interval to 7 days or less.");
        return;
    }
    if (localStorage.getItem('granularity') === 'MINUTE' && (diff > 24)) {
        alert("You cannot choose this granularity, reduce hours interval to 24 hours or less.");
        return;
    }

    localStorage.setItem('start_date', start.replace('Z', ''));
    localStorage.setItem('end_date', end.replace('Z', ''));
    localStorage.setItem('comparison_date', newOne.replace('Z', ''));

    this.end_date = localStorage.getItem('end_date').substring(0, 10).toString();
    this.start_date = localStorage.getItem('start_date').substring(0, 10).toString();
    window.location.reload();
  }


  getDateComparison(s, e) {

    let new_s = new Date(s);
    let new_e = new Date(e);
    let n = new Date(s);

    new_e.setHours(new_e.getHours());
    new_s.setHours(new_s.getHours());
    let diff = Math.floor((Date.UTC(new_e.getFullYear(), new_e.getMonth(), new_e.getDate()) - Date.UTC(new_s.getFullYear(), new_s.getMonth(), new_s.getDate()) ) /(1000 * 60 * 60 * 24));

    if (diff < 1) {
      diff = new_e.getHours() - s.getHours();
      n.setDate(new_s.getDate());
      n.setHours(new_s.getHours() - diff)
    } else {
      n.setDate(new_s.getDate() - diff);
      let diff2 = new_e.getHours() - new_s.getHours();
      n.setHours(new_s.getHours() - diff2)
    }

    let diffMinutes = new_e.getMinutes() - new_s.getMinutes();
    n.setHours(n.getHours() - (n.getTimezoneOffset() / 60));
    n.setMinutes(new_s.getMinutes() - diffMinutes);
    n.setSeconds(s.getSeconds());
    n.setMilliseconds(new_s.getMilliseconds());
    return n.toISOString();
  }




  logout(): void {
  }

  //NOUVEAU SERVICE SELECTOR

  displayedColumns: string[] = ['select', 'position', 'client', 'service', 'type'];
  dataSource = new MatTableDataSource<ServicesElement>(ELEMENT_DATA);
  selection = new SelectionModel<ServicesElement>(true, []);

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    let checker = (arr, target) => target.every(v => arr.includes(v));
    const numRows = this.dataSource.filteredData.length;
    return numSelected === numRows || checker(this.selection.selected, this.dataSource.filteredData);
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected() ?
        this.toutVider() :
        this.toutSelect();
  }


  toutVider() {

    this.dataSource.filteredData.forEach(row => this.selection.deselect(row));
    this.new_selected_services_checkboxes = [];
    localStorage.setItem('new_selected_services_checkboxes', '');
    localStorage.setItem('new_selected_services', '');
    this.new_selected_services = '';
    for(let i = 0; i < this.selection.selected.length; i++) {
      this.new_selected_services_checkboxes.push(this.selection.selected[i]['position']);
      this.new_selected_services = this.new_selected_services +  '\'' + this.services[this.selection.selected[i]['position'] - 1] + '\',';
    }
    localStorage.setItem('new_selected_services_checkboxes', this.new_selected_services_checkboxes.toString());
    localStorage.removeItem('new_selected_services');
    localStorage.setItem('new_selected_services', this.new_selected_services.slice(0, -1));
  }

  toutSelect() {

    this.dataSource.filteredData.forEach(row => this.selection.deselect(row));
    this.dataSource.filteredData.forEach(row => this.selection.select(row));
    this.new_selected_services_checkboxes = [];
    localStorage.setItem('new_selected_services_checkboxes', '');
    localStorage.setItem('new_selected_services', '');
    this.new_selected_services = '';
    for(let i = 0; i < this.selection.selected.length; i++) {
      this.new_selected_services_checkboxes.push(this.selection.selected[i]['position']);
      this.new_selected_services = this.new_selected_services +  '\'' + this.services[this.selection.selected[i]['position'] - 1] + '\',';
    }
    localStorage.setItem('new_selected_services_checkboxes', this.new_selected_services_checkboxes.toString());
    localStorage.removeItem('new_selected_services');
    localStorage.setItem('new_selected_services', this.new_selected_services.slice(0, -1));
  }


  /** The label for the checkbox on the passed row */
  checkboxLabel(row?: ServicesElement): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.position + 1}`;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }


  set_service(row?: ServicesElement) {
    let service = '';
    if (row['type'] != undefined)
      service = row['client'] + '-' + row['service'] + '-' + row['type'];
    else if (row['service'] != undefined)
      service = row['client'] + '-' + row['service'];
    else
      service = row['client'];

    let pos = row['position'];

    if (localStorage.getItem('new_selected_services_checkboxes')) {
      this.new_selected_services_checkboxes = localStorage.getItem('new_selected_services_checkboxes').split(',').map(Number);
    } else
      this.new_selected_services_checkboxes = [];
    if(this.new_selected_services_checkboxes.includes(pos)) {
      let indexCheck = this.new_selected_services_checkboxes.indexOf(pos);
      this.new_selected_services_checkboxes.splice(indexCheck, 1);
    } else
      this.new_selected_services_checkboxes.push(pos);

    localStorage.setItem('new_selected_services_checkboxes', this.new_selected_services_checkboxes.toString());


    if (localStorage.getItem('new_selected_services')) {
      this.new_selected_services_arr = localStorage.getItem('new_selected_services').split(',')
    }

    if(this.new_selected_services_arr.includes('\''+service+'\'')) {
      let indexCheck = this.new_selected_services_arr.indexOf('\''+service+'\'');
      this.new_selected_services_arr.splice(indexCheck, 1);
    } else
      this.new_selected_services_arr.push('\''+service+'\'');


    this.new_selected_services = '';
    for (let elem of this.new_selected_services_arr) {
      if (elem != '' && elem != "")
        this.new_selected_services = this.new_selected_services +  elem + ',';
    }
    localStorage.removeItem('new_selected_services');
    localStorage.setItem('new_selected_services', this.new_selected_services.slice(0, -1));

  }

  toNext() {
    let selector = document.getElementById('timeend') as HTMLInputElement;
    selector.click();
  }


  setLastIntervalDate(id) {
    localStorage.removeItem('start_date');
    localStorage.removeItem('end_date');
    localStorage.removeItem('comparison_date');
    let factor = localStorage.getItem('complete_date_factor');

    var curr = new Date;
    if (id == 'completeYear') {
      curr.setFullYear(curr.getFullYear() - Number(factor));
      var firstday = new Date(curr.getFullYear(), 0, 1);
      var lastday = new Date(curr.getFullYear() + Number(factor), 0, 0);
    } else if (id == 'completeMonth') {
      curr.setMonth(curr.getMonth() - Number(factor));
      var firstday = new Date(curr.getFullYear(), curr.getMonth(), 1);
      var lastday = new Date(curr.getFullYear(), curr.getMonth() + Number(factor), 0);
    } else if (id == 'completeWeek') {
      curr.setDate(curr.getDate() - 7 * Number(factor));
      var first = curr.getDate() - curr.getDay() + 1;
      var firstday = new Date(curr.setDate(first));
      var lastday = new Date(curr.setDate(curr.getDate() + (6 * Number(factor)) + (Number(factor) - 1)));
    } else if (id == 'completeDay') {
      curr.setDate(curr.getDate() - Number(factor));
      var firstday = curr;
      // var lastday = curr;
      var lastday = new Date(curr.getFullYear(), curr.getMonth(), curr.getDate() + (Number(factor) - 1));
    }

    var tzStart = firstday.getTimezoneOffset() / 60;
    lastday = new Date(lastday.setHours(23, 59, 0, 0));
    var tzLast = lastday.getTimezoneOffset() / 60;
    var finalFirstday = firstday.setHours(-tzStart, 0, 0, 0);
    var finalLastday = lastday.setHours(23 - tzLast, 59, 0, 0);
    localStorage.setItem('start_date', new Date(finalFirstday).toISOString().replace('Z', ''));
    localStorage.setItem('end_date', new Date(finalLastday).toISOString().replace('Z', ''));
    let s = new Date(localStorage.getItem('start_date'));
    let e = new Date(localStorage.getItem('end_date'));
    localStorage.setItem('comparison_date', this.getDateComparison(s, e).replace('Z', ''))
  }

  getTimeFilterDisplay() {
    return this.precision_display[localStorage.getItem('precision')];
  }

}
