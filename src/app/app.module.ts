import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { MaterialModule } from './modules/shared/material.module';
import { LayoutsModule } from './modules/layouts/layouts.module';
import { ReactiveFormsModule } from '@angular/forms';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AboutModule } from './modules/general/about.module';
import { HelpModule } from './modules/general/help.module';
import { DisabledFeatureModule } from './modules/general/disabled-feature.module';
import { DashboardModule } from './modules/general/dashboard.module';
import { ProfileModule } from './modules/profile/profile.module';
import { TableModule } from './modules/general/table.module';
import { ButtonsModule } from './modules/general/buttons.module';
import { ConsumptionModule} from './modules/general/consumption.module';
import { LoginModule } from './modules/general/login.module';
import { FormsModule } from '@angular/forms';
import { SessionModule } from './modules/general/session.module';
import { QualityModule } from './modules/general/quality.module';
import { AuthService } from './services/auth/auth.service';
import { AuthGuardService } from './services/auth/auth-guard.service';
import { RoleGuardService } from './services/role/role-guard.service';
import { JwtHelperService } from '@auth0/angular-jwt';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule} from '@angular/material/core';
import { PersonalizedComponent } from './general/personalized/personalized.component';
import {NgxMaterialTimepickerModule} from 'ngx-material-timepicker';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import {MatTableExporterModule } from 'mat-table-exporter';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ClickOutsideModule } from 'ng-click-outside';
import { OvpComponent } from './general/ovp/ovp.component';
import { DvrStatsComponent } from './general/dvr-stats/dvr-stats.component';
import { OvpStatsComponent } from './general/ovp-stats/ovp-stats.component';
import { PivotIframe1Component } from './general/pivot-iframe1/pivot-iframe1.component';



@NgModule({
  declarations: [
    AppComponent,
    PersonalizedComponent,
    OvpComponent,
    DvrStatsComponent,
    OvpStatsComponent,
    PivotIframe1Component,
  ],
    imports: [
        BrowserModule,
        AppRoutingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        FormsModule,
        SessionModule,
        HttpClientModule,
        MaterialModule,
        QualityModule,
        LayoutsModule,
        ConsumptionModule,
        AboutModule,
        DisabledFeatureModule,
        DashboardModule,
        HelpModule,
        ProfileModule,
        TableModule,
        ButtonsModule,
        MatDatepickerModule,
        MatNativeDateModule,
        LoginModule,
        NgxMaterialTimepickerModule,
        NgbModule,
        MatTableModule,
        MatSortModule,
        MatTableExporterModule,
        DragDropModule,
        ClickOutsideModule
    ],
  providers: [AuthService, AuthGuardService, RoleGuardService, JwtHelperService],
  exports: [
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
