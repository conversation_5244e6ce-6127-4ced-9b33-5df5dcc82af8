<mat-card>
    <mat-tab-group [color]="'accent'">
        <mat-tab li18n-label="@@informations" label="Informations">
            <div class="form-container">
                <section>
                    <h4 i18n="@@customizetheme">Customize theme</h4>
                    <div class="toggle">
                        <label for="change-theme" i18n="@@light">Light</label>
                        <mat-slide-toggle
                                id="change-theme"
                                (change)="onChange($event)"
                                [checked]="checked"
                                i18n="@@dark">
                            Dark
                        </mat-slide-toggle>
                    </div>
                    <h4 i18n="@@username">Username</h4>
                    <mat-form-field>
                        <mat-label i18n="@@username">Username</mat-label>
                        <input matInput>
                    </mat-form-field>
                    <h4>Email address</h4>
                    <mat-form-field>
                        <mat-label i18n="@@email">Email</mat-label>
                        <input matInput>
                    </mat-form-field>
                    <h4>Timezone</h4>
                    <mat-form-field>
                        <mat-label i18n="@@timezone">Timezone</mat-label>
                        <input matInput>
                    </mat-form-field>
                </section>
                <button mat-flat-button color="accent" class="submit" i18n="@@submit">Submit</button>
            </div>
        </mat-tab>
        <mat-tab label="Channels">
            <div class="form-container">
                <section>
                    <mat-form-field>
                        <mat-label i18n="@@channels">Channels</mat-label>
                        <input matInput i18n-placeholder="@@channels">
                    </mat-form-field>
                </section>
                <button mat-flat-button color="accent" class="submit" i18n="@@submit">Submit</button>
            </div>
        </mat-tab>
        <mat-tab label="Destinations">
            <div class="form-container">
                <section>
                    <mat-form-field>
                        <mat-label i18n="@@channels">Destinations</mat-label>
                        <input matInput i18n-placeholder="@@channels">
                    </mat-form-field>
                </section>
                <button mat-flat-button color="accent" class="submit" i18n="@@submit">Submit</button>
            </div>
        </mat-tab>
    </mat-tab-group>
</mat-card>
