//@import "../../assets/scss/variables/colors/colors";
//@import "../../assets/scss/dark-theme/dark-theme";
//@import "../../assets/scss/light-theme/light-theme";

//mat-card {
//  &.main {
//    background-color: darken(mat-color($primary), 10%);
//
//  }
//}

mat-card-content {
  padding-top: 24px;
}

.form-container {

    section {
        margin-bottom: 25px;
        h4 {
            margin: 22px 0 5px;
        }
      mat-form-field {
        width: auto;
      }
    }

  .submit {
    width: auto;
  }

    .toggle {
      margin-bottom: 36px;
    }

    .toggle, mat-form-field {
        padding-top: 17px;
        label {
            padding-right: 10px;
        }
    }
}
