import { Component, EventEmitter, OnInit, Output } from '@angular/core';

import { ThemesService } from '../services/themes.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  @Output() readonly toggleThemeEvent: EventEmitter<string>;
  constructor(private themesService: ThemesService) {
    this.toggleThemeEvent = new EventEmitter<string>();
  }
  themeClass: string;
  dark: boolean;

  checked: boolean;

  ngOnInit() {}

  /**
   * Update the current theme according to the value of the event
   * @param event : the slider toggle event.
   */
  onChange(event) {
    // Update the class to change color
   this.themesService.updateTheme(event.checked);
    if (event.checked) {
      this.themeClass = 'dark-theme';

      this.themesService.setDarkTheme();
    } else {
      this.themeClass = 'light-theme';

      this.themesService.setLightTheme();
    }
  }

}
