import { Injectable } from '@angular/core';
import { OverlayContainer } from '@angular/cdk/overlay';
import { Subject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemesService {
  private themeObs$ = new Subject();
  classList: Array<string>;

  constructor(private overlayContainer: OverlayContainer) {
    this.classList = ['light-theme', 'dark-theme'];
  }

  setDarkTheme(): void {
    const overlayContainerClasses = this.overlayContainer.getContainerElement().classList;
    overlayContainerClasses.remove(...this.classList);
    overlayContainerClasses.add('dark-theme');
  }

  setLightTheme(): void {
    const overlayContainerClasses = this.overlayContainer.getContainerElement().classList;
    overlayContainerClasses.remove(...this.classList);
    overlayContainerClasses.add('light-theme');
  }

  /**
   * Get the class of the theme (as a boolean, true is dark theme, false is light theme)
   */
  getTheme(): Subject<{}> {
    return this.themeObs$;
  }

   /**
   * Update the class of the theme.
   * True is dark theme, False is light theme.
   */
  updateTheme(data: boolean): void {
      this.themeObs$.next(data);
  }
}
