import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import * as _ from 'lodash';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  horizontalPosition: MatSnackBarHorizontalPosition = 'center';
  verticalPosition: MatSnackBarVerticalPosition = 'top';
  private snackbarConfig: any;

  private progressBarState: boolean;

  constructor(private snackBar: MatSnackBar) {
    this.snackbarConfig = new MatSnackBarConfig();
    this.snackbarConfig.duration = 3000;
    this.snackbarConfig.horizontalPosition = this.horizontalPosition;
    this.snackbarConfig.verticalPosition = this.verticalPosition;
    this.progressBarState = false;
  }

  addNotificationInfo(message: string): void {
    if (message && message !== '') {
      this.snackbarConfig.panelClass = ['snackbar-info'];
      this.snackBar.open(message, null, this.snackbarConfig);
    }
  }

  /**
   * Show notification info. The message is from <code>data[key]</code>.
   * @param {string} key
   * @param data
   */
  addNotificationInfoFromData(key: string, data: any): void {
    const message = _.get(data, key, '');
    this.addNotificationInfo(message);
  }

  addNotificationSuccess(message: string): void {
    if (message && message !== '') {
      this.snackbarConfig.panelClass = ['snackbar-success'];
      this.snackBar.open(message, null, this.snackbarConfig);
    }
  }

  /**
   * Show notification success. The message is from <code>data[key]</code>.
   * @param {string} key
   * @param data
   */
  addNotificationSuccessFromData(key: string, data: any): void {
    const message = _.get(data, key, '');
    this.addNotificationSuccess(message);
  }

  addNotificationError(message: string): void {
    if (message && message !== '') {
      this.snackbarConfig.panelClass = ['snackbar-error'];
      this.snackBar.open(message, null, this.snackbarConfig);
    }
  }

  /**
   * Show notification error. The message is from <code>data[key]</code>.
   * @param {string} key
   * @param data
   */
  addNotificationErrorFromData(key: string, data: any): void {
    let message = _.get(data, key, '');
    this.addNotificationError(message);
  }

  addCustomNotification(component: any): void {
    this.snackBar.openFromComponent(component, this.snackbarConfig);
  }

  getProgressBarState(): boolean {
    return this.progressBarState;
  }

  showProgressBar(): void {
    this.progressBarState = true;
  }

  hideProgressBar(): void {
    this.progressBarState = false;
  }

}
