import { NgModule } from '@angular/core';
import { RouterModule, Routes, CanActivate } from '@angular/router';
import { MainComponent } from './layout/main/main.component';
import { AboutComponent } from './general/about/about.component';
import { HelpComponent } from './general/help/help.component';
import { ProfileComponent } from './profile/profile.component';
import { DashboardComponent } from './general/dashboard/dashboard.component';
import { DisabledFeatureComponent } from './general/disabled-feature/disabled-feature.component';
import { TableComponent } from './general/table/table.component';
import { ButtonsComponent } from './general/buttons/buttons.component';
import { LoginComponent } from './general/login/login.component';
import { NotConnectedComponent } from './layout/not-connected/not-connected.component';
import { ConsumptionComponent} from './general/consumption/consumption.component';
import { SessionComponent } from './general/session/session.component';
import { QualityComponent } from './general/quality/quality.component';
import { AuthGuardService } from './services/auth/auth-guard.service';
import { PersonalizedComponent} from './general/personalized/personalized.component';
import { DvrStatsComponent } from './general/dvr-stats/dvr-stats.component';
import { OvpStatsComponent } from './general/ovp-stats/ovp-stats.component';
import { PivotIframe1Component } from './general/pivot-iframe1/pivot-iframe1.component';
import { JwtModule } from '@auth0/angular-jwt';

const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    children: [
      {path: 'about', component: AboutComponent, canActivate: [AuthGuardService]},
      {path: 'help', component: HelpComponent, canActivate: [AuthGuardService]},
      //{path: 'profile', component: ProfileComponent, canActivate: [AuthGuardService]},
      {path: 'work', component: DashboardComponent, canActivate: [AuthGuardService]},
      {path: 'disabled-feature', component: DisabledFeatureComponent, canActivate: [AuthGuardService]},
      {path: 'table', component: TableComponent, canActivate: [AuthGuardService]},
      {path: 'buttons', component: ButtonsComponent, canActivate: [AuthGuardService]},
      {path: 'consumption', component: ConsumptionComponent, canActivate: [AuthGuardService]},
      {path: 'session', component: SessionComponent, canActivate: [AuthGuardService]},
      {path: 'quality', component: QualityComponent, canActivate: [AuthGuardService]},
      {path: 'personalized', component: PersonalizedComponent, canActivate: [AuthGuardService]},
      {path: 'dvr', component: DvrStatsComponent, canActivate: [AuthGuardService]},
      {path: 'ovp', component: OvpStatsComponent, canActivate: [AuthGuardService]},
      // {path: 'pivot', component: PivotIframe1Component, canActivate: [AuthGuardService]},
      {
        path: '',
        redirectTo: 'work',
        pathMatch: 'full'
      },
    ]
  },
  {
    path: '',
    component: NotConnectedComponent,
    children: [
      {path: 'login', component: LoginComponent},
        ]
  }
  // {
  //   path: 'admin',
  //   component: AdminComponent,
  //   canActivate: [RoleGuard],
  //   data: {
  //     expectedRole: 'admin'
  //   }
  // },
];

export function getToken() {
  return localStorage.getItem('token');
}

@NgModule({
  imports: [RouterModule.forRoot(routes, { relativeLinkResolution: 'legacy' }), JwtModule.forRoot({ config: {tokenGetter: getToken}})],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
