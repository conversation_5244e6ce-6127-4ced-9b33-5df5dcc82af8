import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { ProfileComponent } from '../../profile/profile.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        ProfileComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class ProfileModule { }
