import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { MaterialModule } from './material.module';

// import { ConfirmDialogComponent } from '../../components/common/confirm-dialog/confirm-dialog.component';
// import { FileUploadComponent } from '../../components/common/file-upload/file-upload.component';

@NgModule({
    declarations: [
    // FileUploadComponent,
    // ConfirmDialogComponent
    ],
    imports: [
        CommonModule,
        MaterialModule,
    ],
    exports: [
    // FileUploadComponent,
    // ConfirmDialogComponent
    ]
})

export class AppCommonModule {
}
