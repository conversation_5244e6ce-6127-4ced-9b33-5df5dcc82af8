import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';
import { SessionComponent } from '../../general/session/session.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        SessionComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    exports: [
        SessionComponent
    ],
    providers: []
})
// @ts-ignore
export class SessionModule { }
