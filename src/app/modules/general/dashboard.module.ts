import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { DashboardComponent } from '../../general/dashboard/dashboard.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PieChartsDashboardModule } from './pie-charts-dashboard.module';
import { MomentTimezonePickerModule } from 'moment-timezone-picker';
import { AlertModalModule } from './alert-modal.module';

@NgModule({
    declarations: [
        DashboardComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule,
        PieChartsDashboardModule,
        MomentTimezonePickerModule,
        FormsModule,
        AlertModalModule
    ],
    providers: []
})
export class DashboardModule { }
