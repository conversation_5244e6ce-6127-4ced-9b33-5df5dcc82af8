import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { TableComponent } from '../../general/table/table.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        TableComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class TableModule { }
