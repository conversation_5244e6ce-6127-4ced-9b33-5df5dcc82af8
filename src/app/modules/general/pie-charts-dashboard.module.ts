import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { PieChartsDashboardComponent } from '../../general/pie-charts-dashboard/pie-charts-dashboard.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        PieChartsDashboardComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    exports: [
        PieChartsDashboardComponent
    ],
    providers: []
})
// @ts-ignore
export class PieChartsDashboardModule { }
