import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { DisabledFeatureComponent } from '../../general/disabled-feature/disabled-feature.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        DisabledFeatureComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class DisabledFeatureModule { }
