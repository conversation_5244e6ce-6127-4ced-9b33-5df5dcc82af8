import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';
import { ReactiveFormsModule } from '@angular/forms';
import { QualityComponent } from '../../general/quality/quality.component';

@NgModule({
    declarations: [
        QualityComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    exports: [
        QualityComponent
    ],
    providers: []
})
// @ts-ignore
export class QualityModule { }
