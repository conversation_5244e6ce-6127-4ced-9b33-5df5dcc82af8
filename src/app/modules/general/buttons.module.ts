import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { ButtonsComponent } from '../../general/buttons/buttons.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        ButtonsComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class ButtonsModule { }
