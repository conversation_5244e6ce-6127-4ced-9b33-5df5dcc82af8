import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { ConsumptionComponent } from '../../general/consumption/consumption.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        ConsumptionComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class ConsumptionModule { }
