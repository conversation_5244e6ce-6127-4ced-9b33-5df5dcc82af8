import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppCommonModule } from '../shared/app-common.module';
import { MaterialModule } from '../shared/material.module';

import { AboutComponent } from '../../general/about/about.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [
        AboutComponent,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        AppCommonModule
    ],
    providers: []
})
export class AboutModule { }
