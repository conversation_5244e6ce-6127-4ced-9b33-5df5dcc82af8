import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../shared/material.module';
import { MainComponent } from '../../layout/main/main.component';
import { NotConnectedComponent } from '../../layout/not-connected/not-connected.component';
import { RouterModule } from '@angular/router';
import { NavbarComponent } from '../../layout/navbar/navbar.component';
import { SidebarComponent } from '../../layout/sidebar/sidebar.component';
import { FooterComponent } from '../../layout/footer/footer.component';
import { DefaultHeaderComponent } from '../../layout/default-header/default-header.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MomentTimezonePickerModule } from 'moment-timezone-picker';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';


@NgModule({
  declarations: [
    MainComponent,
    NotConnectedComponent,
    NavbarComponent,
    SidebarComponent,
    FooterComponent,
    DefaultHeaderComponent
  ],
    imports: [
        CommonModule,
        RouterModule,
        MaterialModule,
        MatDatepickerModule,
        MatNativeDateModule,
        ReactiveFormsModule,
        MomentTimezonePickerModule,
        FormsModule,
        NgxMaterialTimepickerModule
    ],
  exports: [
    MainComponent,
    NotConnectedComponent,
    NavbarComponent,
    SidebarComponent,
    FooterComponent,
    DefaultHeaderComponent
  ]
})

export class LayoutsModule {
}
