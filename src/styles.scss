@use '@angular/material' as mat;
@import 'assets/scss/variables/layout';

@include mat.core();

@import 'assets/scss/variables/common';

@import 'assets/scss/dark-theme/dark-theme.scss';
@import 'assets/scss/light-theme/light-theme.scss';

@import 'assets/scss/variables/navbar/button-logo-navbar';
@import 'assets/scss/variables/tooltip/tooltip';

@mixin custom-components-theme($theme) {
  @include button-logo-navbar($theme);
}

.light-theme {
  @include mat.all-component-themes($light-theme);
  @include custom-components-theme($light-theme);

  .app-logo {
    background-color: unset;

    h1 {
      color: mat.get-color-from-palette($advr-light-pal, default-contrast)
    }
  }

  .mat-menu-panel {
    background-color: mat.get-color-from-palette($advr-light-pal, 50);
  }
}

.dark-theme {
  @include mat.all-component-themes($dark-theme);
  @include custom-components-theme($dark-theme);

  .app-logo {
    background-color: unset;

    h1 {
      color: mat.get-color-from-palette($advr-dark-pal, 100)
    }
  }
}

$font-family: roboto, helvetica, Geneva, Verdana, sans-serif;
body {
  font-family: $font-family;
}

button {
  &.submit {
    margin: 15px 0;
  }
}

.tooltipBookmark {
  word-break: break-all !important;
  white-space: normal !important;
}
