<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="advanced_replay" datatype="html">
        <source>Product</source>
        <target>Produit</target>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="live_logging" datatype="html">
        <source>Application 1</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
      </trans-unit>
      <trans-unit id="live_events" datatype="html">
        <source>Application 2</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
      </trans-unit>
      <trans-unit id="live_multi_cam" datatype="html">
        <source>Application 3</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
      </trans-unit>
      <trans-unit id="my_profile" datatype="html">
        <source> My Profile </source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
      </trans-unit>
      <trans-unit id="logout" datatype="html">
        <source>Logout</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/navbar/navbar.component.html</context>
          <context context-type="linenumber">36</context>
        </context-group>
      </trans-unit>
      <trans-unit id="cut" datatype="html">
        <source>Feature 1</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/sidebar/sidebar.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="assets" datatype="html">
        <source>Feature 2</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/sidebar/sidebar.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="help" datatype="html">
        <source>Help</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/sidebar/sidebar.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="about" datatype="html">
        <source>About</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/layout/sidebar/sidebar.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="general" datatype="html">
        <source>General</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/profile/profile.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="Customize theme" datatype="html">
        <source>Customize theme</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/profile/profile.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
      </trans-unit>
      <trans-unit id="name" datatype="html">
        <source>Username</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/profile/profile.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
      </trans-unit>
      <trans-unit id="submit" datatype="html">
        <source>Submit</source>
        <context-group purpose="location">
          <context context-type="sourcefile">app/profile/profile.component.html</context>
          <context context-type="linenumber">43</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
