input.mat-input-element {
  margin-top: -.0625em;
}

.mat-input-element {
  &:disabled {
    color: $color-element-disabled;
  }
  caret-color: #3f51b5;
  &::placeholder {
    color: $input-color;
  }
  &::-moz-placeholder {
    color: $input-color;
  }
  &::-webkit-input-placeholder {
    color: $input-color;
  }
  &:-ms-input-placeholder {
    color: $input-color;
  }
}

.mat-form-field-invalid {
  .mat-input-element {
    caret-color: #f44336;
  }
}

.mat-dialog-container {
  box-shadow: 0 11px 15px -7px rgba(0, 0, 0, .2), 0 24px 38px 3px rgba(0, 0, 0, .14), 0 9px 46px 8px rgba(0, 0, 0, .12);
  background: $background-dialog-color;
  color: $dialog-color;
}

.mat-dialog-title {
  font: 500 20px/32px Roboto, "Helvetica Neue", sans-serif;
}

.mat-form-field {
  .mat-select.mat-select-invalid {
    .mat-select-arrow {
      color: $color_element_invalid;
    }
  }
  .mat-select.mat-select-disabled {
    .mat-select-arrow {
      color: $color_element_disabled;
    }
  }
}

.mat-form-field-label {
  transition-duration: 0.2s;
  color: $label-color;
}
.mat-form-field-appearance-legacy {
  .mat-form-field-underline {
    background-color: $input-underline-color;
  }
}
.mat-focused {
  .mat-form-field-ripple {
    background-color: $input-underline-color;
  }
  .mat-form-field-label {
    color: $label-color;
  }
}


