@use '@angular/material' as mat;
@import './colors/colors';
body{
  margin: 0;
  padding: 0;
}

.app-container {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

mat-sidenav-container {
  .app-container {
    margin: 20px 20px;

    mat-card {
      border-radius: 0;
    }
  }

  .app-container {
    &.full-width {
      margin: 0;

      mat-card {
        border-radius: 0;
      }
    }
  }
}

.header-container {
  padding: 5px 15px;
  margin: 5px 15px;
}

.app-sidenav-container {
  flex: 1;
}

.app-is-mobile .app-sidenav-container {
  flex: 1 0 auto;
}

.fill-space {
  flex: 1 1 auto;
}

.logo-icon {
  height: 40px;
  vertical-align: middle;
  width: auto;
  padding-left: 10px;
}

.user-logo {
  height: 29px;
  border-radius: 50%;
}

button.user-logo-btn.mat-icon-button {
  width: auto !important;
}

.top-nav-notifi{
  margin-right:40px !important;
}

.p-lr15 {
  padding: 15px 15px;
}

.p-r15 {
  padding-right: 15px;
}

.p-l15 {
  padding-left: 15px;
}

.m-lr15 {
  margin: 15px 15px;
}

.m-l15 {
  margin-left: 15px;
}

.m-r15 {
  margin-right: 15px;
}

.m-l5 {
  margin-left: 5px;
}

.m-r20 {
  margin-right: 20px;
}

.m-l20 {
  margin-left: 20px;
}

.no-mb {
  margin-bottom: 0 !important;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.t-r {
  text-align: right;
}

.p-15 {
  padding: 15px;
}

.responsive-img {
  width: 100%;
  height: auto;
}

.mat-progress-bar-buffer {
  background-color: whitesmoke !important;
}

.light-theme .mat-dialog-actions .mat-raised-button.mat-primary {
  color: #222222 !important;
}

.light-theme .mat-dialog-actions .mat-raised-button[disabled].mat-primary {
  color: dimgrey !important;
}

.dialog-container {
  width: 500px
}

.confirm-dialog-container {
  width: 300px
}

.snackbar-info {
  color: $advr-snackbar-info !important;
  background-color: mat.get-color-from-palette($advr-primary-dark-color, 300);
}

.snackbar-success {
  color: mat.get-contrast-color-from-palette($advr-accent-color, 100);
  background-color: mat.get-color-from-palette($advr-primary-dark-color, 400);
}

.snackbar-error {
  color: $advr-snackbar-success !important;
  background-color: mat.get-color-from-palette($advr-accent-color, 300);
}
