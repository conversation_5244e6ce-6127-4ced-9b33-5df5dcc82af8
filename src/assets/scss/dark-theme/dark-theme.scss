@use '@angular/material' as mat;
@import "../variables/colors/colors";

$dark-primary: mat.define-palette($advr-primary-dark-color);
$dark-accent: mat.define-palette($advr-accent-color);
$dark-theme: mat.define-dark-theme($advr-dark-pal, $advr-accent-pal);

.accent-color {
  color: mat.get-color-from-palette($dark-accent) !important;
}

.dark-theme .mat-list-base .mat-list-item.active {
  background-color: mat.get-color-from-palette($advr-dark-pal) !important;
  border-left: solid mat.get-color-from-palette($dark-accent)  5px !important;
}

.footer-dark {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: unset;
}

mat-label {
  color: $font-color;
}

.dark-theme .mat-card {
  color: $font-color;
}
