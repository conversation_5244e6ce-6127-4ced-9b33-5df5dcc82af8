@use '@angular/material' as mat;
@import "../variables/colors/colors";

$color-element-disabled: rgba(0, 0, 0, .38);
$color-element-invalid: #f44336;
$input-color: rgba(0, 0, 0, .42);
$background-dialog-color: #fff;
$dialog-color: rgba(0, 0, 0, .87);
$label-color: rgba(0, 0, 0, .6);
$input-underline-color: rgba(0, 0, 0, .87);
$font-family: Roboto, "Helvetica Neue", sans-serif;

@import '../variables/theme/custom';

$primary: mat.define-palette($advr-primary-color);
$accent: mat.define-palette($advr-accent-color);
$light-theme: mat.define-light-theme($advr-light-pal, $advr-accent-pal);

.accent-color {
  color: mat.get-color-from-palette($accent) !important;
}

.header-container {
  background-color: white;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: $advr-navbar-color;
}

/* Side menu */
.light-theme .mat-list-base .mat-list-item.active {
  border-left: solid mat.get-color-from-palette($accent)  5px !important;
}

.text {
  color: #737373;
}

mat-label {
  color: $font-color !important;
}

.light-theme .mat-card {
  color: $font-color !important;
}

.light-theme .mat-list-base .mat-list-item {
  color: $font-color !important;
}

